package com.flipkart.fintech.pinaka.client.v7;

import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;

import java.io.IOException;
import java.io.InputStream;

public interface PinakaClientV7 {
    PageActionResponse applyNow(LandingPageRequest landingPageRequest, String merchantUserId, String smUserId,
                                String merchantId, String requestId, String userAgent) throws PinakaClientException;

    PageActionResponse resumeJourney(LandingPageRequest landingPageRequest, String merchantUserId, String smUserId,
                                     String merchantId, String requestId, String userAgent) throws PinakaClientException;

    PageActionResponse submitForm(UserActionRequest submitRequest, String merchantUserId, String smUserId,
                                  String merchantId, String requestId) throws PinakaClientException;

    PageActionResponse resume(ResumePageRequest resumePageRequest, String merchantUserId, String smUserId,
                              String merchantId, String requestId, String userAgent) throws PinakaClientException;

    PageActionResponse submitUserAction(SubmitRequest submitRequest, String merchantUserId, String smUserId,
                                        String merchantId, String requestId) throws PinakaClientException;

    WebhooksResponse submitLenderEvent(AxisWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException;

    WebhooksResponse submitSandboxLenderEvent(SandboxWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException;

    KfsResponse getKfsDetails(KfsRequest kfsRequest, String merchantId, String requestId) throws PinakaClientException;

    StatusResponse getStatus(String applicationId, String requestId, String merchantUserId, String smUserId, String merchantId) throws PinakaClientException;

    SearchResponse getEmpSearchSug(String prefix, String merchantId) throws PinakaClientException;

    SearchResponse getEmployerSuggestions(String prefix, String merchantId) throws PinakaClientException;

    FetchBulkDataResponse fetchBulkData(FetchBulkDataRequest fetchBulkDataRequest, String merchantUserId,
                                        String smUserId, String merchantId, String requestId) throws PinakaClientException;

    SecurityKeyResponse getEncryptionKey(String requestId, String merchantId) throws PinakaClientException;

    PageActionResponse submit(UserActionRequest submitRequest, String merchantUserId, String smUserId,
                              String merchantId, String requestId, String userAgent) throws PinakaClientException;

    StateChangeResponse hasStateChanged(StateChangeRequest stateChangeRequest, String merchantUserId,
                                        String smUserId, String merchant, String requestId)
            throws PinakaClientException;

    FetchBulkDataResponseV2 fetchBulkDataV2(PageServiceRequest fetchBulkDataRequest, String merchantUserId,
                                            String smUserId, String merchant, String requestId)
            throws PinakaClientException, IOException;

    IfscSearchResponse searchIfsc(IfscSearchRequest ifscSearchRequest, String merchantId, String requestId)
            throws PinakaClientException;

    PageActionResponse dummyResponse(String url, String merchantId, String requestId) throws PinakaClientException;

    DocumentUploadResponse documentUpload(String merchantUserId, String smUserId, String merchantId,
                                          String documentType, String applicationId, String encryptedSymmetricKey,
                                          String publicKeyRefId, InputStream documentInputStream, String documentPassword,
                                          String requestId) throws PinakaClientException;


    PincodeDetailsResponse checkPincodeExistence(String pincode, String merchantId) throws PinakaClientException;

    void migrateApplications(MigrateApplicationsRequest migrateApplicationsRequest, String requestId) throws PinakaClientException;

    PageActionResponse refreshPageFetchBureau(String merchantUserId, String smUserId,String requestId, String userAgent,String merchantId) throws PinakaClientException;

    PageActionResponse pageFetchBureau(String merchantUserId, String smUserId,String requestId, String userAgent,String merchantId) throws PinakaClientException;

    PageActionResponse formSubmit(FormSubmitRequestV2 submitRequest, String requestId, String userAgent,String merchantId) throws PinakaClientException;

    void insertApplicationEvents(ApplicationEvent applicationEvent) throws PinakaClientException;

    PageActionResponse refreshSMPageFetchBureau(String smUserId,String requestId, String userAgent,String merchantId) throws PinakaClientException;
    PageActionResponse getSMPageFetchBureau(BureauDataRequestSm bureauDataRequestSm,String requestId, String userAgent,String merchantId) throws PinakaClientException;
    PageActionResponse putSMCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, String requestId, String userAgent, String merchantId) throws PinakaClientException ;
    PageActionResponse tryWithAnotherLender(ResumePageRequest resumePageRequest, String requestId, String userAgent, String merchantId) throws PinakaClientException;
    UIEventIngestionResponse uiEventIngester(UIEventIngestionRequest uiEventIngestionRequest, String requestId, String merchantId) throws PinakaClientException;
}
