package com.flipkart.fintech.pinaka.client.v3;

import com.flipkart.cri.alfred.api.model.datapoint.DataPointType;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.request.v3.PennyDropNameMatchRequest;
import com.flipkart.fintech.pinaka.api.request.v3.ApplicationCreateRequest;
import com.flipkart.fintech.pinaka.api.request.v3.FetchEddStatusRequest;
import com.flipkart.fintech.pinaka.api.response.ApplicationCreateResponse;
import com.flipkart.fintech.pinaka.api.response.ConsentDetailsResponse;
import com.flipkart.fintech.pinaka.api.response.PennyDropNameMatchResponse;
import com.flipkart.fintech.pinaka.api.response.UserKycStatusResponse;
import com.flipkart.fintech.pinaka.api.response.v3.EddStatusResponse;
import com.flipkart.fintech.pinaka.api.response.v3.FetchLenderIdentifierResponse;
import com.flipkart.fintech.pinaka.api.response.v3.FetchTerminalApplicationResponse;
import com.flipkart.fintech.pinaka.api.response.v3.discovery.FetchApplicationResponse;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.google.inject.ImplementedBy;

@ImplementedBy(PinakaClientV3Impl.class)
public interface PinakaClientV3 {


    UserKycStatusResponse fetchUserKycStatus(String merchant, FetchUserKycStatusRequest fetchUserKycStatusRequest) throws PinakaClientException;

    ConsentDetailsResponse fetchConsentDetails(String merchantId, String merchantUserId, String consentType) throws PinakaClientException;

    FetchLenderIdentifierResponse fetchLenderIdentifierResponse(String merchantUserId, Lender lender) throws PinakaClientException;

    FetchTerminalApplicationResponse fetchTerminalApplication(String merchantId, String merchantAccountId, ProductType productType) throws PinakaClientException;

    boolean checkDataPointFeasibility(DataPointType dataPointType, String accountId, String merchantId) throws PinakaClientException;

    EddStatusResponse fetchEddStatus(String merchant, FetchEddStatusRequest fetchEddStatusRequest) throws PinakaClientException;

    ApplicationCreateResponse createPennyDropApplication(String merchant, ApplicationCreateRequest applicationCreateRequest) throws PinakaClientException;

    PennyDropNameMatchResponse checkNameMatch(String merchantId, String merchantAccountId, PennyDropNameMatchRequest pennyDropNameMatchRequest) throws PinakaClientException;

    void updateKycValidity(String merchantId, String merchantAccountId, String applicationId) throws PinakaClientException;

    void processEbcCallback(EbcCallbackRequest ebcCallbackRequest) throws PinakaClientException;

    FetchApplicationResponse fetchActiveApplicationWithJourneyDetails(String merchantId, String merchantAccountId,
                                                                  ProductType productType) throws PinakaClientException;

    void processCfaCallback(CfaCallbackRequest callbackRequest) throws PinakaClientException;
}
