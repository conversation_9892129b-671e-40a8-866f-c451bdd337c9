package com.flipkart.fintech.pinaka.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import io.dropwizard.jackson.Jackson;

import java.util.TimeZone;

public class ObjectMapperFactory {
    public static final ObjectMapper OBJECT_MAPPER = Jackson.newObjectMapper();

    public ObjectMapperFactory() {
    }

    static {
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+530"));
    }
}
