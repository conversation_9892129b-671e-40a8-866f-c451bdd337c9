package com.flipkart.fintech.pinaka.client.async;

import com.flipkart.fintech.citadel.api.models.user_identity.AddUserIdentityRequest;
import com.flipkart.fintech.citadel.api.models.user_identity.DeleteUserIdentityRequest;
import com.flipkart.fintech.common.model.change_entity.ChangeEntityRequest;
import com.flipkart.fintech.pinaka.api.request.EbcCallbackRequest;
import com.flipkart.fintech.pinaka.api.request.v3.CreateLoanRequest;
import com.flipkart.fintech.pinaka.client.PinakaClientException;

public interface PinakaAsyncClient {
    void createLoan(CreateLoanRequest createLoanRequest,String trackingId, String accountId, String merchantId) throws PinakaClientException;
    void processEbcCallback(EbcCallbackRequest ebcCallbackRequest) throws PinakaClientException;
    void changeEntityEvent(ChangeEntityRequest changeEntityRequest) throws PinakaClientException;
    void createUserIdentity(AddUserIdentityRequest addUserIdentityRequest) throws PinakaClientException;
    void removeUserIdentity(DeleteUserIdentityRequest deleteUserIdentityRequest, String applicationId, String accountId) throws PinakaClientException;
}
