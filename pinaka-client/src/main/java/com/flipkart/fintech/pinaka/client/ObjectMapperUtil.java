package com.flipkart.fintech.pinaka.client;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.flipkart.fintech.pinaka.api.response.deserializer.AccordianWidgetDeserializer;
import com.flipkart.fintech.pinaka.api.response.deserializer.ValueDeserializer;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.AccordionWidgetData;
 
public class ObjectMapperUtil {
    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
        module.addDeserializer(Value.class, new ValueDeserializer());
        objectMapper.registerModule(module);
    }


    public static ObjectMapper get() {
        return objectMapper;
    }

}
