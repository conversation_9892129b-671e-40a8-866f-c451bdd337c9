package com.flipkart.fintech.pinaka.client.v6;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.request.v6.*;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.BureauActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;
import com.google.inject.Inject;

import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

import lombok.CustomLog;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.glassfish.jersey.media.multipart.MultiPartFeature;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static com.flipkart.fintech.pinaka.client.Utils.addTraceId;

@CustomLog
public class PinakaClientV6Impl implements PinakaClientV6 {
    private static final String ERROR_PINAKA = "error response from pinaka service";
    private final PinakaClientConfig clientConfig;
    private final ObjectMapper objectMapper = ObjectMapperUtil.get();
    private WebTarget webTarget;

    @Inject
    public PinakaClientV6Impl(PinakaClientConfig clientConfig, Client client) {
        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path(Constants.APP_CONTEXT_PATH).register(
                MultiPartFeature.class);
    }

    @Override
    public PageActionResponse applyNow(LandingPageRequest landingPageRequest, String merchantId, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.APPLY_NOW_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(landingPageRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public PageActionResponse resume(ResumePageRequest resumePageRequest, String merchantId, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.RESUME_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(resumePageRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public PageActionResponse submitUserAction(SubmitRequest submitRequest, String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_USER_ACTION).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(submitRequest)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public WebhooksResponse submitLenderEvent(AxisWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException {
        Response response = null;
        WebhooksResponse webhooksResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_LENDER_EVENT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            //Here we are adding temp merchant as lender events doesn't have merchant awareness // TODO later fix it
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(webhooksRequest)));
            if (response.getStatus() == 200) {
                webhooksResponse = objectMapper.readValue(response.readEntity(String.class), WebhooksResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return webhooksResponse;
    }

    @Override
    public WebhooksResponse submitSandboxLenderEvent(SandboxWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException {
        Response response = null;
        WebhooksResponse webhooksResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.SUBMIT_SANDBOX_LENDER_EVENT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            //Here we are adding temp merchant as lender events doesn't have merchant awareness // TODO later fix it
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(webhooksRequest)));
            if (response.getStatus() == 200) {
                webhooksResponse = objectMapper.readValue(response.readEntity(String.class), WebhooksResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return webhooksResponse;
    }


    @Override
    public KfsResponse getKfsDetails(KfsRequest kfsRequest, String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        KfsResponse kfsResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.KFS_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(kfsRequest)));
            if (response.getStatus() == 200) {
                kfsResponse = objectMapper.readValue(response.readEntity(String.class), KfsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return kfsResponse;
    }

    @Override
    public StatusResponse getStatus(String applicationId, String requestId, String accountId, String merchantId) throws PinakaClientException {
        Response response = null;
        StatusResponse statusResponse = null;
        try {
            String path = String.format(Constants.GET_STATUS_PATH, applicationId);
            Invocation.Builder invocationBuilder = webTarget.path(path).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_ACCOUNT_ID, accountId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                statusResponse = objectMapper.readValue(response.readEntity(String.class), StatusResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return statusResponse;
    }

    @Override
    public SearchResponse getEmpSearchSug(String prefix, String merchantId) throws PinakaClientException {
        Response response = null;
        SearchResponse searchResponse;
        try {
            String path = String.format(Constants.PL_EMP_SEARCH_PATH, prefix);
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam(Constants.PREFIX, prefix)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                searchResponse = objectMapper.readValue(response.readEntity(String.class), SearchResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return searchResponse;
    }

    private String generateAuthToken(String merchant) {
        StringBuilder keyReverse = new StringBuilder(merchant);
        String input = merchant + ":" + keyReverse.reverse();
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    public FetchBulkDataResponse fetchBulkData(FetchBulkDataRequest fetchBulkDataRequest, String merchantId, String requestId) throws PinakaClientException {
        Response response = null;
        FetchBulkDataResponse fetchBulkDataResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.FETCH_BULK_DATA_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(fetchBulkDataRequest)));
            if (response.getStatus() == 200) {
                fetchBulkDataResponse = objectMapper.readValue(response.readEntity(String.class), FetchBulkDataResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return fetchBulkDataResponse;

    }

    @Override
    public SecurityKeyResponse getEncryptionKey(String requestId, String merchantId) throws PinakaClientException {
        Response response = null;
        SecurityKeyResponse securityKeyResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PL_ENCRYPTION_KEY_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                securityKeyResponse = objectMapper.readValue(response.readEntity(String.class), SecurityKeyResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return securityKeyResponse;
    }

    @Override
    public PageActionResponse submit(UserActionRequest submitRequest, String requestId, String userAgent) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.USER_ACTION_SUBMIT).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_USER_AGENT, userAgent);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(submitRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public StateChangeResponse hasStateChanged(StateChangeRequest stateChangeRequest, String requestId)
            throws PinakaClientException {
        Response response = null;
        try {
            String path = Constants.PL_STATE_CHANGE_PATH;
            Invocation.Builder invocationBuilder = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(stateChangeRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), StateChangeResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public FetchBulkDataResponseV2 fetchBulkDataV2(PageServiceRequest pageServiceRequest, String requestId)
            throws PinakaClientException, IOException {
        Response response = null;
        FetchBulkDataResponseV2 fetchBulkDataResponseV2;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.FETCH_BULK_DATA_PATH_V2)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(pageServiceRequest)));
            if (response.getStatus() == 200) {
                fetchBulkDataResponseV2 = objectMapper.readValue(response.readEntity(String.class), FetchBulkDataResponseV2.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return fetchBulkDataResponseV2;
    }

    @Override
    public IfscSearchResponse searchIfsc(IfscSearchRequest ifscSearchRequest, String requestId)
            throws PinakaClientException {
        Response response = null;
        try {
            String path = Constants.PL_IFSC_SEARCH_PATH;
            Invocation.Builder invocationBuilder = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(ifscSearchRequest)));
            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), IfscSearchResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public PageActionResponse dummyResponse(String url, String requestId) throws PinakaClientException {
        Response response = null;
        PageActionResponse pageActionResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.DUMMY_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_REQUEST_ID, requestId);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(url)));
            if (response.getStatus() == 200) {
                pageActionResponse = objectMapper.readValue(response.readEntity(String.class), PageActionResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pageActionResponse;
    }

    @Override
    public DocumentUploadResponse documentUpload(String accountId, String documentType, String applicationId, String encryptedSymmetricKey,
                                                 String publicKeyRefId, InputStream documentInputStream, String documentPassword,
                                                 String requestId) throws PinakaClientException {
        Response response = null;
        try {
            String path = String.format(Constants.DOCUMENT_UPLOAD, documentType, applicationId);

            FormDataMultiPart multiPart = new FormDataMultiPart()
                    .field("document", documentInputStream, MediaType.MULTIPART_FORM_DATA_TYPE)
                    .field("encrypted_symmetric_key", encryptedSymmetricKey)
                    .field("public_key_ref_id", publicKeyRefId)
                    .field("document_password", documentPassword);

            response = webTarget.path(path)
                    .request()
                    .header(Constants.X_ACCOUNT_ID, accountId)
                    .header(Constants.X_REQUEST_ID, requestId)
                    .header(Constants.X_MERCHANT_ID, clientConfig.getMerchant())
                    .header(Constants.X_CLIENT_ID, clientConfig.getClient())
                    .header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()))
                    .post(Entity.entity(multiPart, MediaType.MULTIPART_FORM_DATA_TYPE));

            if (response.getStatus() == 200) {
                return objectMapper.readValue(response.readEntity(String.class), DocumentUploadResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public UserOfferDataResponse getUserOfferData(String applicationId, String smUserId, String lender) throws PinakaClientException {
        String path = String.format(Constants.USER_OFFER_DATA_PATH, applicationId);
        Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("smUserId", smUserId).queryParam("lender", lender).request(MediaType.APPLICATION_JSON_TYPE);
        try(Response response = invocationBuilder.get()){
            if (response.getStatusInfo().getFamily() == Response.Status.Family.SUCCESSFUL) {
                return response.readEntity(UserOfferDataResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }
        catch (Exception e) {
            throw new PinakaClientException(e);
        }
    }


    @Override
    public PincodeDetailsResponse checkPincodeExistence(String pincode, String merchantId) throws PinakaClientException {
        Response response = null;
        PincodeDetailsResponse pincodeDetailsResponse = null;
        String path = Constants.CHECK_PINCODE_VALIDITY;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("pincode", pincode).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                pincodeDetailsResponse = response.readEntity(PincodeDetailsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return pincodeDetailsResponse;
    }


}