package com.flipkart.fintech.pinaka.client;

import com.flipkart.fintech.pinaka.client.v2.PinakaClientV2;
import com.flipkart.fintech.pinaka.client.v2.PinakaClientV2Impl;
import com.flipkart.fintech.pinaka.client.v4.PinakaClientV4;
import com.flipkart.fintech.pinaka.client.v4.PinakaClientV4Impl;
import com.flipkart.fintech.pinaka.client.v6.PinakaClientV6;
import com.flipkart.fintech.pinaka.client.v6.PinakaClientV6Impl;
import com.flipkart.fintech.pinaka.client.v7.PinakaClientV7;
import com.flipkart.fintech.pinaka.client.v7.PinakaClientV7Impl;
import com.google.inject.AbstractModule;

/**
 * Created by sujee<PERSON><PERSON>.r on 04/09/17.
 */
public class PinakaClientModule extends AbstractModule{

    @Override
    protected void configure() {
        bind(PinakaClient.class).to(PinakaClientImpl.class);
        bind(PinakaClientV2.class).to(PinakaClientV2Impl.class);
        bind(PinakaClientV4.class).to(PinakaClientV4Impl.class);
        bind(PinakaClientV6.class).to(PinakaClientV6Impl.class);
        bind(PinakaClientV7.class).to(PinakaClientV7Impl.class);
    }

}
