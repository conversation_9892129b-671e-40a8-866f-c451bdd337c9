package com.flipkart.fintech.pinaka.client.async;

import com.flipkart.affordability.config.PinakaAsyncClientConfig;
import com.flipkart.fintech.citadel.api.models.user_identity.AddUserIdentityRequest;
import com.flipkart.fintech.citadel.api.models.user_identity.DeleteUserIdentityRequest;
import com.flipkart.fintech.common.model.change_entity.ChangeEntityRequest;
import com.flipkart.fintech.logger.utils.LoggerUtils;
import com.flipkart.fintech.pinaka.api.request.EbcCallbackRequest;
import com.flipkart.fintech.pinaka.api.request.v3.CreateLoanRequest;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.ObjectMapperFactory;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.winterfell.client.WinterfellClientConfig;
import com.flipkart.restbus.client.core.MessageSender;
import com.flipkart.restbus.client.entity.Message;
import com.google.inject.Inject;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.core.HttpHeaders;
import java.io.IOException;
import java.util.Base64;
import java.util.Date;
import java.util.UUID;

import static com.flipkart.fintech.common.enums.Tenant.FK_CONSUMER_CREDIT;
import static com.flipkart.fintech.pinaka.client.Constants.*;

public class PinakaAsyncClientImpl implements PinakaAsyncClient {

    private final PinakaAsyncClientConfig pinakaClientConfig;
    private final MessageSender           messageSender;
    private static final String           EXCHANGE_TYPE = "queue";
    private static final String           FK_MERCHANT = "flipkart";
    private final WinterfellClientConfig  winterfellClientConfig;
    private static final String APPLICATION_JSON = "application/json";

    @Inject
    public PinakaAsyncClientImpl(PinakaAsyncClientConfig clientConfiguration, MessageSender messageSender,
            WinterfellClientConfig winterfellClientConfig) {
        this.pinakaClientConfig = clientConfiguration;
        this.messageSender = messageSender;
        this.winterfellClientConfig = winterfellClientConfig;
    }

    @Override
    public void createLoan(CreateLoanRequest createLoanRequest, String trackingId, String accountId, String merchantId) throws PinakaClientException {
        try {
            Message message = new Message();
            message.setExchangeName(this.pinakaClientConfig.getExchangeName());
            message.setHttpUri(this.pinakaClientConfig.getUrl() + Constants.APP_CONTEXT_PATH + String.format(Constants.CREATE_LOAN_PATH, trackingId));
            message.setHttpMethod("post");
            message.setExchangeType(EXCHANGE_TYPE);
            message.setPayload(ObjectMapperFactory.OBJECT_MAPPER.writeValueAsString(createLoanRequest));
            message.setCreatedAt(new Date());
            message.addCustomHeaders(X_MERCHANT_ID, merchantId);
            message.addCustomHeaders(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            message.addCustomHeaders(Constants.X_ACCOUNT_ID, accountId);
            message.addCustomHeaders(X_CLIENT_ID, Constants.PINAKA_CLIENT_ID);
            message.addCustomHeaders(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON);
            message.addCustomHeaders(X_EXTERNAL_USER_ID, accountId);
            addTraceId(message);
            message.setGroupId(accountId);
            this.messageSender.send(message);
        } catch (IOException var7) {
            throw new PinakaClientException("Exception while pushing create loan message to outboundmessages " + var7.getMessage());
        }
    }

    @Override
    public void processEbcCallback(EbcCallbackRequest ebcCallbackRequest) throws PinakaClientException {
        try {
            Message message = new Message();
            message.setExchangeName(this.pinakaClientConfig.getEbcExchangeName());
            message.setExchangeType("topic");
            message.setPayload(ObjectMapperFactory.OBJECT_MAPPER.writeValueAsString(ebcCallbackRequest));
            message.setCreatedAt(new Date());
            message.addCustomHeaders(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON);
            message.setReplyToHttpMethod(Constants.HTTP_METHOD_POST);
            message.addCustomHeaders(Constants.HEADER_EVENT_NAME, Constants.EBC_STATUS_UPDATE_EVENT_NAME);
            message.addCustomHeaders(X_EXTERNAL_USER_ID, ebcCallbackRequest.getFkAccountId());
            addTraceId(message);
            message.setGroupId(ebcCallbackRequest.getFkAccountId());
            this.messageSender.send(message);
        } catch (IOException e) {
            throw new PinakaClientException("Exception while pushing ebc callback message to outbound messages " + e.getMessage());
        }
    }

    @Override
    public void changeEntityEvent(ChangeEntityRequest changeEntityRequest) throws PinakaClientException {
        try {
            Message message = new Message();
            message.setExchangeName(this.pinakaClientConfig.getChangeEntityEventTopic());
            message.setHttpMethod("post");
            message.setExchangeType("topic");
            message.setPayload(ObjectMapperFactory.OBJECT_MAPPER.writeValueAsString(changeEntityRequest));
            message.setCreatedAt(new Date());
            message.addCustomHeaders(X_CLIENT_ID, Constants.PINAKA_CLIENT_ID);
            message.addCustomHeaders(Constants.X_ENTITY_TYPE, changeEntityRequest.getEntityType());
            message.addCustomHeaders(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON);
            message.addCustomHeaders(X_EXTERNAL_USER_ID, changeEntityRequest.getUserId());
            addTraceId(message);
            message.setGroupId(changeEntityRequest.getEntityId());
            this.messageSender.send(message);
        } catch (IOException var7) {
            throw new PinakaClientException("Exception while pushing create loan message to outboundmessages " + var7.getMessage());
        }
    }


    @Override
    public void createUserIdentity(AddUserIdentityRequest addUserIdentityRequest) throws PinakaClientException {
        try {
            Message message = new Message();
            message.setExchangeName(this.pinakaClientConfig.getAdvanzDedupeExchangeName());
            message.setExchangeType(EXCHANGE_TYPE);
            message.setHttpUri(this.winterfellClientConfig.getHost() + Constants.WINTERFELL_ADD_USER_IDENTITY_PATH);
            message.setHttpMethod(Constants.HTTP_METHOD_POST);
            message.setPayload(ObjectMapperFactory.OBJECT_MAPPER.writeValueAsString(addUserIdentityRequest));
            message.setCreatedAt(new Date());
            message.addCustomHeaders(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON);
            message.addCustomHeaders(X_TENANT_ID, FK_CONSUMER_CREDIT);
            message.addCustomHeaders(X_MERCHANT_ID, FK_MERCHANT);
            message.addCustomHeaders(X_CLIENT_ID, Constants.PINAKA_CLIENT_ID);
            message.addCustomHeaders(X_REQUEST_ID, UUID.randomUUID().toString());
            message.addCustomHeaders(X_TRACE_ID, UUID.randomUUID().toString());
            message.addCustomHeaders(X_EXTERNAL_USER_ID, addUserIdentityRequest.getAccountId());
            addTraceId(message);
            message.setGroupId(addUserIdentityRequest.getApplicationId());
            this.messageSender.send(message);
        } catch (IOException e) {
            throw new PinakaClientException("Exception while pushing user identity create request to outbound messages " + e.getMessage());
        }
    }

    @Override
    public void removeUserIdentity(DeleteUserIdentityRequest deleteUserIdentityRequest, String applicationId, String accountId) throws PinakaClientException {
        try {
            Message message = new Message();
            message.setExchangeName(this.pinakaClientConfig.getAdvanzDedupeExchangeName());
            message.setExchangeType(EXCHANGE_TYPE);
            message.setHttpUri(this.winterfellClientConfig.getHost() + Constants.WINTERFELL_DELETE_USER_IDENTITY_PATH);
            message.setHttpMethod(Constants.HTTP_METHOD_POST);
            message.setPayload(ObjectMapperFactory.OBJECT_MAPPER.writeValueAsString(deleteUserIdentityRequest));
            message.setCreatedAt(new Date());
            message.addCustomHeaders(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON);
            message.addCustomHeaders(X_TENANT_ID, FK_CONSUMER_CREDIT);
            message.addCustomHeaders(X_MERCHANT_ID, FK_MERCHANT);
            message.addCustomHeaders(X_CLIENT_ID, Constants.PINAKA_CLIENT_ID);
            message.addCustomHeaders(X_REQUEST_ID, UUID.randomUUID().toString());
            message.addCustomHeaders(X_TRACE_ID, UUID.randomUUID().toString());
            message.addCustomHeaders(X_EXTERNAL_USER_ID, accountId);
            addTraceId(message);
            message.setGroupId(applicationId);
            this.messageSender.send(message);
        } catch (IOException e) {
            throw new PinakaClientException("Exception while pushing user identity delete request to outbound messages " + e.getMessage());
        }
    }

    private String generateAuthToken(String merchant) {
        StringBuilder keyReverse = new StringBuilder(merchant);
        String input = merchant+":"+keyReverse.reverse();
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    private void addTraceId(Message message) {
        String traceId = LoggerUtils.getTraceId();
        if(StringUtils.isNotBlank(traceId)) {
            message.addCustomHeaders(X_TRACE_ID, traceId);
        }
    }
}
