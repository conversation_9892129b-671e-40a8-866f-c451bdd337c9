package com.flipkart.fintech.pinaka.client;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 05/09/17.
 */
public class Constants {
    private Constants() {

    }

    public static final String APP_CONTEXT_PATH         = "/pinaka";
    public static final String X_MERCHANT_ID            = "X-Merchant-Id";
    public static final String X_CLIENT_ID              = "X-Client-Id";
    public static final String X_TRACE_ID               = "X-Trace-ID";
    public static final String X_TENANT_ID              = "X-TENANT-ID";
    public static final String X_ENTITY_TYPE            = "X_ENTITY_TYPE";
    public static final String X_AUTHORIZATION          = "X-Authorization";
    public static final String X_ACCOUNT_ID             = "X-Account-Id";
    public static final String X_MERCHANT_ACCOUNT_ID    = "X-Merchant-Account-Id";
    public static final String X_MERCHANT_USER_ID    = "X-Merchant-User-Id";
    public static final String X_SM_USER_ID    = "X-SM-User-Id";
    public static final String X_REQUEST_ID             = "X-Request-Id";
    public static final String X_PRODUCT                = "X-PRODUCT";
    public static final String REQUEST_ID               = "request_id";
    public static final String PINAKA_CLIENT_ID         = "pinaka";
    public static final String LENDER_USER_REF_ID_PARAM = "lender_user_ref_id";
    public static final String X_EXTERNAL_USER_ID       = "X-External-User-Id";
    public static final String X_HTTP_METHOD            = "X-Http-Method";
    public static final String X_USER_AGENT = "X-User-Agent";

    public static final String CREATE_APPLICATION_PATH        = "/applications/create";
    public static final String CREATE_APPLICATION_V2_PATH     = "/2/applications/create";
    public static final String CREATE_AUTOPAY_MANDATE_V1_PATH = "/1/mandate/create";
    public static final String UPDATE_APPLICATION_PATH        = "/applications/%s/update";
    public static final String UPDATE_APPLICATION_V2_PATH     = "/2/applications/%s/update";
    public static final String APPLICATION_CONFIRMATION_PATH  = "/2/applications/confirmation";

    public static final String CREATE_LOAN_PATH                     = "/3/applications/%s/create-loan";
    public static final String FETCH_USER_KYC_STATUS_PATH           = "/3/applications/fetch-user-kyc-status";
    public static final String FETCH_ENHANCED_DUE_DILIGENCE_STATUS  = "/3/applications/fetch-edd-status";
    public static final String CREATE_PENNY_DROP_APPLICATION_PATH   = "/3/penny-drop/create-application";
    public static final String FETCH_LENDER_IDENTIFIER              = "/3/applications/%s/fetch-lender-identifier";
    public static final String FETCH_ALT_DATA_DETAILS               = "/applications/product/%s/fetch-alt-data-details";
    public static final String CANCEL_EVENT_PATH                    = "/applications/%s/flux/%s/cancel";
    public static final String POST_PROCESSING_TASK_PATH            = "/applications/%s/post_processing_task";
    public static final String POST_PROCESSING_TASK_V2_PATH         = "/2/applications/%s/post_processing_task";
    public static final String FETCH_ACTIVE_APPLICATION_V2_PATH     = "/2/applications/%s/%s/fetch_application";
    public static final String KYC_SCHEDULER_PATH                   = "/5/applications/kyc-scheduler/schedule";
    public static final String KYC_SCHEDULER_UPDATE_PATH            = "/5/applications/kyc-scheduler/update";
    public static final String VKYC_UPDATE_PATH                     = "/5/applications/vkyc/update";

    public static final String FETCH_BNPL_APPLICATION_DATA_PATH               = "/applications/bnpl/%s/fetch_parameters";
    public static final String UPDATE_BNPL_APPLICATION_DATA_PATH              = "/applications/bnpl/%s/update";
    public static final String INSIGHTS_GENERATED_DATA_PATH                   = "/applications/bnpl/%s/insights";
    public static final String FETCH_ALL_APPLICATIONS_PATH                    = "/applications/%s/fetch_all_applications";
    public static final String GENERATE_ACCESS_CODE_PATH                      = "/applications/%s/generate_account_access_code";
    public static final String FETCH_FORM_FIELDS_PATH                         = "/applications/%s/fetch_form_fields";
    public static final String CREATE_BORROWER                                = "/1/borrower/create";
    public static final String WHITELIST_CREATE                               = "/1/whitelist/create";
    public static final String FETCH_WHITELIST                                = "/1/whitelist/%s/product/%s";
    public static final String CHECK_DATAPOINT_FEASIBILITY_PATH               = "/3/applications/%s/check-feasibility";
    public static final String PENNY_DROP_NAME_MATCH_PATH                     = "/3/penny-drop/name-match";
    public static final String UPDATE_KYC_VALIDITY_PATH                       = "/3/applications/%s/update-kyc-validity";
    public static final String FETCH_ACTIVE_APPLICATION_WITH_EBC_DETAILS_PATH = "/3/application-discovery/%s/%s/fetch_application";
    public static final String CHECK_PINCODE_VALIDITY             = "/6/pincode/existence";
    public static final String PL_UI_EVENT_INGESTER               = "/1/ui/batch-event";


    public static final String FETCH_LENDER_ACCOUNT_DETAILS_PATH = "/1/borrower/%s/%s/lender_account_details";
    public static final String FETCH_LENDER_CONFIG_PATH          = "/1/borrower/%s/%s/fetch_lender_config";
    public static final String FETCH_AUTOPAY_DETAILS_PATH        = "/applications/fetch_autopay_details";
    public static final String FETCH_BORROWER_DETAILS_PATH       = "/1/borrower";

    public static final String SEND_COMMUNICATION_PATH  = "/communications/%s/sendAsyncComm";
    public static final String SETUP_AUTOPAY_APPLICABLE = "setup_autopay_applicable";
    public static final String SETUP_AUTOPAY_MANDATORY  = "setup_autopay_mandatory";
    public static final String RETRY                    = "retry";
    public static final String ERROR_MESSAGE            = "error_message";
    public static final String DEFAULT                  = "DEFAULT";
    public static final String WORKFLOW                 = "workflow";
    public static final String PREFIX                   = "prefix";


    public static final String FETCH_CONSENT_DETAILS_PATH                          = "/1/borrower/%s/consent/%s/fetch";
    public static final String FETCH_TERMINAL_APPLICATION_PATH                     = "/3/applications/%s/fetch-terminal-application";
    public static final String FAILURE                                             = "FAILURE";
    public static final String SUCCESS                                             = "SUCCESS";
    public static final String STATUS                                              = "status";
    public static final String CHECK_ELIGIBILITY_WITH_LENDER_RESPONSE_STATUS       = "check_eligibility_with_lender_response_status";
    public static final String CHECK_ELIGIBILITY_WITH_UNDERWRITING_RESPONSE_STATUS = "check_eligibility_with_underwriting_response_status";
    public static final String PRE_VALIDATIONS_STATUS                              = "validation_status";
    public static final String ALT_DATA_TYPE                                       = "alt_data_type";

    public static final String HEADER_EVENT_NAME             = "X_EVENT_NAME";
    public static final String EBC_STATUS_UPDATE_EVENT_NAME  = "ebc_status_updated";
    public static final String HTTP_METHOD_POST              = "post";
    public static final String EBC_CALLBACK_PATH_V3          = "/1/ebc/callback";
    public static final String FETCH_PINAKA_APPLICATION_PATH = "/4/applications/%s/details";
    public static final String CFA_CALLBACK_API_PATH         = "/1/cfa/callback";

    public static final String WINTERFELL_ADD_USER_IDENTITY_PATH    = "/1/identity/add";
    public static final String WINTERFELL_DELETE_USER_IDENTITY_PATH = "/1/identity/remove";

    public static final String PINCODE_URL         = "/1/pincode/";
    public static final String PINCODE_CREATE_PATH = String.join("", PINCODE_URL, "create");
    public static final String PINCODE_FETCH_PATH  = String.join("", PINCODE_URL, "fetch");
    public static final String PINCODE_REMOVE_PATH = String.join("", PINCODE_URL, "remove");
    public static final String CREATE_AND_SUBMIT_APPLICATION = "/uat/createAndSubmitApplication";
    public static final String LENDER_CONFIRMATION = "/uat/lenderConfirmation";
    public static final String APPLY_NOW_PATH = "/6/pl/apply-now";
    public static final String RESUME_JOURNEY_PATH = "/1/lg/resume";
    public static final String SUBMIT_PATH = "/1/lg/submit";
    public static final String RESUME_PATH = "/6/pl/resume";
    public static final String TRY_ANOTHER_LENDER_PATH = "/1/pl/try-another-lender";
    public static final String REJECTED_USER_URL = "/ams/1/rejected-v2";
    public static final String SUBMIT_USER_ACTION = "/6/pl/submit-event";

    public static final String USER_ACTION_SUBMIT = "/6/pl/user-action/submit";
    public static final String ACTION_SUBMIT = "/6/submit";
    public static final String PAGE_FETCH_FOR_BUREAU = "/6/check-score/existing-score";
    public static final String REFRESH_PAGE_FETCH_FOR_BUREAU = "/6/check-score/refresh-score";
    public static final String SUBMIT_LENDER_EVENT = "/6/pl/submit-lender-event";
    public static final String SUBMIT_SANDBOX_LENDER_EVENT = "/6/pl/submit-sandbox-lender-event";
    public static final String KFS_PATH = "/6/pl/fetch-kfs";
    public static final String GET_STATUS_PATH = "/6/pl/fetch-status/application-id/%s";
    public static final String FETCH_BULK_DATA_PATH = "/6/pl/fetch-bulk-data";
    public static final String PL_EMP_SEARCH_PATH = "/6/pl/get-pl-employer-suggestions";
    public static final String PL_EMP_SEARCH_PATH_V2 = "/6/pl/employer-suggestions";
    public static final String FETCH_BULK_DATA_PATH_V2 = "/6/pl/fetch-bulk-data-v2";
    public static final String PL_ENCRYPTION_KEY_PATH = "/6/pl/security/key";

    public static final String PL_STATE_CHANGE_PATH = "/6/pl/has-state-changed";
    public static final String PL_IFSC_SEARCH_PATH = "/6/pl/ifsc-search";
    public static final String MIGRATE_APPLICATIONS_PATH = "/6/pl/migrate";
    public static final String APPLICATION_EVENTS_PATH = "/6/pl/application-events-yak";

    public static final String DOCUMENT_UPLOAD = "/6/document/%s/application/%s";
    public static final String DUMMY_PATH = "/6/pl/dummy";
    public static final String VARADHI_EVENT_PUSH = "topics/%s/messages";
    public static final String X_RESTBUS_MESSAGE_ID ="X_RESTBUS_MESSAGE_ID";
    public static final String X_RESTBUS_GROUP_ID = "X_RESTBUS_GROUP_ID";

    public static final String SM_REFRESH_BUREAU_PATH = "/6/check-score/sm-refresh-score";
    public static final String SM_FETCH_BUREAU_PATH = "/6/check-score/sm-fetch-score";
    public static final String SM_CS_CROSS_MERCHANT_CONSENT_PATH = "/6/check-score/sm-cross-merchant-consent";

    public static final String CBC_GENERATE_CUSTOMER_HANDSHAKE_TOKENS_PATH = "/cbc/customer-handshake";

    public static final String USER_OFFER_DATA_PATH = "/6/offer-data/%s";

    public static final int CBC_CUSTOMER_HANDSHAKE_PINAKA_CONNECTION_TIMEOUT = 60000;
    public static final int CBC_CUSTOMER_HANDSHAKE_PINAKA_READ_TIMEOUT = 60000;
}
