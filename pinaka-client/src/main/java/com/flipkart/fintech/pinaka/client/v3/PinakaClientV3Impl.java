package com.flipkart.fintech.pinaka.client.v3;

import com.flipkart.cri.alfred.api.model.datapoint.DataPointType;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.request.v3.PennyDropNameMatchRequest;
import com.flipkart.fintech.pinaka.api.request.v3.ApplicationCreateRequest;
import com.flipkart.fintech.pinaka.api.request.v3.FetchEddStatusRequest;
import com.flipkart.fintech.pinaka.api.response.ApplicationCreateResponse;
import com.flipkart.fintech.pinaka.api.response.ConsentDetailsResponse;
import com.flipkart.fintech.pinaka.api.response.PennyDropNameMatchResponse;
import com.flipkart.fintech.pinaka.api.response.UserKycStatusResponse;
import com.flipkart.fintech.pinaka.api.response.v3.EddStatusResponse;
import com.flipkart.fintech.pinaka.api.response.v3.FetchLenderIdentifierResponse;
import com.flipkart.fintech.pinaka.api.response.v3.FetchTerminalApplicationResponse;
import com.flipkart.fintech.pinaka.api.response.v3.discovery.FetchApplicationResponse;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.google.inject.Inject;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Base64;

import static com.flipkart.fintech.pinaka.client.Constants.*;
import static com.flipkart.fintech.pinaka.client.Utils.addTraceId;

public class PinakaClientV3Impl implements PinakaClientV3 {

    private final PinakaClientConfig clientConfig;
    private WebTarget webTarget;
    private static final String ERROR_PINAKA = "error response from pinaka service";

    @Inject
    public PinakaClientV3Impl(PinakaClientConfig clientConfig, Client client){

        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path(Constants.APP_CONTEXT_PATH);
    }


    @Override
    public UserKycStatusResponse fetchUserKycStatus(String merchant, FetchUserKycStatusRequest fetchUserKycStatusRequest) throws PinakaClientException {
        Response response = null;
        UserKycStatusResponse userKycStatusResponse;

        try {
            Invocation.Builder invocationBuilder = webTarget.path(FETCH_USER_KYC_STATUS_PATH).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(fetchUserKycStatusRequest));
            if(response.getStatus() == 200){
                userKycStatusResponse = response.readEntity(UserKycStatusResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA+ response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return userKycStatusResponse;
    }

    @Override
    public ConsentDetailsResponse fetchConsentDetails(String merchantId, String merchantUserId, String consentType) throws PinakaClientException {
        Response response = null;
        ConsentDetailsResponse consentDetailsResponse;
        String path = String.format(Constants.FETCH_CONSENT_DETAILS_PATH, merchantUserId, consentType);

        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_ACCOUNT_ID, merchantUserId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if(response.getStatus() == 200){
                consentDetailsResponse = response.readEntity(ConsentDetailsResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return consentDetailsResponse;
    }

    @Override
    public FetchLenderIdentifierResponse fetchLenderIdentifierResponse(String merchantUserId, Lender lender) throws PinakaClientException {
        Response response = null;
        FetchLenderIdentifierResponse fetchLenderIdentifierResponse;
        String path = String.format(Constants.FETCH_LENDER_IDENTIFIER, lender);

        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_MERCHANT_ACCOUNT_ID, merchantUserId);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if(response.getStatus() == 200){
                fetchLenderIdentifierResponse = response.readEntity(FetchLenderIdentifierResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return fetchLenderIdentifierResponse;
    }

    @Override
    public FetchTerminalApplicationResponse fetchTerminalApplication(String merchantId, String merchantAccountId, ProductType productType) throws PinakaClientException {
        Response response = null;
        FetchTerminalApplicationResponse fetchTerminalApplicationResponse;
        String path = String.format(Constants.FETCH_TERMINAL_APPLICATION_PATH, productType);

        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_MERCHANT_ACCOUNT_ID, merchantAccountId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_REQUEST_ID, Constants.REQUEST_ID);
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if(response.getStatus() == 200){
                fetchTerminalApplicationResponse = response.readEntity(FetchTerminalApplicationResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return fetchTerminalApplicationResponse;
    }

    @Override
    public boolean checkDataPointFeasibility(DataPointType dataPointType, String accountId, String merchantId) throws PinakaClientException {
        Response response = null;
        Boolean feasibility;
        String path = String.format(Constants.CHECK_DATAPOINT_FEASIBILITY_PATH, dataPointType.name());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_MERCHANT_ACCOUNT_ID, accountId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if(response.getStatus() == 200){
                feasibility = response.readEntity(Boolean.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return feasibility;
    }

    @Override
    public EddStatusResponse fetchEddStatus(String merchant, FetchEddStatusRequest fetchEddStatusRequest) throws PinakaClientException {
        Response response = null;
        EddStatusResponse eddStatusResponse;

        try {
            Invocation.Builder invocationBuilder = webTarget.path(FETCH_ENHANCED_DUE_DILIGENCE_STATUS).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(fetchEddStatusRequest));
            if(response.getStatus() == 200){
                eddStatusResponse = response.readEntity(EddStatusResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return eddStatusResponse;
    }

    @Override
    public ApplicationCreateResponse createPennyDropApplication(String merchant, ApplicationCreateRequest applicationCreateRequest) throws PinakaClientException {
        Response response = null;
        ApplicationCreateResponse applicationCreateResponse;

        try {
            Invocation.Builder invocationBuilder = webTarget.path(CREATE_PENNY_DROP_APPLICATION_PATH).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(applicationCreateRequest));
            if(response.getStatus() == 200 || response.getStatus() == 201){
                applicationCreateResponse = response.readEntity(ApplicationCreateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){
                response.close();
            }
        }
        return applicationCreateResponse;
    }

    @Override
    public PennyDropNameMatchResponse checkNameMatch(String merchantId, String merchantAccountId, PennyDropNameMatchRequest pennyDropNameMatchRequest) throws PinakaClientException {
        Response response = null;
        PennyDropNameMatchResponse pennyDropNameMatchResponse;

        try {
            Invocation.Builder invocationBuilder = webTarget.path(PENNY_DROP_NAME_MATCH_PATH).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_MERCHANT_ACCOUNT_ID, merchantAccountId);
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(pennyDropNameMatchRequest));
            if(response.getStatus() == 200) {
                pennyDropNameMatchResponse = response.readEntity(PennyDropNameMatchResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return pennyDropNameMatchResponse;
    }

    @Override
    public void updateKycValidity(String merchantId, String merchantAccountId, String applicationId) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(String.format(UPDATE_KYC_VALIDITY_PATH, applicationId)).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_MERCHANT_ACCOUNT_ID, merchantAccountId);
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(null);
            if(response.getStatus() != 200) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
    }

    @Override
    public void processEbcCallback(EbcCallbackRequest ebcCallbackRequest) throws PinakaClientException {
        Response response = null;
        String path = Constants.EBC_CALLBACK_PATH_V3;

        try {
            Invocation.Builder invocationBuilder = this.webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(ebcCallbackRequest));
            if (response.getStatus() != 204) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public FetchApplicationResponse fetchActiveApplicationWithJourneyDetails(String merchantId, String merchantAccountId, ProductType productType) throws PinakaClientException {
        Response response = null;

        FetchApplicationResponse fetchApplicationResponse;

        try {
            Invocation.Builder invocationBuilder = webTarget.path(String.format(
                    FETCH_ACTIVE_APPLICATION_WITH_EBC_DETAILS_PATH,
                    merchantAccountId, productType.name())).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                fetchApplicationResponse = response.readEntity(FetchApplicationResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return fetchApplicationResponse;
    }

    @Override
    public void processCfaCallback(CfaCallbackRequest callbackRequest) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = this.webTarget.path(CFA_CALLBACK_API_PATH).request(MediaType.APPLICATION_JSON_TYPE);
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(callbackRequest));
            if (response.getStatus() != 204) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    private String generateAuthToken(String merchant) {
        StringBuilder keyReverse = new StringBuilder(merchant);
        String input = merchant+":"+keyReverse.reverse();
        return Base64.getEncoder().encodeToString(input.getBytes());
    }
}
