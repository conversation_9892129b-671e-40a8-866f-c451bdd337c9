package com.flipkart.fintech.pinaka.client.v6;

import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import org.glassfish.jersey.client.ClientProperties;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import java.io.InputStream;

public class PinakaClientV6ImplTest {

    PinakaClientV6 pinakaClientV6;

    @Before
    public void setUp() throws Exception {
        PinakaClientConfig pinakaClientConfig = new PinakaClientConfig();
        pinakaClientConfig.setMerchant("mp_flipkart");
        pinakaClientConfig.setClient("aapi");
        pinakaClientConfig.setUrl("http://***********:80");
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 15000);
        client.property(ClientProperties.READ_TIMEOUT, 15000);
        pinakaClientV6 = new PinakaClientV6Impl(pinakaClientConfig, client);
    }

    @Test
    @Ignore
    public void documentUpload() throws PinakaClientException {
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("base64Image.txt");
        pinakaClientV6.documentUpload("ACC1", "SELFIE_IMAGE", "APP123",
                "", "", inputStream, "", "123"
        );
    }
}