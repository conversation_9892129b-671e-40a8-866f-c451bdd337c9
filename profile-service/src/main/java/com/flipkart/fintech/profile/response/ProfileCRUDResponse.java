package com.flipkart.fintech.profile.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.profile.model.BasicDetails;
import lombok.*;

import java.util.Map;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ToString
public class ProfileCRUDResponse {
    private Long profileId;
    private String errorMessage;
    private Map<String, Object> data;

}