package com.flipkart.fintech.profile.web.v1;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.profile.response.ProfileBasicDetailResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.response.ProfileCRUDResponse;
import com.flipkart.fintech.profile.request.ProfileRequest;
import com.flipkart.fintech.profile.service.ProfileService;
import io.dropwizard.hibernate.UnitOfWork;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("1/profile")
public class ProfileServiceResource {

    private final ProfileService profileService;

    @Inject
    public ProfileServiceResource(ProfileService profileService) {
        this.profileService = profileService;
    }

    @POST
    @Timed
    @ExceptionMetered
    @Path("/create")
    @UnitOfWork(value = "profile_service")
    public ProfileCRUDResponse createProfile(@Valid ProfileRequest request) {
        log.debug("Creating profile for userId: {}", request.getUserId());
        ProfileCRUDResponse profileResponse = this.profileService.createProfile(request);
        return profileResponse;
    }

    @GET
    @Timed
    @ExceptionMetered
    @Path("/all/{profile_id}")
    @UnitOfWork(value = "profile_service")
    public ProfileDetailedResponse getAll(@PathParam("profile_id") Long profileId) {
        log.info("Getting latest profile for profile_id: {}", profileId);
        return this.profileService.getProfileById(profileId);
    }
    @GET
    @Timed
    @ExceptionMetered
    @Path("/getAll/{user_id}")
    @UnitOfWork(value = "profile_service")
    public ProfileDetailedResponse getAll(@PathParam("user_id") String userId) {
        log.info("Getting latest profile for profile_id: {}", userId);
        return this.profileService.getProfileByUserId(userId, null, false);
    }

    @GET
    @Timed
    @ExceptionMetered
    @Path("/basic-profile/{sm_user_id}")
    @UnitOfWork(value = "profile_service")
    public ProfileBasicDetailResponse getProfile(@PathParam(value = "sm_user_id") String smUserId) {
        log.info("Fetch profile for smUserId: {}", smUserId);
        ProfileBasicDetailResponse profileBasicDetailResponse = this.profileService.getProfile(smUserId);
        return profileBasicDetailResponse;
    }
}