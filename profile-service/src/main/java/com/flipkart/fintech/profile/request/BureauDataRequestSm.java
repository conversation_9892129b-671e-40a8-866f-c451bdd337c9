package com.flipkart.fintech.profile.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.profile.bureau.models.Consent;
import lombok.Data;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class BureauDataRequestSm {

    @JsonProperty("smAccountId")
    @NotBlank
    private String smAccountId;

    @NotNull
    @Valid
    @JsonProperty("consent")
    private Consent consent;

    @JsonProperty("doRefresh")
    private boolean doRefresh;
}
