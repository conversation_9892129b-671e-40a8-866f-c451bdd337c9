package com.flipkart.fintech.profile.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.profile.response.VerifyVpaResponse;
import com.flipkart.fintech.profile.utils.ObjectMapperUtils;
import com.flipkart.payments.ResponseStatus;
import com.flipkart.upi.user.service.api.models.base.v1.request.search_management.GenericSearchRequestDTO;
import com.flipkart.upi.user.service.api.models.base.v1.response.search_management.GenericSearchResponseDTO;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.SneakyThrows;

import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;


public class UpiUserServiceImpl implements UpiUserService{
    private final WebTarget upiUserSvcWebTarget;
    private final ObjectMapper objectMapper = ObjectMapperUtils.getSnakeCaseObjectMapper();

    @Inject
    public UpiUserServiceImpl(@Named("UpiUserWebTarget") WebTarget upiUserSvcWebTarget) {
        this.upiUserSvcWebTarget = upiUserSvcWebTarget;
    }

    @SneakyThrows
    @Override
    public GenericSearchResponseDTO verifyVpa(GenericSearchRequestDTO searchRequestDTO) {
        VerifyVpaResponse res = upiUserSvcWebTarget.path("/v1/upi-user/search").request(MediaType.APPLICATION_JSON_TYPE).headers(getHeaders())
                .post(Entity.json(objectMapper.writeValueAsString(searchRequestDTO))).readEntity(VerifyVpaResponse.class);
        if(ResponseStatus.SUCCESS.equals(res.getStatus())){
            return objectMapper.convertValue(res.getPayload(),GenericSearchResponseDTO.class);
        }
        return new GenericSearchResponseDTO();
    }

    private MultivaluedMap<String, Object> getHeaders() {
        MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add("Content-Type", "application/json");
        headers.add("x-upi-user-id", "SMU2404181112UZT1RQQVM18C3GMJNO6VNK");
        headers.add("x-sm-user-id", "SMAB6A16554A01A452FAA5668A6D826C91C"); //hard coding
        return headers;
    }
}
