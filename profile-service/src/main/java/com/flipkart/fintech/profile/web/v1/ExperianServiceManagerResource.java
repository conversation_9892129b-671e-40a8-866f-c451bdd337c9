package com.flipkart.fintech.profile.web.v1;

import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.BureauReport;
import com.flipkart.fintech.profile.response.BureauStoreResponse;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.UserData;
import com.google.inject.Inject;
import io.dropwizard.hibernate.UnitOfWork;
import lombok.CustomLog;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("1/bureau")
public class ExperianServiceManagerResource {
    private final BureauDataManager bureauDataManager;
    private final UserDataClient userDataClient;

    @Inject
    public ExperianServiceManagerResource(BureauDataManager bureauDataManager, UserDataClient userDataClient) {
        this.bureauDataManager = bureauDataManager;
        this.userDataClient = userDataClient;
    }

    @POST
    @Path("/credit_score")
    @UnitOfWork(value = "profile_service")
    public Response getCreditScore(@Valid BureauDataRequest bureauDataRequest) {
        log.info("Getting credit score for user id : {}", bureauDataRequest.getMerchantUserId());
        BureauDataResponse bureauDataResponse = bureauDataManager.getBureauData(bureauDataRequest);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @GET
    @Path("/refresh/credit_score/{user_id}")
    @UnitOfWork(value = "profile_service")
    public Response getRefreshCreditScore(@PathParam("user_id") String userid) {
        log.info("Getting refresh credit score for user id : {}", userid);
        BureauDataResponse bureauDataResponse = bureauDataManager.getRefreshBureauData(userid,null);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }
    @GET
    @Path("/existing/credit_score/{user_id}")
    @UnitOfWork(value = "profile_service")
    public Response getExistingCreditScore(@PathParam("user_id") String userid) {
        log.info("Getting existing credit score for user id : {}", userid);
        BureauDataResponse bureauDataResponse = bureauDataManager.getExistingBureauData(userid,null);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @GET
    @Path("/fetchExperianData/{sm_user_id}")
    public Response getExperianData(@PathParam("sm_user_id") String smUserId) {
        log.info("Getting existing credit score for user id : {}", smUserId);
        InitialUserDataResponse initialUserDataResponse = bureauDataManager.initialUserData(smUserId);
        return Response.status(Response.Status.OK).entity(initialUserDataResponse).build();
    }

}