package com.flipkart.fintech.profile.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.profile.pubsub.PublisherConfig;
import com.flipkart.fintech.user.data.config.SmUserServiceClientConfig;
import lombok.Data;


@Data
public class  ProfileServiceConfig {

    @JsonProperty
    private ExperianConfig experianConfig;

    @JsonProperty
    private SmUserServiceClientConfig smUserServiceClientConfig;

    @JsonProperty(value = "publisherConfig")
    private PublisherConfig publisherConfig;

    @JsonProperty
    private boolean experianMocked = false;

    @JsonProperty(value = "upiUserServiceClientConfig")
    private UpiUserSvcConfig upiUserSvcConfig;
}