package com.flipkart.fintech.profile.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.profile.api.request.BureauRequest;
import com.flipkart.fintech.profile.bureau.models.Consent;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class BureauDataRequest {

    @JsonProperty("merchantUserId")
    private String merchantUserId;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("smUserId")
    @NotEmpty
    private String smUserId;

    @JsonProperty("firstName")
    @NotEmpty
    private String firstName;

    @JsonProperty("lastName")
    @NotEmpty
    private String lastName;

    @JsonProperty("mobileNum")
    @NotEmpty
    private String mobileNum;

    @JsonProperty("dob")
    private String dob;

    @JsonProperty("pan")
    private String pan;

    @JsonProperty("emailId")
    private String emailId;

    @JsonProperty("consent")
    @NotNull
    @Valid
    private Consent consent;

    @JsonProperty("leadId")
    private String leadId;

    public String getMerchantUserId() {
        return this.merchantUserId == null ? this.accountId : this.merchantUserId;
    }

    public BureauRequest mapToBureauRequest(){
        BureauRequest bureauRequest = new BureauRequest();
        bureauRequest.setFirstName(this.getFirstName());
        bureauRequest.setLastName(this.getLastName());
        bureauRequest.setMobileNo(this.getMobileNum());
        bureauRequest.setEmail(this.getEmailId());
        bureauRequest.setPan(this.getPan());
        return bureauRequest;
    }
}
