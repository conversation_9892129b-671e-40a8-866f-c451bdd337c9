package com.flipkart.fintech.profile.pubsub;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.core.ApiFuture;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.pubsub.v1.PubsubMessage;
import com.google.pubsub.v1.TopicName;
import lombok.CustomLog;

import java.io.IOException;

@CustomLog
public class PubsubPublisher {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final PublisherConfig publisherConfig;
    private final TopicName topic;
    private final Publisher publisher;

    public PubsubPublisher(PublisherConfig publisherConfig) throws IOException {
        this.publisherConfig = publisherConfig;
        this.topic = TopicName.of(publisherConfig.getProjectId(), publisherConfig.getTopicId());
        this.publisher = Publisher.newBuilder(this.topic).setEnableMessageOrdering(publisherConfig.isMessageOrderingEnabled()).build();
    }

    public String publishSync(PubsubMessage message) throws Exception {
        try {
            log.info("publishing message synchronously in topic {} with payload {}", this.topic.toString(),
                    message);
            ApiFuture<String> apiFuture = publisher.publish(message);
            String messageId = apiFuture.get();
            log.info("successfully published message synchronously in topic {} with messageId {} and payload {}", this.topic.toString(), messageId,
                    message);
            return messageId;
        } catch (Exception e) {
            // Must call resumePublish to reset key and continue publishing with order
            if (this.publisherConfig.isMessageOrderingEnabled()) {
                publisher.resumePublish(message.getOrderingKey());
            }
            log.error("failed to published message synchronously in topic {} with payload {}", this.topic.toString(),
                    message, e);
            throw e;
        }
    }


    // once you are done with publishing, shutdown the publisher to free up resources
    public void shutdown() {
        log.info("shutting down publisher for topic {}", this.topic.toString());
        try {
            this.publisher.shutdown();
        } catch (Exception e) {
            log.error("error shutting down publisher for topic {}", this.topic.toString(), e);
        }
    }
}
