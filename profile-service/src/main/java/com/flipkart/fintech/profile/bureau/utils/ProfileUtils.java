package com.flipkart.fintech.profile.bureau.utils;

import com.flipkart.fintech.exception.ServiceErrorResponse;
import org.apache.commons.lang3.StringUtils;

public class ProfileUtils {
    public static ServiceErrorResponse getServiceErrorResponse(ErrorCode errorCode, String message) {
        return ServiceErrorResponse.builder()
                .statusCode(errorCode.getHttpCode())
                .errorCode(errorCode.name())
                .errorMessage(StringUtils.isBlank(message) ? errorCode.getMessage() : message)
                .build();
    }
}
