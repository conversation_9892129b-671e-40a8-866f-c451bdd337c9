package com.flipkart.fintech.profile.Decrypter;

import com.flipkart.fintech.security.aes.AESService;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class DecrypterImpl implements Decrypter {
    private String encryptKey = "cGlpRW5jcnlwdGlvbktleQ==";
    @Override
    public String decrypt(String encryptedString) {
        if (encryptedString == null) {
            return null;
        }
        byte[] plaintextBytes = AESService.decrypt(encryptKey, Base64.getDecoder().decode(encryptedString.getBytes(StandardCharsets.UTF_8)));
        return new String(plaintextBytes, StandardCharsets.UTF_8);
    }
}