package com.flipkart.fintech.profile.yak;

import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.yak.entities.BureauDataEntity;

public interface BureauDataStore {

    BureauDataEntity getExperianInsight(String profileId, String accountId);

    BureauDataEntity getExperianBureauRawData(String profileId, String accountId);

    void putExperianBureauData(BureauDataEntity bureauDataEntity, String accountId, Long createdTimestampInMillis);
    void updateExperianCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, Long createdTimestampInMillis);
    boolean getIfExperianCrossMerchantConsentExists(String accountId);
    void deleteExperianConsent(String accountId);

}
