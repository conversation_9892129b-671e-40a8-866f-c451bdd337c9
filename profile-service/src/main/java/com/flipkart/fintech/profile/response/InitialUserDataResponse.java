package com.flipkart.fintech.profile.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import lombok.*;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class InitialUserDataResponse {

    private String firstAndMiddleName;
    private String lastName;
    private String panNumber;
    private String dateOfBirth;
    private String gender;
    private String email;
    private String employmentType;
    private List<CAISHolderAddressDetails> experianAddressDetails;
    private AddressDetailResponse addressDetailResponse;

}
