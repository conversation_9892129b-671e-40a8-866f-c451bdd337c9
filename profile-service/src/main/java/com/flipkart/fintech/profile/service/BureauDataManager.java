package com.flipkart.fintech.profile.service;

import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.response.*;

public interface BureauDataManager {
    BureauDataResponse getBureauData(BureauDataRequest bureauDataRequest);

    void addBureauData(ExperianBureauDataDto experianBureauDataDto, String smUserId, String merchantKey);

    BureauDataResponse getRefreshBureauData(String merchantUserId, String smUserId);

    BureauDataResponse getExistingBureauData(String merchantUserId, String smUserId);

    BureauDataForInsight getBureauReportBySmUserId(String smUserId, Consent consent, String leadId, Integer monthlyIncome, String fullName);

    BureauDataForInsight getBureauReport(String merchantUserId, String smUserId, Long profileId, String leadId, Consent consent, Integer monthlyIncome);

    BureauDataForInsight getBureauReport(String merchantUserId, String smUserId, Long profileId, String leadId, Consent consent, Integer monthlyIncome, String fullName);

    BureauDataResponse getBureauDataSm(BureauDataRequestSm bureauDataRequest);

    BureauDataForInsight getBureauDataSmV2(BureauDataRequest bureauDataRequest);

    BureauDataResponse getExistingBureauDataSm(String smUserId);

    BureauDataResponse getRefreshBureauDataSm(String smUserId);

    CrossMerchantConsentResponse updateCrossMerchantConsent(CrossMerchantConsentRequest request);

    BureauDataResponse backfillEventData(String smUserId);

    InitialUserDataResponse initialUserData(String smUserId);

    DeleteBureauConsentResponse deleteBureauConsent(String smUserId);

    BureauDataForInsight refreshBureauDataSm(String smUserId, String merchantUserId);

}