package com.flipkart.fintech.profile.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtils {

    public static final SimpleDateFormat experianDateFormat = new SimpleDateFormat("yyyyMMdd");

    private DateUtils() {
    }

    public static Date getDateFromString(String dateString, SimpleDateFormat formatter)
            throws ParseException {
        return formatter.parse(dateString);
    }
}