package com.flipkart.fintech.profile.service;

import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;

public interface BureauDataService {
    ExperianBureauDataDto getBureauInsight(String profileId, String smUserId);

    void addBureauData(ExperianBureauDataDto experianBureauDataDto, String smUserId, String merchantKey);

    ExperianBureauDataDto getBureauRawData(String profileId, String smUserId);

    void updateExperianCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, Long createdTimestampInMillis);

    boolean getIfExperianCrossMerchantConsentExists(String accountId);

    void deleteExperianConsent(String accountId);
}