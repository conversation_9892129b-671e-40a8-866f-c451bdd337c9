package com.flipkart.fintech.profile.bureau.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class ExperianBureauResponse {
    private String showHtmlReportForCreditReport;
    private Date createdAt;
    private String stgOneHitId;
    private String stgTwoHitId;
    private Integer experianUserId;
}
