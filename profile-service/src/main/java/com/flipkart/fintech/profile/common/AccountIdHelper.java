package com.flipkart.fintech.profile.common;

import com.flipkart.kloud.config.DynamicBucket;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

@CustomLog
public class AccountIdHelper {
    private static final String USE_SM_ACCOUNT_ID = "use_sm_account_id";
    private static final String SM_ACCOUNT_ID_PREFIX = "SM";
    private static DynamicBucket dynamicBucket;
    private static volatile boolean isInitialised = false;

    /**
     * For Account Migration, we will flow both merchant account id and new supermoney account id and based on flags,
     * one of the account id will be used. this method will decide which account id to user when using it get flows based
     * on dynamic config
     *
     * @param merchantAccountId
     * @param accountId
     * @return
     */
    public static String getAccountIdToUse(String merchantAccountId, String accountId) {
        boolean shouldUseSMAccountId = shouldUseSMAccountId();
        return shouldUseSMAccountId ? accountId : merchantAccountId;
    }

    public static boolean shouldUseSMAccountId() {
        verifyInitialisation();
        return DynamicConfigHelper.getBoolean(dynamicBucket, USE_SM_ACCOUNT_ID, false);
    }

    public static boolean shouldUseSMAccountId(String smAccountId) {
        verifyInitialisation();
        return StringUtils.isNotEmpty(smAccountId) && DynamicConfigHelper.getBoolean(dynamicBucket, USE_SM_ACCOUNT_ID, false);
    }

    public static boolean isSMAccountId(String accountId) {
        return accountId.startsWith(SM_ACCOUNT_ID_PREFIX);
    }

    public static void init(DynamicBucket bucket) {
        if (isInitialised) {
            log.error("AccountId Helper is already initialised");
        }

        dynamicBucket = bucket;
        isInitialised = true;
    }

    private static void verifyInitialisation() {
        if (!isInitialised) {
            throw new RuntimeException("AccountId Helper is not initialised");
        }
    }
}
