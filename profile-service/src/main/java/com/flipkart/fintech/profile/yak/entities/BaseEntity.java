package com.flipkart.fintech.profile.yak.entities;

import com.flipkart.fintech.yak.annotation.Column;
import lombok.Data;

import java.util.Date;

@Data
public abstract class BaseEntity {

    public static final Long INITIAL_VERSION = -1L;

    private String id;
    private String merchant;
    public BaseEntity(){
    }
    public BaseEntity(String id){
        this.id = id;
    }

    public String getMerchant() {
        return merchant == null ? "FLIPKART" : merchant;
    }

}
