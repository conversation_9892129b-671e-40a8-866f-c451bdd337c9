package com.flipkart.fintech.profile.bureau.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@AllArgsConstructor
@NoArgsConstructor
@CustomLog
public class Consent {
    @JsonProperty("purpose")
    @NotEmpty
    private String purpose;

    @JsonProperty("provided")
    private boolean provided;

    @JsonProperty("ip")
    @NotEmpty
    private String ip;

    @JsonProperty("deviceId")
    @NotEmpty
    private String deviceId;

    @JsonProperty("deviceInfo")
    @NotEmpty
    private String deviceInfo;

    @JsonProperty("ts")
    @NotNull
    private Long ts;

    public void setTs(Long ts) {
        this.ts = ts;
        if (ts == null) return;
        long currentTs = System.currentTimeMillis();
        if (ts > currentTs) {
            log.warn("Device ts ({}) > current ts ({})", ts, currentTs);
            this.ts = currentTs;
        }
    }

}