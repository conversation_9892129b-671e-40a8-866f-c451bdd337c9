package com.flipkart.fintech.profile.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "address_details")
public class AddressDetails implements Serializable {

    @EmbeddedId
    private AddressDetailsId addressDetailsId;

    public AddressDetailsId getAddressDetailsId() {
        return addressDetailsId;
    }

    public void setAddressDetailsId(AddressDetailsId addressDetailsId) {
        this.addressDetailsId = addressDetailsId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public Profile getProfile() {
        return profile;
    }

    public void setProfile(Profile profile) {
        this.profile = profile;
    }

    @ManyToOne
    @MapsId("profileId")
    @JoinColumn(name = "profile_id")
    private Profile profile;

    @Column(name = "pincode")
    private int pincode;

    public int getPincode() {
        return pincode;
    }

    public void setPincode(int pincode) {
        this.pincode = pincode;
    }

    @Setter
    @Getter
    @Column(name = "address_line1")
    private String addressLine1;

    @Setter
    @Getter
    @Column(name = "address_line2")
    private String addressLine2;

    public Status getActive() {
        return active;
    }

    public void setActive(Status active) {
        this.active = active;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "active")
    private Status active;

    @Column(name = "created_at")
    private Date createdAt;

}
