package com.flipkart.fintech.profile.service;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.flipkart.fintech.profile.common.AccountIdHelper;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.fintech.profile.dao.AddressDetailsDao;
import com.flipkart.fintech.profile.dao.BasicDetailsDao;
import com.flipkart.fintech.profile.dao.ContactDetailsDao;
import com.flipkart.fintech.profile.dao.EmploymentDetailsDao;
import com.flipkart.fintech.profile.dao.ProfileDaoImpl;
import com.flipkart.fintech.profile.model.*;
import com.flipkart.fintech.profile.request.ProfileRequest;
import com.flipkart.fintech.profile.response.ProfileBasicDetailResponse;
import com.flipkart.fintech.profile.response.ProfileCRUDResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import com.flipkart.fintech.profile.Decrypter.Decrypter;

import java.util.*;
import javax.inject.Inject;

import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SessionFactory;

@CustomLog
public class ProfileServiceImpl implements ProfileService {
    private final BasicDetailsDao basicDetailsDao;
    private final ContactDetailsDao contactDetailsDao;
    private final AddressDetailsDao addressDetailsDao;
    private final EmploymentDetailsDao employmentDetailsDao;
    private final ProfileDaoImpl profileServiceDao;
    private final UserDataClient userDataClient;
    private final MetricRegistry metricRegistry;
    private final Decrypter decrypter;

    @Inject
    public ProfileServiceImpl(UserDataClient userDataClient, MetricRegistry metricRegistry,
                              Decrypter decrypter, ProfileDaoImpl profileServiceDao, AddressDetailsDao addressDetailsDao,
                              EmploymentDetailsDao employmentDetailsDao, BasicDetailsDao basicDetailsDao, ContactDetailsDao contactDetailsDao) {
        this.addressDetailsDao = addressDetailsDao;
        this.employmentDetailsDao = employmentDetailsDao;
        this.decrypter = decrypter;
        this.profileServiceDao = profileServiceDao;
        this.userDataClient = userDataClient;
        this.basicDetailsDao = basicDetailsDao;
        this.contactDetailsDao = contactDetailsDao;
        this.metricRegistry = metricRegistry;
    }

    private static Gender validateGender(String gender) {
        try {
            return Gender.valueOf(gender.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ProfileCRUDResponse createProfile(ProfileRequest profileRequest) {
        if (profileRequest != null && !StringUtils.isEmpty(profileRequest.getUserId()) && !StringUtils.isBlank(profileRequest.getPan())) {
            try {
                Profile profile = getProfileByRequest(profileRequest);
                if (profile != null) {
                    log.info("Profile exists, updating the profile");
                } else {
                    log.info("Creating profile");
                    profile = upsertProfile(profileRequest);
                }
                try {
                    upsertBasicDetails(profileRequest, profile);
                    upsertAddressDetails(profileRequest, profile);
                    upsertEmploymentDetails(profileRequest, profile);
                    upsertContactDetails(profileRequest, profile);
                    return ProfileCRUDResponse.builder().profileId(profile.getProfileId()).build();
                } catch (Exception e) {
                    log.info("getting error for profile request creation request {} exception {}", profileRequest, e.getMessage());
                    throw new RuntimeException("Error while creating profile");
                }
            } catch (Exception e) {
                log.info("getting error for profile request creation request {} exception {}", profileRequest, e.getMessage());
                throw new RuntimeException("Error while creating profile");
            }
        } else {
            log.error("Invalid profile request with invalid pan or user id request", profileRequest);
            throw new IllegalArgumentException("Invalid profile request");
        }
    }

    private void upsertEmploymentDetails(ProfileRequest profileRequest, Profile profile) throws Exception {
        if (!StringUtils.isBlank(profileRequest.getEmploymentType())) {
            try (Timer.Context timer = metricRegistry.timer("upsertEmploymentDetailsSQL").time()) {
                EmploymentDetails employmentDetails = new EmploymentDetails();
                if (profile.getEmploymentDetails() != null) {
                    employmentDetails = profile.getEmploymentDetails();
                }
                employmentDetails.setEmploymentType(EmploymentType.valueOf(profileRequest.getEmploymentType()));
                if (StringUtils.isNotEmpty(profileRequest.getMonthlyIncome())) {
                    employmentDetails.setMonthlyIncome(Integer.valueOf(profileRequest.getMonthlyIncome()));
                }
                if(profileRequest.isValidBonusIncome()){
                    employmentDetails.setBonusIncome(Integer.valueOf(profileRequest.getBonusIncome()));
                }
                employmentDetails.setCompanyName(profileRequest.getCompanyName());
                employmentDetails.setOrganizationId(profileRequest.getOrganizationId());
                employmentDetails.setIndustryType(profileRequest.getIndustryType());
                employmentDetails.setIndustryId(profileRequest.getIndustryId());
                if (profileRequest.getIncomeSource() != null) {
                    employmentDetails.setIncomeSource(IncomeSource.valueOf(profileRequest.getIncomeSource()));
                }
                if (StringUtils.isNotEmpty(profileRequest.getAnnualTurnOver())) {
                    employmentDetails.setAnnualTurnOver(Integer.valueOf(profileRequest.getAnnualTurnOver()));
                }
                employmentDetails.setProfileId(profile.getProfileId());
                profile.setEmploymentDetails(employmentDetails);
                employmentDetailsDao.saveOrUpdate(employmentDetails);
            } catch (Exception e) {
                throw new Exception(e);
            }
        } else {
            log.info("Invalid profile request with invalid employment details,employment_details not updated");
        }
    }

    private void upsertAddressDetails(ProfileRequest profileRequest, Profile profile) throws Exception {
        if (profileRequest.getPincode() != null) {
            try {
                AddressDetails addressDetails = new AddressDetails();
                AddressDetailsId addressDetailsId = new AddressDetailsId();
                addressDetailsId.setProfileId(profile.getProfileId());
                addressDetails.setAddressDetailsId(addressDetailsId);
                addressDetails.setPincode(profileRequest.getPincode());
                addressDetails.setProfile(profile);
                addressDetails.setAddressLine1(profileRequest.getAddressLine1());
                addressDetails.setAddressLine2(profileRequest.getAddressLine2());
                List<AddressDetails> addressDetailsList = new ArrayList<>();
                if (profile.getAddressDetailsList() != null) {
                    addressDetailsList = profile.getAddressDetailsList();
                    addressDetailsList.add(addressDetails);
                } else {
                    addressDetailsList.add(addressDetails);
                    profile.setAddressDetailsList(addressDetailsList);
                }
                addressDetailsDao.saveOrUpdate(addressDetails);
            } catch (Exception e) {
                log.error("Error in upserting address details :", e.getMessage(), e);
                throw new Exception(e);
            }
        } else {
            log.info("Invalid address details, address_details not updated");
        }
    }

    private void upsertBasicDetails(ProfileRequest profileRequest, Profile profile) {
        try {
            BasicDetails basicDetails = new BasicDetails();
            if (profile.getBasicDetails() != null) {
                basicDetails = profile.getBasicDetails();
            }
            basicDetails.setProfileId(profile.getProfileId());
            if (!StringUtils.isBlank(profileRequest.getDob())) {
                basicDetails.setDob(profileRequest.getDob());
            }
            if (!StringUtils.isBlank(profileRequest.getGender())) {
                basicDetails.setGender(validateGender(profileRequest.getGender()).toString());
            }
            if (!StringUtils.isBlank(profileRequest.getFirstName())) {
                basicDetails.setFirstName(profileRequest.getFirstName());
            }
            if (!StringUtils.isBlank(profileRequest.getLastName())) {
                basicDetails.setLastName(profileRequest.getLastName());
            }

            if (!StringUtils.isBlank(profileRequest.getVerifiedFirstName()) && !"null".equals(profileRequest.getVerifiedFirstName())) {
                basicDetails.setVerifiedFirstName(profileRequest.getVerifiedFirstName());
            }

            if (!StringUtils.isBlank(profileRequest.getVerifiedLastName()) && !"null".equals(profileRequest.getVerifiedLastName())) {
                basicDetails.setVerifiedLastName(profileRequest.getVerifiedLastName());
            }

            if (profileRequest.getMatch() != null) {
                basicDetails.setNameMatch(profileRequest.getMatch());
            }

            if (profileRequest.getMatchScore() != null) {
                basicDetails.setMatchScore(profileRequest.getMatchScore());
            }

            profile.setBasicDetails(basicDetails);
            this.basicDetailsDao.saveOrUpdate(basicDetails);
        } catch (Exception e) {
            log.info("Invalid basic details, basic_details not updated");
        }

    }

    private void upsertContactDetails(ProfileRequest profileRequest, Profile profile) {
        try {
            ContactDetails contactDetails = new ContactDetails();
            if (profile.getContactDetails() != null) {
                contactDetails = profile.getContactDetails();
            }
            contactDetails.setProfileId(profile.getProfileId());
            if (!StringUtils.isBlank(profileRequest.getEmailId())) {
                contactDetails.setContactType("Email");
                contactDetails.setContactValue(profileRequest.getEmailId());
            }
            profile.setContactDetails(contactDetails);
            this.contactDetailsDao.saveOrUpdate(contactDetails);
        } catch (Exception e) {
            log.info("Invalid contact details, contact_details not updated");
            ;
        }

    }

    private Profile upsertProfile(ProfileRequest profileRequest) {
        try (Timer.Context timer = metricRegistry.timer("upsertProfileSQL").time()) {
            Profile profileEntity = new Profile();
            profileEntity.setUserId(profileRequest.getUserId());
            profileEntity.setSmUserId(profileRequest.getSmUserId());
            profileEntity.setPan(profileRequest.getPan());
            return this.profileServiceDao.saveOrUpdate(profileEntity);
        } catch (Exception e) {
            log.error("Invalid profile details, profile not updated");
            throw new RuntimeException();
        }
    }

    @Override
    public ProfileDetailedResponse getProfileById(Long profileId) {
        Profile profile = this.profileServiceDao.getAll(profileId);
        UserData userData = getUserData(profile.getUserId(), profile.getSmUserId());
        return ProfileDetailedResponse.builder()
                .merchantUserId(profile.getUserId())
                .smUserId(profile.getSmUserId())
                .profileId(profile.getProfileId())
                .phoneNo(getPhoneNum(userData))
                .dob(Optional.ofNullable(profile.getBasicDetails()).map(BasicDetails::getDob).orElse(null))
                .gender(Optional.ofNullable(profile.getBasicDetails()).map(BasicDetails::getGender).orElse(null))
                .userEnteredPincode(Optional.ofNullable(profile.getLatestAddress()).map(AddressDetails::getPincode).orElse(null))
                .email(getEmail(profile))
                .firstName(getFirstName(profile))
                .lastName(getLastName(profile))
                .pan(profile.getPan())
                .employmentType(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getEmploymentType).orElse(null))
                .incomeSource(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getIncomeSource).orElse(null))
                .build();
    }

    private String getEmail(Profile profile) { //TODO: WARNING!! CHECK PROFILE OBJECT
        if (Objects.isNull(profile) || Objects.isNull(profile.getContactDetails())) {
            return null;
        }
        return profile.getContactDetails().getContactValue();
    }

    private String getFirstName(Profile profile) { //TODO: WARNING!! CHECK PROFILE OBJECT
        if (Objects.isNull(profile) || Objects.isNull(profile.getBasicDetails())) {
            return null;
        }

        if ((StringUtils.isNotEmpty(profile.getBasicDetails().getVerifiedFirstName())) && !Objects.equals(profile.getBasicDetails().getVerifiedFirstName(), "null")) {
            return profile.getBasicDetails().getVerifiedFirstName();
        }
        return profile.getBasicDetails().getFirstName();
    }

    private String getLastName(Profile profile) { //TODO: WARNING!! CHECK PROFILE OBJECT
        if (Objects.isNull(profile) || Objects.isNull(profile.getBasicDetails())) {
            return null;
        }
        if ((StringUtils.isNotEmpty(profile.getBasicDetails().getVerifiedLastName())) && !Objects.equals(profile.getBasicDetails().getVerifiedLastName(), "null")) {
            return profile.getBasicDetails().getVerifiedLastName();
        }
        return profile.getBasicDetails().getLastName();
    }

    private String getPhoneNum(UserData userData) {
        if (Objects.isNull(userData.getPrimaryPhone())) {
            return null;
        }
        if (userData.getPrimaryPhone().length() > 10) {
            return userData.getPrimaryPhone().substring(3);
        }
        return userData.getPrimaryPhone();
    }

    @Override
    public ProfileDetailedResponse getProfileByUserId(String userId, String smUserId, boolean isUnmaskedData) {
        Profile profile = this.profileServiceDao.getAllByUserId(userId, smUserId);
        UserData userData = getUserData(userId, smUserId);
        String phoneNum = getPhoneNum(userData);
        if (profile == null) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(ProfileServiceImpl.class, "profile", "null")).mark();
            return ProfileDetailedResponse.builder().phoneNo(phoneNum).build();
        }
        String email = Optional.ofNullable(profile.getContactDetails())
                .filter(contactDetails -> "Email".equals(contactDetails.getContactType()))
                .map(ContactDetails::getContactValue)
                .orElse(null);
        String firstName = Optional.ofNullable(profile.getBasicDetails()).map(BasicDetails::getFirstName).orElse(null);
        String lastName = Optional.ofNullable(profile.getBasicDetails()).map((BasicDetails::getLastName)).orElse(null);
        if (isUnmaskedData) {
            firstName = decrypter.decrypt(firstName);
            lastName = decrypter.decrypt(lastName);
        }
        return ProfileDetailedResponse.builder()
                .smUserId(profile.getSmUserId())
                .merchantUserId(profile.getUserId())
                .profileId(profile.getProfileId())
                .phoneNo(phoneNum)
                .dob(Optional.ofNullable(profile.getBasicDetails()).map(BasicDetails::getDob).orElse(null))
                .gender(Optional.ofNullable(profile.getBasicDetails()).map(BasicDetails::getGender).orElse(null))
                .email(email)
                .firstName(firstName)
                .lastName(lastName)
                .pan(profile.getPan())
                .addressLine1(Optional.ofNullable(profile.getLatestAddress()).map(AddressDetails::getAddressLine1).orElse(null))
                .addressLine2(Optional.ofNullable(profile.getLatestAddress()).map(AddressDetails::getAddressLine2).orElse(null))
                .companyName(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getCompanyName).orElse(null))
                .organizationId(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getOrganizationId).orElse(null))
                .monthlyIncome(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getMonthlyIncome).orElse(null))
                .bonusIncome(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getBonusIncome).orElse(null))
                .incomeSource(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getIncomeSource).orElse(null))
                .industryType(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getIndustryType).orElse(null))
                .industryId(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getIndustryId).orElse(null))
                .annualTurnOver(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getAnnualTurnOver).orElse(null))
                .userEnteredPincode(Optional.ofNullable(profile.getLatestAddress()).map(AddressDetails::getPincode).orElse(null))
                .employmentType(Optional.ofNullable(profile.getEmploymentDetails()).map(EmploymentDetails::getEmploymentType).orElse(null))
                .build();
    }

    public Profile getProfileByRequest(ProfileRequest profileRequest) {
        if (StringUtils.isNotEmpty(profileRequest.getSmUserId())) {
            List<Profile> profiles = profileServiceDao.getProfileBySMUserIdPan(profileRequest.getSmUserId(), profileRequest.getPan());
            if (!profiles.isEmpty()) return profiles.stream()
                    .filter(profile -> profile.getUserId().equals(profileRequest.getUserId()))
                    .findAny()
                    .orElse(profiles.get(0));
        }
        return profileServiceDao.getProfileByUserIdPan(profileRequest.getUserId(), profileRequest.getPan());
    }

    private UserData getUserData(String merchantUserId, String smUserId) {
        if (AccountIdHelper.shouldUseSMAccountId(smUserId)) {
            return userDataClient.getUserData(Merchant.SUPERMONEY, merchantUserId, smUserId, PIIDataType.PLAINTEXT);
        }
        return userDataClient.getUserData(Merchant.FLIPKART, merchantUserId, smUserId, PIIDataType.PLAINTEXT);
    }

    @Override
    public ProfileBasicDetailResponse getProfile(final String smUserId) {
        ProfileBasicDetailResponse response = null;
        Optional<Profile> profileOptional = Optional.ofNullable(this.profileServiceDao.getProfileBySmUserId(smUserId));
        if (profileOptional.isPresent() && profileOptional.get().getBasicDetails() != null) {
            Profile profile = profileOptional.get();
            response = ProfileBasicDetailResponse.builder()
                    .smUserId(profile.getSmUserId())
                    .merchantUserId(profile.getUserId())
                    .firstName(profile.getBasicDetails().getFirstName())
                    .lastName(profile.getBasicDetails().getLastName())
                    .nameMatch(profile.getBasicDetails().getNameMatch())
                    .matchScore(profile.getBasicDetails().getMatchScore())
                    .build();
        }
        return response;
    }
}