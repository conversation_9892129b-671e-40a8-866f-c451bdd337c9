package com.flipkart.fintech.profile.service;

import com.flipkart.upi.user.service.api.models.base.v1.request.search_management.GenericSearchRequestDTO;
import com.flipkart.upi.user.service.api.models.base.v1.response.search_management.GenericSearchResponseDTO;
import com.google.inject.ImplementedBy;

@ImplementedBy(UpiUserServiceImpl.class)
public interface UpiUserService {
    GenericSearchResponseDTO verifyVpa(GenericSearchRequestDTO searchRequestDTO);
}
