package com.flipkart.fintech.profile.web.v2;


import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.library.Feature;
import com.flipkart.fintech.pinaka.library.FeatureReport;
import com.flipkart.fintech.pinaka.library.ReportContext;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.pinaka.library.ReportType;
import com.flipkart.fintech.pinaka.library.UserDetails;
import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.fintech.profile.request.*;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.CrossMerchantConsentResponse;
import com.flipkart.fintech.profile.response.DeleteBureauConsentResponse;
import com.flipkart.fintech.profile.response.PullExperianResponse;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.google.inject.Inject;
import io.dropwizard.hibernate.UnitOfWork;
import lombok.CustomLog;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("2/bureau")
public class ExperianServiceManagerResourceV2 {
    private final BureauDataManager bureauDataManager;
    private final ReportFeatureCalculator reportFeatureCalculator;

    @Inject
    public ExperianServiceManagerResourceV2(BureauDataManager bureauDataManager, ReportFeatureCalculator reportFeatureCalculator) {
        this.bureauDataManager = bureauDataManager;
        this.reportFeatureCalculator = reportFeatureCalculator;
    }

    @POST
    @Path("/credit_score")
    @UnitOfWork(value = "profile_service")
    public Response getCreditScore(@Valid BureauDataRequest bureauDataRequest) {
        log.info("Getting credit score from flipkart smUserId : {} request : {}", bureauDataRequest.getMerchantUserId(),
                bureauDataRequest);
        BureauDataResponse bureauDataResponse = bureauDataManager.getBureauData(bureauDataRequest);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @GET
    @Path("/refresh/credit_score")
    @UnitOfWork(value = "profile_service", readOnly = true, transactional = false)
    public Response getRefreshCreditScore(@QueryParam(value = "merchantUserId") String merchantUserId,
                                          @QueryParam(value = "smUserId") String smUserId) {
        log.info("Getting refresh credit score  MerchantUserId id : {} , smUserId : {}", merchantUserId, smUserId);
        BureauDataResponse bureauDataResponse = bureauDataManager.getRefreshBureauData(merchantUserId, smUserId);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @GET
    @Path("/existing/credit_score")
    @UnitOfWork(value = "profile_service", readOnly = true, transactional = false)
    public Response getExistingCreditScore(@QueryParam(value = "merchantUserId") String merchantUserId,
                                           @QueryParam(value = "smUserId") String smUserId) {
        log.info("Getting existing credit score MerchantUserId id : {} , smUserId : {}", merchantUserId, smUserId);
        BureauDataResponse bureauDataResponse = bureauDataManager.getExistingBureauData(merchantUserId, smUserId);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @POST
    @Path("/credit_score/sm")
    public Response getCreditScoreSm(@Valid BureauDataRequestSm bureauDataRequestSm) {
        log.info("Getting credit score for request : {}", bureauDataRequestSm);
        BureauDataResponse bureauDataResponse = bureauDataManager.getBureauDataSm(bureauDataRequestSm);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @POST
    @Path("/credit_score_features/sm")
    public Response calculateFeatures(@Valid BureauFeatureRequestSm bureauFeatureRequestSm) {
        log.info("Getting credit score features for user id : {}", bureauFeatureRequestSm.getSmAccountId());
        BureauDataForInsight bureauDataForInsight = bureauDataManager.getBureauReportBySmUserId(bureauFeatureRequestSm.getSmAccountId(),
                bureauFeatureRequestSm.getConsent(), bureauFeatureRequestSm.getLeadId(), bureauFeatureRequestSm.getMonthlyIncome(),
                bureauFeatureRequestSm.getFullName());

        ReportContext reportContext = new ReportContext();
        String deCodedXmlReport = null;
        if (StringUtils.isNotEmpty(bureauDataForInsight.getReport())) {
            deCodedXmlReport = StringEscapeUtils.unescapeHtml4(bureauDataForInsight.getReport());
        }
        reportContext.setReport(deCodedXmlReport);
        reportContext.setUserDetails(new UserDetails());
        reportContext.getUserDetails().setSmUserId(bureauFeatureRequestSm.getSmAccountId());
        reportContext.getUserDetails().setMonthlyIncome(bureauFeatureRequestSm.getMonthlyIncome());
        reportContext.setReportType(ReportType.EXPERIAN_REPORT);
        FeatureReport bureauReport = reportFeatureCalculator.calculateFeatureScore(reportContext,
                Feature.getAllFeatures(), bureauDataForInsight.getErrorString());

        return Response.status(Response.Status.OK).entity(bureauReport).build();
    }

    @POST
    @Path("/credit_score/fetch")
    public Response fetchOrRefreshCreditReportSm(@Valid BureauDataRequest bureauDataRequest) {
        log.info("Getting credit score features for user id : {}", bureauDataRequest.getSmUserId());
        PullExperianResponse pullExperianResponse = new PullExperianResponse();
        try {
            BureauDataForInsight bureauDataForInsight = bureauDataManager.getBureauDataSmV2(bureauDataRequest);
            pullExperianResponse.setErrorString(bureauDataForInsight.getErrorString());
            pullExperianResponse.setStatusCode(Response.Status.OK.name());
            return Response.ok(pullExperianResponse).build();
        } catch (Exception e) {
            log.error("unable to fetch credit score for user id : {}, {}", bureauDataRequest.getSmUserId(), e.getMessage());
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(ExperianServiceManagerResourceV2.class, "fetchOrRefreshCreditReportSm", "Failure")).mark();
            pullExperianResponse.setStatusCode(Response.Status.INTERNAL_SERVER_ERROR.name());
            return Response.ok(pullExperianResponse).build();
        }

    }

    @GET
    @Path("/existing/credit_score/sm/{user_id}")
    public Response getExistingCreditReportSm(@PathParam("user_id") String smUserid) {
        log.info("Getting existing credit score for user id : {}", smUserid);
        BureauDataResponse bureauDataResponse = bureauDataManager.getExistingBureauDataSm(smUserid);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @GET
    @Path("/refresh/credit_score/sm/{user_id}")
    public Response getRefreshCreditScoreSm(@PathParam("user_id") String userid) {
        log.info("Getting refresh credit score for user id : {}", userid);
        BureauDataResponse bureauDataResponse = bureauDataManager.getRefreshBureauDataSm(userid);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @PUT
    @Timed
    @ExceptionMetered
    @Path("/cs/consent/update")
    public Response updateCrossMerchantConsent(@Valid CrossMerchantConsentRequest request) {
        log.info("Updating cross merchant consent for account id : {}", request.getAccountId());
        CrossMerchantConsentResponse consentResponse = bureauDataManager.updateCrossMerchantConsent(request);
        return Response.status(Response.Status.OK).entity(consentResponse).build();
    }

    @GET
    @Path("/credit_score/backfill/sm/{user_id}")
    public Response creditScoreBackfill(@PathParam("user_id") String smUserId) {
        log.info("Getting credit score for user id : {}", smUserId);
        BureauDataResponse bureauDataResponse = bureauDataManager.backfillEventData(smUserId);
        return Response.status(Response.Status.OK).entity(bureauDataResponse).build();
    }

    @DELETE
    @Timed
    @ExceptionMetered
    @Path("/cs/consent/delete/{user_id}")
    public Response deleteConsent(@PathParam("user_id") String smAccountId) {
        log.info("Updating cross merchant consent for account id : {}", smAccountId);
        DeleteBureauConsentResponse deleteBureauConsentResponse = bureauDataManager.deleteBureauConsent(smAccountId);
        return Response.status(Response.Status.OK).entity(deleteBureauConsentResponse).build();
    }

    @POST
    @Timed
    @ExceptionMetered
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/bureau/refresh")
    public Response refreshBureauData(BureauRefreshRequest request) {
        try {
            log.info("calling refresh API for smUserId : {}", request.getSmUserId());
            bureauDataManager.refreshBureauDataSm(request.getSmUserId(), request.getMerchantUserId());
            Map<String, String> successResponse = new HashMap<>();
            successResponse.put("data", "Bureau data refresh triggered successfully for smUserId: " + request.getSmUserId());

            return Response.status(Response.Status.OK)
                    .entity(successResponse)
                    .build();

        } catch (Exception e) {
            log.error("Error while refreshing bureau data for smUserId: {}", request.getSmUserId(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Error while refreshing bureau data for smUserId " + request.getSmUserId() + ": " + e.getMessage());

            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(errorResponse)
                    .build();
        }
    }
}