package com.flipkart.fintech.profile.model;

import lombok.Data;

@Data
public class bureauLender {
    private String name;
    private Integer onTimePayment;
    private Integer delayedPayment;
    private Integer totalPayment;
    private Double onTimePaymentPer;
    private boolean isActive;
    private Integer accountType;
    private String creditLimit;
    private String highestCreditOrOriginalLoanAmount;
    private String currentBalance;
    private Double creditUsage;
    private String creditAge;
    private String openDate;
    private String closedDate;
    private String creditMix;
}
