package com.flipkart.fintech.profile.service;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.library.entities.*;
import com.flipkart.fintech.profile.model.AccountType;
import com.flipkart.fintech.profile.model.CreditInsights;
import com.flipkart.fintech.profile.model.bureauLender;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.utils.AccountTypeToLoanMapping;
import com.flipkart.fintech.profile.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class BureauInsightsService implements BureauInsights {
    private static final String CARD_CATEGORY_LOWERCASE = "cards";
    private static final String CARD_CATEGORY_PROPERCASE = "Cards";
    private static final String CARD_SINGULAR = "Card";

    private static final String LOAN_CATEGORY_LOWERCASE = "loans";
    private static final String LOAN_CATEGORY_PROPERCASE = "Loans";
    private static final String LOAN_SINGULAR = "Loan";

    private static final String OTHER_CATEGORY_LOWERCASE = "others";
    private static final String OTHER_CATEGORY_PROPERCASE = "Others";
    private static final String OTHER_SINGULAR = "Other";

    @Override
    public void generateInsights(ExperianReport report, BureauDataResponse bureauDataResponse) {
        if (Objects.isNull(report) || Objects.isNull(report.getCaisAccount()) ||
                Objects.isNull(report.getCaisAccount().getCaisAccountDetails())) {
            return;
        }

        CreditInsights creditInsights = new CreditInsights();

        for (CAISAccountDetails accountDetails : report.getCaisAccount().getCaisAccountDetails()) {
            List<CAISAccountHistory> accountHistoryList = accountDetails.getCaisAccountHistory();
            generateLenderWiseInsights(bureauDataResponse, accountHistoryList, accountDetails, creditInsights);
        }
        buildBureauInsights(bureauDataResponse, creditInsights, report);

    }

    public InitialUserDataResponse fetchUserInitialData(ExperianReport report) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        String emailId = Optional.ofNullable(report)
                .map(ExperianReport::getCurrentApplication)
                .map(CurrentApplication::getCurrentApplicationDetails)
                .map(CurrentApplicationDetails::getCurrentApplicantDetails)
                .map(CurrentApplicantDetails::getEmailId)
                .filter(email -> !email.isEmpty())
                .orElse(null);

        String panNumber = Optional.ofNullable(report)
                .map(ExperianReport::getCurrentApplication)
                .map(CurrentApplication::getCurrentApplicationDetails)
                .map(CurrentApplicationDetails::getCurrentApplicantDetails)
                .map(CurrentApplicantDetails::getIncomeTaxPan)
                .filter(pan -> !pan.isEmpty())
                .orElse(null);

        String employmentStatus = Optional.ofNullable(report)
                .map(ExperianReport::getCurrentApplication)
                .map(CurrentApplication::getCurrentApplicationDetails)
                .map(CurrentApplicationDetails::getCurrentOtherDetails)
                .map(CurrentOtherDetails::getEmploymentStatus)
                .filter(status -> !status.isEmpty())
                .orElse(null);

        List<CAISAccountDetails> caisAccountDetailsList = Optional.ofNullable(report)
                .map(ExperianReport::getCaisAccount)
                .map(CAISAccount::getCaisAccountDetails).orElse(new ArrayList<>());
        Optional<CAISAccountDetails> caisAccountDetailsOptional = Optional.empty();
        if(caisAccountDetailsList.size()>0){
            caisAccountDetailsOptional = caisAccountDetailsList.stream().max((o1, o2) -> {
                try {
                    return DateUtils.getDateFromString(o1.getOpenDate(), DateUtils.experianDateFormat).compareTo(DateUtils.getDateFromString(o2.getOpenDate(), DateUtils.experianDateFormat));
                } catch (ParseException | NullPointerException e) {
                    return -1;
                }
            });
        }

        if (!caisAccountDetailsOptional.isPresent()) {
            return InitialUserDataResponse.builder()
                    .email(emailId)
                    .panNumber(panNumber)
                    .employmentType(employmentStatus)
                    .build();
        }

        CAISAccountDetails caisAccountDetails = caisAccountDetailsOptional.get();

        String dateOfBirth = Optional.ofNullable(caisAccountDetails)
                .map(CAISAccountDetails::getCaisHolderDetails)
                .map(CAISHolderDetails::getDateOfBirth)
                .filter(birth -> !birth.isEmpty())
                .orElse(null);

        String gender = Optional.ofNullable(caisAccountDetails)
                .map(CAISAccountDetails::getCaisHolderDetails)
                .map(CAISHolderDetails::getGenderCode)
                .filter(g -> !g.isEmpty())
                .orElse(null);

        String firstName = Optional.ofNullable(caisAccountDetails)
                .map(CAISAccountDetails::getCaisHolderDetails)
                .map(CAISHolderDetails::getFirstName)
                .filter(f -> !f.isEmpty())
                .orElse(null);

        String lastName = Optional.ofNullable(caisAccountDetails)
                .map(CAISAccountDetails::getCaisHolderDetails)
                .map(CAISHolderDetails::getLastName)
                .filter(f -> !f.isEmpty())
                .orElse(null);

        String middleName = Optional.ofNullable(caisAccountDetails)
                .map(CAISAccountDetails::getCaisHolderDetails)
                .map(CAISHolderDetails::getMiddleName)
                .filter(f -> !f.isEmpty())
                .orElse(null);

        //todo
        CAISHolderAddressDetails caisHolderAddressDetails = Optional.ofNullable(caisAccountDetails)
                .map(CAISAccountDetails::getCaisHolderAddressDetails)
                .filter(addressDetails -> !addressDetails.isEmpty())
                .map(addressDetails -> addressDetails.get(0))
                .orElse(null);

        String addressLine1 = Optional.ofNullable(caisHolderAddressDetails)
                .map(CAISHolderAddressDetails::getFirstLineOfAddress)
                .filter(address -> !address.isEmpty())
                .orElse(null);

        String addressLine2 = Optional.ofNullable(caisHolderAddressDetails)
                .map(CAISHolderAddressDetails::getSecondLineOfAddress)
                .filter(address -> !address.isEmpty())
                .orElse(null);

        String pinCode = Optional.ofNullable(caisHolderAddressDetails)
                .map(CAISHolderAddressDetails::getZipPostalCodeOfAddress)
                .filter(zip -> !zip.isEmpty())
                .orElse(null);

        AddressDetailResponse addressDetailResponse = new AddressDetailResponse();
        addressDetailResponse.setAddressLine1(addressLine1);
        addressDetailResponse.setAddressLine2(addressLine2);
        addressDetailResponse.setPincode(pinCode);

        return InitialUserDataResponse.builder()
                .firstAndMiddleName(Stream.of(firstName,middleName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(" ")))
                .lastName(lastName)
                .email(emailId)
                .panNumber(panNumber)
                .gender(gender)
                .dateOfBirth(dateOfBirth)
                .employmentType(employmentStatus)
                .experianAddressDetails(Optional.ofNullable(caisAccountDetails.getCaisHolderAddressDetails()).orElse(Collections.emptyList()))
                .addressDetailResponse(addressDetailResponse)
                .build();
    }


    private void buildBureauInsights(BureauDataResponse bureauDataResponse, CreditInsights creditInsights, ExperianReport report) {
        if (creditInsights.getOnTimePayments() != null) {
            bureauDataResponse.setOnTimePayment(creditInsights.getOnTimePayments());
        } else {
            bureauDataResponse.setOnTimePayment(null);
        }

        if (creditInsights.getDelayedPayments() != null) {
            bureauDataResponse.setDelayedPayment(creditInsights.getDelayedPayments());
        } else {
            bureauDataResponse.setDelayedPayment(null);
        }

        if (creditInsights.getTotalPayments() != null) {
            bureauDataResponse.setTotalPayment(creditInsights.getTotalPayments());
        } else {
            bureauDataResponse.setTotalPayment(null);
        }

        if (creditInsights.getOnTimePayments() != null && creditInsights.getTotalPayments() != null && creditInsights.getTotalPayments() != 0) {
            double per = ((double) creditInsights.getOnTimePayments() / creditInsights.getTotalPayments()) * 100;
            bureauDataResponse.setOnTimePaymentPer(per);
        } else {
            bureauDataResponse.setOnTimePaymentPer(null);
        }

        String result = "";
        if (Objects.nonNull(creditInsights.getMaxCreditAge()) && creditInsights.getMaxCreditAge().getYears() == 0) {
            result = String.format("%dm", creditInsights.getMaxCreditAge().getMonths());
        } else if (Objects.nonNull(creditInsights.getMaxCreditAge())) {
            result = String.format("%dy,%dm", creditInsights.getMaxCreditAge().getYears(), creditInsights.getMaxCreditAge().getMonths());
        }
        bureauDataResponse.setCreditAge(result);

        if (creditInsights.getCreditLimit() != null) {
            bureauDataResponse.setCreditLimit(creditInsights.getCreditLimit());
        } else {
            bureauDataResponse.setCreditLimit(null);
        }

        if (creditInsights.getCurrentBalance() != null) {
            bureauDataResponse.setCurrentBalance(creditInsights.getCurrentBalance());
        } else {
            bureauDataResponse.setCurrentBalance(null);
        }

        if (creditInsights.getCurrentBalance() != null && creditInsights.getCreditLimit() != null && creditInsights.getCreditLimit() != 0) {
            double creditUsage = ((double) creditInsights.getCurrentBalance() / creditInsights.getCreditLimit()) * 100;
            bureauDataResponse.setCreditUsage(creditUsage);
        } else {
            bureauDataResponse.setCreditUsage(null);
        }

        updateCreditMix(bureauDataResponse);
        updateLast180DCreditInquiries(bureauDataResponse, report);

    }

    private void updateLast180DCreditInquiries(BureauDataResponse bureauDataResponse, ExperianReport report) {
        bureauDataResponse.setLast180DaysCreditEnquiries(report.getTotalCAPSSummary().getTotalCAPSLast180Days());
    }

    private void generateLenderWiseInsights(BureauDataResponse bureauDataResponse, List<CAISAccountHistory> accountHistoryList, CAISAccountDetails accountDetails,
                                            CreditInsights creditInsights) {
        bureauLender lender = new bureauLender();
        int lenderOntimePayment = 0;
        int lenderDelayedPayment = 0;
        int lenderTotalPayment = 0;

        for (CAISAccountHistory history : accountHistoryList) {
            lenderTotalPayment++;
            if (history.getDaysPastDue() != null && history.getDaysPastDue() > 0) {
                lenderDelayedPayment++;
            } else {
                lenderOntimePayment++;
            }
        }

        lender.setAccountType(Integer.parseInt(accountDetails.getAccountType()));
        lender.setDelayedPayment(lenderDelayedPayment);
        lender.setTotalPayment(lenderTotalPayment);
        lender.setOnTimePayment(lenderOntimePayment);
        double per = ((double) lenderOntimePayment / lenderTotalPayment) * 100;
        lender.setOnTimePaymentPer(per);

        lender.setName(accountDetails.getSubscriberName());

        // Initialize or increment CreditInsights values
        Integer totalPayments = creditInsights.getTotalPayments();
        Integer onTimePayments = creditInsights.getOnTimePayments();
        Integer delayedPayments = creditInsights.getDelayedPayments();

        creditInsights.setTotalPayments((totalPayments != null ? totalPayments : 0) + lenderTotalPayment);
        creditInsights.setOnTimePayments((onTimePayments != null ? onTimePayments : 0) + lenderOntimePayment);
        creditInsights.setDelayedPayments((delayedPayments != null ? delayedPayments : 0) + lenderDelayedPayment);

        updateLenderCreditAge(lender, accountDetails, creditInsights);
        updateLenderCreditUsage(lender, accountDetails, creditInsights);
        updateAccountTypeMap(bureauDataResponse, accountDetails);
        bureauDataResponse.getLenders().add(lender);
    }

    private void updateCreditMix(BureauDataResponse bureauDataResponse) {
        try {
            int cardNum = 0;
            int loanNum = 0;
            int othersNum = 0;

            for (AccountType accountType : bureauDataResponse.getAccountTypeMap().values()) {
                if (accountType.getName().contains("CARD")) {
                    cardNum += accountType.getCount();
                } else if (accountType.getName().contains("LOAN")) {
                    loanNum += accountType.getCount();
                } else {
                    othersNum += accountType.getCount();
                }
            }

            StringBuilder creditMix = new StringBuilder();
            String cardS = cardNum > 1 ? CARD_CATEGORY_PROPERCASE : CARD_SINGULAR;
            String loanS = loanNum > 1 ? LOAN_CATEGORY_PROPERCASE : LOAN_SINGULAR;
            String otherS = othersNum > 1 ? OTHER_CATEGORY_PROPERCASE : OTHER_SINGULAR;

            if (cardNum > 0 && loanNum > 0) {
                if (cardNum >= loanNum) {
                    creditMix.append(cardNum).append(" ").append(cardS).append(" ")
                            .append(loanNum).append(" ").append(loanS);
                } else {
                    creditMix.append(loanNum).append(" ").append(loanS).append(" ")
                            .append(cardNum).append(" ").append(cardS);
                }
            } else if (cardNum > 0) {
                creditMix.append(cardNum).append(" ").append(cardS);
                if (othersNum > 0){
                        creditMix.append(" ").append(othersNum).append(" ").append(otherS);
                }
            } else if (loanNum > 0) {
                creditMix.append(loanNum).append(" ").append(loanS);
                if (othersNum > 0){
                    creditMix.append(" ").append(othersNum).append(" ").append(otherS);
                }
            } else if(othersNum > 0) {
                creditMix.append(othersNum).append(" ").append(otherS);
            }

            bureauDataResponse.setCreditMix(creditMix.toString());

            for (bureauLender lender : bureauDataResponse.getLenders()) {
                int accType = lender.getAccountType();
                lender.setCreditMix(AccountTypeToLoanMapping.loanMap.get(accType));
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateAccountTypeMap(BureauDataResponse bureauDataResponse, CAISAccountDetails accountDetails) {
        try {
            if (bureauDataResponse.getAccountTypeMap().containsKey(accountDetails.getAccountType())) {
                AccountType accountType = bureauDataResponse.getAccountTypeMap().get(accountDetails.getAccountType());
                accountType.setCount(accountType.getCount() + 1);
            } else {
                AccountType accountType = new AccountType();
                accountType.setName(AccountTypeToLoanMapping.loanMap.get(accountDetails.getAccountType()));
                accountType.setCount(accountType.getCount() + 1);
                bureauDataResponse.getAccountTypeMap().put(accountDetails.getAccountType(), accountType);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateLenderCreditAge(bureauLender lender, CAISAccountDetails accountDetails, CreditInsights creditInsights) {
        try {
            String openDateStr = accountDetails.getOpenDate();
            if (StringUtils.isEmpty(openDateStr)) {
                return;
            }
            LocalDate openDate = LocalDate.parse(openDateStr, DateTimeFormatter.BASIC_ISO_DATE);
            lender.setOpenDate(accountDetails.getOpenDate());
            LocalDate closeDate = LocalDate.now();
            if (Objects.isNull(accountDetails.getDateClosed()) || StringUtils.isBlank(accountDetails.getDateClosed())) {
                lender.setActive(true);
            } else {
                String closeDateStr = accountDetails.getDateClosed();
                closeDate = LocalDate.parse(closeDateStr, DateTimeFormatter.BASIC_ISO_DATE);
            }
            Period period = Period.between(openDate, closeDate);
            if (creditInsights.getMaxCreditAge() == null || period.getYears() > creditInsights.getMaxCreditAge().getYears() ||
                    (period.getYears() == creditInsights.getMaxCreditAge().getYears() && period.getMonths() > creditInsights.getMaxCreditAge().getMonths())) {
                creditInsights.setMaxCreditAge(period);
            }
            String result = "";
            if (period.getYears() == 0) {
                result = String.format("%dm", period.getMonths());
            } else {
                result = String.format("%dy,%dm", period.getYears(), period.getMonths());
            }
            lender.setCreditAge(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateLenderCreditUsage(bureauLender lender, CAISAccountDetails accountDetails, CreditInsights creditInsights) {
        try {
            if (Objects.isNull(accountDetails.getAccountType()) || !accountDetails.getAccountType().equals("10") ||
                    Objects.isNull(accountDetails.getCurrentBalance()) ||
                    (Objects.isNull(accountDetails.getCreditLimitAmount()) && Objects.isNull(accountDetails.getHighestCreditOrOriginalLoanAmount()))) {
                return;
            }
            int numr = Integer.parseInt(accountDetails.getCurrentBalance());
            Integer currentBalance = creditInsights.getCurrentBalance();
            if (lender.isActive()) {
                creditInsights.setCurrentBalance((currentBalance != null ? currentBalance : 0) + numr);
            }
            lender.setCurrentBalance(String.valueOf(numr));


            int denr = 0;
            if (StringUtils.isNotEmpty(accountDetails.getCreditLimitAmount())) {
                denr = Integer.parseInt(accountDetails.getCreditLimitAmount());
                Integer creditLimit = creditInsights.getCreditLimit();
                if (lender.isActive()){
                    creditInsights.setCreditLimit((creditLimit != null ? creditLimit : 0) + denr);
                }
                lender.setCreditLimit(String.valueOf(denr));
            } else if (accountDetails.getHighestCreditOrOriginalLoanAmount() != null) {
                denr = Integer.parseInt(accountDetails.getHighestCreditOrOriginalLoanAmount());
                Integer creditLimit = creditInsights.getCreditLimit();
                if (lender.isActive()){
                    creditInsights.setCreditLimit((creditLimit != null ? creditLimit : 0) + denr);
                }
                lender.setHighestCreditOrOriginalLoanAmount(String.valueOf(denr));
            }

            if (numr == 0 && denr == 0) {
                return;
            }

            double creditUsagePer = ((double) numr / denr) * 100;
            lender.setCreditUsage(creditUsagePer);


        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
