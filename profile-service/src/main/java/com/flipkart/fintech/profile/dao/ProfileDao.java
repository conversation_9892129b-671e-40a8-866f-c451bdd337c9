package com.flipkart.fintech.profile.dao;

import com.flipkart.fintech.profile.model.Profile;

import java.util.List;

public interface ProfileDao {

    Profile getAll(Long profileId);

    Profile saveOrUpdate(Profile profile);

    Profile getProfileByUserIdPan(String userId,String pan);

    Profile getAllByUserId(String userId,String smUserId);
    List<Profile> getProfileBySMUserIdPan(String smUserId, String pan);
    Profile getProfileBySmUserId(final String smUserId);
}
