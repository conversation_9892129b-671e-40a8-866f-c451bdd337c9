package com.flipkart.fintech.profile.dao;

import com.flipkart.fintech.profile.model.BasicDetails;
import com.flipkart.fintech.profile.model.EmploymentDetails;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.SessionFactory;

public class EmploymentDetailsDao extends AbstractDAO<com.flipkart.fintech.profile.model.EmploymentDetails> {
    public EmploymentDetailsDao(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    public EmploymentDetails saveOrUpdate(EmploymentDetails employmentDetails){
        return persist(employmentDetails);
    }
}
