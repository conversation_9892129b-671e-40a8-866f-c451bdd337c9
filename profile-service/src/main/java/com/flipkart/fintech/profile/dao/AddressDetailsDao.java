package com.flipkart.fintech.profile.dao;

import com.flipkart.fintech.profile.model.AddressDetails;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.SessionFactory;

public class AddressDetailsDao extends AbstractDAO<AddressDetails> {
    public AddressDetailsDao(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    public AddressDetails saveOrUpdate(AddressDetails addressDetails){
        return persist(addressDetails);
    }
}
