package com.flipkart.fintech.profile.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


import com.flipkart.fintech.pandora.client.CheckBureauScoreClient;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.profile.Decrypter.Decrypter;
import com.flipkart.fintech.profile.api.response.BureauResponse;
import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.profile.config.ExperianConfig;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.user.data.client.UserDataClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

public class RefreshCreditScoreTest {

    private BureauDataManagerImpl bureauDataManager;

    private BureauDataService bureauDataService;
    private BureauInsights bureauInsights;

    @Mock
    private ProfileServiceConfig profileServiceConfig;
    private CheckBureauScoreClient checkBureauScoreClient;

    private final String smUserId = "smUser123";
    private final String merchantUserId = "merchant456";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Mock all required dependencies
        ProfileService profileService = mock(ProfileService.class);
        checkBureauScoreClient = mock(CheckBureauScoreClient.class);
        bureauDataService = mock(BureauDataService.class);
        ExperianFeatureService experianFeatureService = mock(ExperianFeatureService.class);
        ReportFeatureCalculator reportFeatureCalculator = mock(ReportFeatureCalculator.class);
        bureauInsights = mock(BureauInsights.class);
        UserDataClient userDataClient = mock(UserDataClient.class);
        Decrypter decrypter = mock(Decrypter.class);

        // Create instance with all dependencies
        bureauDataManager = new BureauDataManagerImpl(
                profileService,
                checkBureauScoreClient,
                bureauDataService,
                experianFeatureService,
                reportFeatureCalculator,
                bureauInsights,
                userDataClient,
                profileServiceConfig,
                decrypter
        );
    }

    @Test
    public void testRefreshBureauDataSm_WhenValidBureauDataExists_ReturnsNonNull() {
        String html = "mocked-raw-html";

        BureauDataForInsight expected = mock(BureauDataForInsight.class);
        when(expected.getReport()).thenReturn(html);

        BureauResponse bureauResponse = mock(BureauResponse.class);
        when(bureauResponse.getErrorMsg()).thenReturn(null);
        when(bureauResponse.getRawData()).thenReturn(html);

        when(checkBureauScoreClient.getCreditReportByExperian(any())).thenReturn(bureauResponse);

        Consent consent = new Consent();
        long sixtyDaysAgo = System.currentTimeMillis() - (20L * 24 * 60 * 60 * 1000);
        consent.setTs(sixtyDaysAgo);
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        experianBureauDataDto.setConsent(consent);
        experianBureauDataDto.getExperianBureauResponse().setCreatedAt(new Date(125, 1, 9));
        experianBureauDataDto.setId("dummyId");
        experianBureauDataDto.getExperianBureauResponse().setShowHtmlReportForCreditReport("<html></html>");
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);

        ExperianConfig experianConfig = new ExperianConfig();
        experianConfig.setTtl(40L * 24 * 60 * 60 * 1000);
        when(profileServiceConfig.getExperianConfig()).thenReturn(experianConfig);


        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNotNull(result);
        assertEquals(html, result.getReport());
    }

    @Test
    public void testRefreshBureauDataSm_WhenBureauDataIsNull_ReturnsNull() {
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(null);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNull(result);
    }

    @Test
    public void testRefreshBureauDataSm_WhenExperianResponseIsNull_ReturnsNull() {
        ExperianBureauDataDto experianBureauDataDto = null;
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNull(result);
    }

    @Test
    public void testRefreshBureauDataSm_WhenHtmlReportIsNull_ReturnsNull() {
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse());
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNull(result);
    }

}
