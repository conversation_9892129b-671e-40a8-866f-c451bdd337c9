package com.flipkart.fintech.profile.service;

import com.flipkart.fintech.profile.dao.ProfileDaoImpl;
import com.flipkart.fintech.profile.model.Profile;
import com.flipkart.fintech.profile.request.ProfileRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProfileServiceTest {

    @Mock
    ProfileDaoImpl profileDao;

    ProfileServiceImpl profileService;

    @Before
    public void setUp() {
         profileService =
                new ProfileServiceImpl(null, null, null,
                        profileDao, null, null, null, null);
    }

    @Test
    public void testProfileGetUserBySmUserIdAndPanWithMultipleProfiles(){
        Profile profile1 = Profile.builder().profileId(1L).pan("123").smUserId("SMA").userId("ABC").build();

        Profile profile2 = Profile.builder().profileId(2L).pan("123").smUserId("SMA").userId("ABC1").build();

        ProfileRequest profileRequest = ProfileRequest.builder().smUserId("SMA").pan("123").build();
        List<Profile> profiles = Arrays.asList(profile1, profile2);
        when(profileDao.getProfileBySMUserIdPan("SMA", "123")).thenReturn(profiles);

        Profile profile = profileService.getProfileByRequest(profileRequest);
        assertEquals(1L, profile.getProfileId().longValue());
    }

    @Test
    public void testProfileGetUserBySmUserIdAndPanWithSingleProfile(){
        Profile profile1 = Profile.builder().profileId(1L).pan("123").smUserId("SMA").userId("ABC").build();


        ProfileRequest profileRequest = ProfileRequest.builder().smUserId("SMA").pan("123").build();
        List<Profile> profiles = Collections.singletonList(profile1);
        when(profileDao.getProfileBySMUserIdPan("SMA", "123")).thenReturn(profiles);

        Profile profile = profileService.getProfileByRequest(profileRequest);
        assertEquals(1L, profile.getProfileId().longValue());
    }

    @Test
    public void testProfileGetUserBySmUserIdAndPanNoProfile(){
        Profile profile1 = Profile.builder().profileId(1L).pan("123").smUserId("SMA").userId("ABC").build();
        ProfileRequest profileRequest = ProfileRequest.builder().smUserId("SMA").pan("123").userId("ABC").build();
        when(profileDao.getProfileBySMUserIdPan("SMA", "123")).thenReturn(Collections.emptyList());
        when(profileDao.getProfileByUserIdPan("ABC", "123")).thenReturn(profile1);
        Profile profile = profileService.getProfileByRequest(profileRequest);
        assertEquals(1L, profile.getProfileId().longValue());
    }
}
