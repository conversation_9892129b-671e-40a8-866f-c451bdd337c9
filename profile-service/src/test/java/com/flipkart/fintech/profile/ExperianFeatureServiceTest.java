package com.flipkart.fintech.profile;

import com.flipkart.fintech.pinaka.library.*;
import com.flipkart.fintech.profile.eventPulisher.BQEventPublisher;
import com.flipkart.fintech.profile.model.ReportDetails;
import com.flipkart.fintech.profile.pubsub.CreditScorePublisher;
import com.flipkart.fintech.profile.service.ExperianFeatureService;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.crisys.client.CrisysClient;

import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.*;
import java.util.concurrent.*;

import static org.mockito.Mockito.*;

public class ExperianFeatureServiceTest {

    @Mock
    private ReportFeatureCalculator reportFeatureCalculator;
    @Mock
    private CrisysClient crisysClient;
    @Mock
    private ExecutorService executorService;
    @Mock
    private BQEventPublisher bqEventPublisher;
    @Mock
    private CreditScorePublisher creditScorePublisher;
    @Mock
    private DynamicBucket dynamicBucket;

    @InjectMocks
    private ExperianFeatureService experianFeatureService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        experianFeatureService = new ExperianFeatureService(
                reportFeatureCalculator,
                crisysClient,
                executorService,
                bqEventPublisher,
                creditScorePublisher,
                dynamicBucket
        );
    }

    @Test
    public void testPublishEventToPubSubIsCalledWhenFlagIsTrue() throws Exception {
        String xmlReport = "&lt;report&gt;&lt;/report&gt;";
        ReportDetails reportDetails = ReportDetails
                .builder()
                .smUserId("user123")
                .merchantUserId("merchant123")
                .source("CREDIT_SCORE")
                .profileId("profile123")
                .id("report123")
                .build();

        FeatureReport mockFeatureReport = new FeatureReport();
        mockFeatureReport.featureMap = Collections.singletonMap(Feature.EXP_SCORE, "750");
        mockFeatureReport.reportDateInYyyyMmDd = "20240501";

        when(dynamicBucket.getBoolean("publishCreditScoreEvent")).thenReturn(true);
        when(reportFeatureCalculator.calculateFeatureScore(any(), any(), isNull())).thenReturn(mockFeatureReport);
        when(executorService.submit(any(Runnable.class))).thenReturn(mock(Future.class));
        ExperianFeatureService spyService = Mockito.spy(experianFeatureService);

        doNothing().when(spyService).publishEventToPubSub("user123");

        spyService.createAndStoreExperianFeature(xmlReport, reportDetails, null, 50000);

        Thread.sleep(100);
        verify(spyService, times(1)).publishEventToPubSub("user123");
    }

}
