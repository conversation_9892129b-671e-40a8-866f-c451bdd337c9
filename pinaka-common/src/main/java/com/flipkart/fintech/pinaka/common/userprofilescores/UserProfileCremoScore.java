package com.flipkart.fintech.pinaka.common.userprofilescores;

import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.google.inject.ImplementedBy;

@ImplementedBy(UserProfileCremoScoreImpl.class)
public interface UserProfileCremoScore {

    Double getUserCremoScore(MerchantUser merchantUser, String requestId);

    Double getCremoScoreByUserProfile(UserProfileResponseV3 userProfile, FetchUserProfileResponse userProfileV2);
}
