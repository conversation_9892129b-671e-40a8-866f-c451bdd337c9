package com.flipkart.fintech.pinaka.common.userprofilescores;

import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileRequest;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.request.v3.UserProfileFetchRequestV3;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.alfredclient.AlfredClient;
import com.flipkart.fintech.pinaka.common.constants.AlfredConstants;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClient;
import com.flipkart.fintech.pinaka.common.utils.UserProfileInsightsUtils;
import com.flipkart.fintech.profile.common.DynamicConfigHelper;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.kloud.config.DynamicBucket;
import com.codahale.metrics.Timer;
import com.google.inject.Inject;
import com.sumo.crisys.api.CremoScores;
import com.sumo.crisys.client.CrisysClient;
import com.sumo.crisys.client.CrisysClientException;
import com.sumo.crisys.response.CreditProfileResponse;
import com.sumo.crisys.response.CremoScoresResponse;
import lombok.CustomLog;

import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;

import static com.flipkart.fintech.pinaka.common.constants.AlfredConstants.CRYSIS_AXIS_MODEL_VERSION;

@CustomLog
public class UserProfileScoresImpl implements UserProfileScores {
    private final AlfredClient alfredClient;
    private final DexterClient dexterClient;
    private final CrisysClient crisysClient;
    private final DynamicBucket dynamicBucket;

    @Inject
    public UserProfileScoresImpl(AlfredClient alfredClient, DexterClient dexterClient, CrisysClient crisysClient, DynamicBucket dynamicBucket) {
        this.alfredClient = alfredClient;
        this.dexterClient = dexterClient;
        this.crisysClient = crisysClient;
        this.dynamicBucket = dynamicBucket;
    }

    public UserProfileResponseV3 getUserProfile(MerchantUser merchantUser) {
        try(Timer.Context timer=PinakaMetricRegistry.getMetricRegistry().timer("getUserProfile").time()){
            UserProfileFetchRequestV3 userProfileFetchRequestV3 = new UserProfileFetchRequestV3();
            userProfileFetchRequestV3.setVersion(AlfredConstants.ALFRED_CLIENT_VERSION);
            userProfileFetchRequestV3.setAccountId(merchantUser.getMerchantUserId());
            return alfredClient.fetchUserProfile(
                    userProfileFetchRequestV3, merchantUser.getMerchantKey(), AlfredConstants.ALFRED_CLIENT_VERSION
            );
        } catch (Exception e) {
            log.error("Error while calling fetching user profile from Alfred Service for accountId {}", merchantUser.getMerchantUserId(), e);
            return null;
        }
    }

    @Override
    public FetchUserProfileResponse getUserProfileByDexter(String requestId, MerchantUser merchantUser) {
        try {
            FetchUserProfileRequest fetchUserProfileRequest = new FetchUserProfileRequest();
            fetchUserProfileRequest.setAccountId(merchantUser.getMerchantUserId());

            return dexterClient.fetchUserProfile(fetchUserProfileRequest, DexterConstants.DEXTER_CLIENT_ID, requestId);
        } catch (Exception e) {
            log.error("Error while calling fetching user profile from Dexter Service for accountId {}", merchantUser.getMerchantUserId(), e);
            return null;
        }
    }

    @Override
    public Double getUserBinScore(MerchantUser merchantUser, String requestId) {
        try {
            //TODO: Refactor once flipkart scores are available in crisys
            if (MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY.equals(merchantUser.getMerchantKey())) {
                return getBinScoreFromCrisys(merchantUser);
            }

            UserProfileResponseV3 userProfile = new UserProfileResponseV3();
            FetchUserProfileResponse userProfileV2 = new FetchUserProfileResponse();

            Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
            if(dexterFlag){
                userProfileV2 = getUserProfileByDexter(requestId, merchantUser);
                if(userProfileV2 == null) {
                    return null;
                }
            }else {
                userProfile = getUserProfile(merchantUser);
                if (userProfile == null) {
                    return null;
                }
            }


            return UserProfileInsightsUtils.getBinScore(userProfile, userProfileV2, dynamicBucket);
        } catch (Exception e) {
            PinakaMetricRegistry.getMetricRegistry().meter("binScoreFetchError").mark();
            log.error("Failed to fetch binscore for user: {}", merchantUser, e);
        }
        return null;
    }

    @Override
    public Double getUserBin(MerchantUser merchantUser, String requestId) {
        try {
            //TODO: Refactor once flipkart scores are available in crisys
            if (MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY.equals(merchantUser.getMerchantKey())) {
                return getBinFromCrisys(merchantUser);
            }
            UserProfileResponseV3 userProfile = new UserProfileResponseV3();
            FetchUserProfileResponse fetchUserProfileResponse = new FetchUserProfileResponse();

            Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
            if(dexterFlag){
                fetchUserProfileResponse = getUserProfileByDexter(requestId, merchantUser);
                if(Objects.isNull(fetchUserProfileResponse)) {
                    return null;
                }
            }else{
                userProfile = getUserProfile(merchantUser);
                if (userProfile == null) {
                    return null;
                }
            }

            return UserProfileInsightsUtils.getBin(userProfile, fetchUserProfileResponse, dynamicBucket);
        } catch (Exception e) {
            log.error("Failed to fetch binscore for user: {}", merchantUser, e);
        }
        return null;
    }

    @Override
    public CremoScores getCremoScore(MerchantUser merchantUser) throws CrisysClientException {
        CremoScoresResponse cremoScoresResponse = crisysClient.fetchCremoScores(merchantUser.getSmUserId());
        return fetchCremoBandFromResponse(cremoScoresResponse);
    }

    private CremoScores fetchCremoBandFromResponse(CremoScoresResponse cremoScoresResponse) {
        if(Objects.isNull(cremoScoresResponse.cremoScoreCollection()) || Objects.isNull(cremoScoresResponse.cremoScoreCollection().cremoScores())){
            return null;
        }
        Optional<CremoScores> cremoScores = cremoScoresResponse.cremoScoreCollection().cremoScores().stream()
                .max(Comparator.comparingLong(cremoScore -> Long.parseLong(cremoScore.version())));
        return cremoScores.orElse(null);
    }

    @Override
    public Double getUserExperianScore(MerchantUser merchantUser) {
        return null;
    }

    private Double getBinScoreFromCrisys(MerchantUser merchantUser) throws CrisysClientException {
        CreditProfileResponse creditProfileResponse = crisysClient.fetchCreditProfileByAccountId(merchantUser.getSmUserId());
        String modelVersion = DynamicConfigHelper.getString(dynamicBucket, CRYSIS_AXIS_MODEL_VERSION, "202403");
        return UserProfileInsightsUtils.getBinScore(creditProfileResponse.creditProfile(), modelVersion);
    }

    private Double getBinFromCrisys(MerchantUser merchantUser) throws CrisysClientException {
        CreditProfileResponse creditProfileResponse = crisysClient.fetchCreditProfileByAccountId(merchantUser.getSmUserId());
        String modelVersion = DynamicConfigHelper.getString(dynamicBucket, CRYSIS_AXIS_MODEL_VERSION, "202403");
        return UserProfileInsightsUtils.getBin(creditProfileResponse.creditProfile(), modelVersion);
    }
}
