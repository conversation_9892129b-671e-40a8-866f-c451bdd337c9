package com.flipkart.fintech.pinaka.models;

import com.flipkart.fintech.cryptex.annotation.EncryptedConfig;
import io.dropwizard.db.DataSourceFactory;

public class DatabaseConfig extends DataSourceFactory {
    @EncryptedConfig(key = "databaseConfigUrl")
    private String encryptedUrl;
    @EncryptedConfig(key = "databaseConfigUser")
    private String encryptedUser;
    @EncryptedConfig(key = "databaseConfigPassword")
    private String encryptedPassword;

    public String getEncryptedUrl() {
        return encryptedUrl;
    }

    public void setEncryptedUrl(String encryptedUrl) {
        super.setUrl(encryptedUrl);
        this.encryptedUrl = encryptedUrl;
    }

    public String getEncryptedUser() {
        return encryptedUser;
    }

    public void setEncryptedUser(String encryptedUser) {
        this.encryptedUser = encryptedUser;
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword;
    }
}
