package com.flipkart.fintech.pinaka.common.bigfootIngestor;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class BigfootEntityConfig {
    @JsonProperty
    private Map<String, String> borrowerConfig;

    @JsonProperty
    private Map<String, String> loanApplicationConfig;

    @JsonProperty
    private Map<String, String> pLApplicationConfig;

    @JsonProperty
    private Map<String, String> tnsScoreConfig;

    @JsonProperty
    private Map<String, String> subApplicationConfig;

    @JsonProperty
    private Map<String, String> whitelistConfig;

    @JsonProperty
    private Map<String, String> sourceAttributionConfig;

    @JsonProperty
    private Map<String, String> ebcApplicationConfig;

    @JsonProperty
    private Map<String,String> pincodeConfig;

    @JsonProperty
    private Map<String, String> revalidationApplicationConfig;

    @JsonProperty
    private Map<String, String> offerOrchestrationConfig;



}
