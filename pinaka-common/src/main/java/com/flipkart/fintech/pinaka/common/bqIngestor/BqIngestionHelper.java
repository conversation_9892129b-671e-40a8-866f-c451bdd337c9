package com.flipkart.fintech.pinaka.common.bqIngestor;

import java.util.List;

import com.google.api.core.ApiFuture;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.inject.Inject;
import com.supermoney.schema.PinakaService.ApplicationStateEventV1;
import com.supermoney.publisher.EventPublisher;
import com.supermoney.schema.PinakaService.LeadV3Events;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;


@RequiredArgsConstructor(onConstructor = @__(@Inject))
@CustomLog
public class BqIngestionHelper {
    private final EventPublisher eventPublisher;

    public void insertApplicationEvents(ApplicationStateEventV1 applicationEvent) {
        log.info(String.format("publishing event for application id: %s",
                applicationEvent.getApplicationId()));
        publishEvent(applicationEvent);
    }

    /**
     * Generic method to insert lead events for any version (V3, V4, etc.)
     * Uses LeadV3Events schema for all lead versions
     *
     * @param leadEvents Lead event data (using LeadV3Events schema generically)
     */
    public void insertLeadEvents(LeadV3Events leadEvents) {
        log.info("publishing lead event for application id: {}", leadEvents.getApplicationId());
        publishEvent(leadEvents);
    }

    /**
     * Backward compatibility method for V3
     * @deprecated Use insertLeadEvents(leadEvents) instead
     */
    @Deprecated
    public void insertLeadV3Events(LeadV3Events leadV3Events) {
        insertLeadEvents(leadV3Events);
    }

    private void publishEvent(Object event) {
        try {
            List<ApiFuture<String>> futures = eventPublisher.publishEvent(event);
            for (ApiFuture<String> future : futures) {
                ApiFutures.addCallback(future, new ApiFutureCallback<String>() {
                    public void onSuccess(String messageId) {
                        log.info("Published message with ID: {}", messageId);
                    }

                    public void onFailure(Throwable t) {
                        log.error("Failed to publish message: {}", t.getMessage());
                    }
                }, MoreExecutors.directExecutor());
            }
        } catch (Exception e) {
            log.error("Exception while publishing the event: {} to BigQuery: {}", String.valueOf(event), e.getMessage());
        }
    }

}
