package com.flipkart.fintech.pinaka.common.dexterClient;

import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileRequest;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.kloud.authn.AuthTokenService;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static org.reflections.Reflections.log;

public class Dexter<PERSON>lientImpl implements Dexter<PERSON>lient{

    public static final String APP_CONTEXT_PATH = "/dexter";
    public static final String X_CLIENT_ID = "X-Client-Id";
    public static final String X_REQUEST_ID = "X-Request-Id";
    public static final String X_TRACE_ID = "X-Trace-Id";
    public static final String X_PROFILE_ID = "X-Profile-Id";
    public static final String FETCH_USER_PROFILE = "/1/user-profile/fetch";
    private static final String TOKEN_FORMAT = "Bearer %s";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String PROFILE_ID = "sm";


    private final WebTarget fkWebTarget;
    private final AuthTokenService authTokenService;
    private final DexterClientConfig dexterClientConfig;

    @Inject
    public DexterClientImpl(Client client, AuthTokenService authTokenService, DexterClientConfig dexterClientConfig) {
        this.fkWebTarget = client.target(dexterClientConfig.getUrl());
        this.authTokenService = authTokenService;
        this.dexterClientConfig = dexterClientConfig;
    }

    @Override
    public FetchUserProfileResponse fetchUserProfile(FetchUserProfileRequest fetchUserProfileRequest, String clientId, String requestId) throws RuntimeException {
        Response response = null;
        FetchUserProfileResponse fetchUserProfileResponse = null;
        String path = String.format(APP_CONTEXT_PATH + FETCH_USER_PROFILE);
        try {
            log.info("fetching userProfileResponse for userId from dexter- {}", fetchUserProfileRequest.getAccountId());
            String token = String.format(TOKEN_FORMAT, authTokenService.fetchToken(dexterClientConfig.getTargetClientId()).getToken());
            Invocation.Builder invocationBuilder =
                    fkWebTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE).accept(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(X_CLIENT_ID, clientId);
            invocationBuilder.header(X_REQUEST_ID, requestId);
            invocationBuilder.header(AUTHORIZATION_HEADER, token);
            invocationBuilder.header(X_TRACE_ID, requestId);
            invocationBuilder.header(X_PROFILE_ID, PROFILE_ID);

            log.info("fetchUserProfileRequest request is {}", fetchUserProfileRequest);
            response = invocationBuilder.post(Entity.json(fetchUserProfileRequest));

            if (response.getStatus() != 200) {
                log.error("User profile response for acc id {} is {}", fetchUserProfileRequest.getAccountId(),
                        response.readEntity(String.class));
                throw new RuntimeException(String.format("User profile response for account id %s",
                        fetchUserProfileRequest.getAccountId()));
            }

            fetchUserProfileResponse = response.readEntity(FetchUserProfileResponse.class);
            log.info("fetchUserProfile for userId - {} is - {}", fetchUserProfileRequest.getAccountId(), fetchUserProfileResponse);
        } catch (Exception exp) {
            log.error("Error while fetching profile from dexter. account id: {}", fetchUserProfileRequest.getAccountId(), exp);
            throw new RuntimeException(exp);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return fetchUserProfileResponse;
    }
}
