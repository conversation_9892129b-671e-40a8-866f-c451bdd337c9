package com.flipkart.fintech.pinaka.common.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.client.PinakaCalvinClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.varadhi.RobinhoodEventRequest;
import com.google.inject.Inject;
import org.glassfish.jersey.media.multipart.MultiPartFeature;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static com.flipkart.fintech.pinaka.common.constants.Constants.*;

public class RobinhoodClientImpl implements RobinhoodClient {

    private WebTarget webTarget;
    private final PinakaCalvinClientConfig clientConfig;
    private final ObjectMapper objectMapper = ObjectMapperUtil.get();
    private static final String ERROR_PINAKA = "error response from pinaka service";
    @Inject
    public RobinhoodClientImpl(PinakaCalvinClientConfig clientConfig, Client client) {
        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path(APP_CONTEXT_PATH);
    }
    @Override
    public void pushEvent(RobinhoodEventRequest eventRequest) throws PinakaClientException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(ROBINHOOD_EVENT_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(X_REQUEST_ID, eventRequest.getGrpId());
            response = invocationBuilder.post(Entity.json(objectMapper.writeValueAsString(eventRequest)));
            if (response.getStatus() != 204) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }
}
