package com.flipkart.fintech.pinaka.models;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class AppVersion implements Comparable<AppVersion> {

    private final String strVersion;
    private final int major;
    private final int minor;
    private final int patch;

    public AppVersion(int major, int minor, int patch) {
        this.major = major;
        this.minor = minor;
        this.patch = patch;
        this.strVersion = StringUtils.join(major, minor, patch, '.');
    }

    @JsonCreator
    public AppVersion(String version) {
        this.strVersion = version;
        List<Integer> partsInt = parse(version);
        this.major = partsInt.get(0);
        this.minor = partsInt.get(1);
        this.patch = partsInt.get(2);
    }

    private List<Integer> parse(String version) {
        String[] parts = version.split("\\.");
        if (parts.length < 3) {
            throw new IllegalArgumentException("Version length needs to be atleast 3");
        }
        return Arrays.stream(parts).map(Integer::parseInt).collect(Collectors.toList());
    }

    @Override
    public int compareTo(@NonNull AppVersion other) {
        if (this.major != other.major) return Integer.compare(this.major, other.major);
        if (this.minor != other.minor) return Integer.compare(this.minor, other.minor);
        return Integer.compare(this.patch, other.patch);
    }
}