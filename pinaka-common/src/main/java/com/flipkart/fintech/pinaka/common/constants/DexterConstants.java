package com.flipkart.fintech.pinaka.common.constants;

public class DexterConstants {
    public static final String DEXTER_CLIENT_ID = "CALM";
    public static final String REST = "rest";
    public static final String CREMO = "cremo";
    public static final String CBC_JRM_V3_VERSION = "v3_3";
    public static final String CBC_JRM_V1_VERSION = "v1_0";
    public static final String CBC_LI_DEXTER_MODEL_NAME = "li";
    public static final String MOST_USED_ADDRESS_ID = "most_used_address_id";
    public static final String CBC_FPG_SM_INSIGHTS_DEXTER_MODEL_NAME = "fpg_sm_insights";
    public static final String MOST_USER_ADDRESS_ID_AAC = "most_used_address_id_sm_aac";
    public static final String MOST_USER_ADDRESS_ID_NON_AAC = "most_used_address_id_sm_nonaac";
    public static final String IDFC_SEG = "idfc_int_seg";
    public static final String CBC_JRM = "jrm";
    public static final String SCORE = "SCORE";
    public static final String BIN = "BIN";
    public static final String EQUAL_BIN = "EQUAL_BIN";
    public static final String BIN_SCORE = "BIN_SCORE";
    public static final String AXIS_V2 = "AXIS_V2";
    public static final String DEXTER_FLAG = "dexter_flag";

}
