package com.flipkart.fintech.pinaka.common.dexterClient;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.inject.Inject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DexterClientConfig {
    @JsonProperty(value="url")
    private String url;
    @JsonProperty(value="targetClientId")
    private String targetClientId;

}
