import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.utils.UserProfileInsightsUtils;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.InputStream;

import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class ShippingAddressIdTest {

    @Mock
    private UserProfileResponseV3 userProfile1;

    @Mock
    private UserProfileResponseV3 userProfile2;
    @Mock
    private UserProfileResponseV3 userProfile3;

    @Mock
    private UserProfileResponseV3 userProfile4;

    @Mock
    private FetchUserProfileResponse fetchUserProfile1;
    @Mock
    private FetchUserProfileResponse fetchUserProfile2;
    @Mock
    private FetchUserProfileResponse fetchUserProfile3;

    @Mock
    private DynamicBucket dynamicBucket;

    @Before
    public void setUp() throws Exception {
        InputStream inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("userProfile1.json");
        userProfile1 = ObjectMapperUtil.get().readValue(inputStream, UserProfileResponseV3.class);
        inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("userProfile2.json");
        userProfile2 = ObjectMapperUtil.get().readValue(inputStream, UserProfileResponseV3.class);
        inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("userProfile3.json");
        userProfile3 = ObjectMapperUtil.get().readValue(inputStream, UserProfileResponseV3.class);
        inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("userProfile4.json");
        userProfile4 = ObjectMapperUtil.get().readValue(inputStream, UserProfileResponseV3.class);
        inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("fetchUserProfile1.json");
        fetchUserProfile1 = ObjectMapperUtil.get().readValue(inputStream, FetchUserProfileResponse.class);
        inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("fetchUserProfile2.json");
        fetchUserProfile2 = ObjectMapperUtil.get().readValue(inputStream, FetchUserProfileResponse.class);
        inputStream = ShippingAddressIdTest.class.getClassLoader().getResourceAsStream("fetchUserProfile3.json");
        fetchUserProfile3 = ObjectMapperUtil.get().readValue(inputStream, FetchUserProfileResponse.class);

    }

    @Test
    public void getAddressCurrentFlow(){
        Mockito.when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG)).thenReturn(Boolean.FALSE);
        String shippingAddress3 = UserProfileInsightsUtils.getShippingAddressId(userProfile2, fetchUserProfile1, dynamicBucket);
        assertNotNull(shippingAddress3);
    }

    @Test
    public void FPGNonAACTest() {
        Mockito.when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG)).thenReturn(Boolean.FALSE);
        String shippingAddress1 = UserProfileInsightsUtils.getShippingAddressId(userProfile1, fetchUserProfile2, dynamicBucket);
        assertNotNull(shippingAddress1);
    }

    @Test
    public void FPGAACTest() {
        Mockito.when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG)).thenReturn(Boolean.FALSE);
        String shippingAddress2 = UserProfileInsightsUtils.getShippingAddressId(userProfile3, fetchUserProfile3, dynamicBucket);
        assertNotNull(shippingAddress2);
    }

    @Test
    public void nullTest() {
        Mockito.when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG)).thenReturn(Boolean.FALSE);
        String shippingAddress2 = UserProfileInsightsUtils.getShippingAddressId(userProfile4, fetchUserProfile1, dynamicBucket);
        assertNotNull(shippingAddress2);
    }
}
