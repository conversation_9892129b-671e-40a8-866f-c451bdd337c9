package com.flipkart.fintech.profile.pagehandler.profileservice.impl;

import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.response.idfc.WidgetEntity;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.pagehandler.FetchPageDataHandlerV2;
import com.flipkart.fintech.profile.pagehandler.handlers.PageHandlerV2;
import lombok.CustomLog;

import java.util.List;
import java.util.Objects;

@CustomLog
public class FetchPageDataHandlerV2Impl implements FetchPageDataHandlerV2 {
    @Override
    public FetchBulkDataResponseV2 fetchBulkDataV3(PageServiceRequest pageServiceRequest) throws PinakaEx<PERSON>, PinakaClientException {
        List<WidgetEntity> pageResponse = null;
        return new FetchBulkDataResponseV2();
    }
}
