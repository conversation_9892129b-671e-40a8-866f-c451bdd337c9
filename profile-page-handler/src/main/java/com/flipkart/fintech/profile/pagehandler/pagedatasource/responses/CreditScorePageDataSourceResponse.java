package com.flipkart.fintech.profile.pagehandler.pagedatasource.responses;

import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import lombok.Data;

import java.util.Map;

@Data
public class CreditScorePageDataSourceResponse extends Value {
    private Map<String, Object> queryParams;
    private BureauDataResponse bureauDataResponse;
}
