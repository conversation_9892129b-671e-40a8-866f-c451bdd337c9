package com.flipkart.fintech.profile.pagehandler.widgettransformer;

import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.decrypter.DecrypterImpl;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.BureauDetailsPageDataSourceResponse;
import com.flipkart.fintech.profile.pagehandler.utils.TransformerUtils;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.insurtech.TextBoxFormFieldValueV0;
import com.flipkart.rome.datatypes.response.fintech.onboarding.DateFormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@CustomLog
public class CreditScoreDetailsFormTransformer {

    @Inject
    public CreditScoreDetailsFormTransformer(){
    }

    private static String genericFormJson;
    private static String announcementFormJson;
    static {
        genericFormJson = TransformerUtils.readFileasString("template/bureau/CreditDetailsForm.json");
        announcementFormJson = TransformerUtils.readFileasString("template/bureau/CreditScoreAnnouncement.json");
    }

    public GenericFormWidgetData buildWidgetData(String accountId, String smAccountId,BureauDetailsPageDataSourceResponse bureauDetailsPageDataSourceResponse) throws PinakaClientException {
        try {
            GenericFormWidgetData creditScoreFormWidget = ObjectMapperUtil.get().readValue(genericFormJson, GenericFormWidgetData.class);
            updateValidations(creditScoreFormWidget);
            updateBasicDetails(creditScoreFormWidget, bureauDetailsPageDataSourceResponse);
            return creditScoreFormWidget;
        } catch (Exception e) {
            log.error("Credit Score build Widget Data failed with error : {}", e.getMessage());
            throw new PinakaClientException(e);
        }
    }

    public AnnouncementV2WidgetData buildAnnouncementWidgetData(){
        try {
            AnnouncementV2WidgetData announcementV2WidgetData = ObjectMapperUtil.get().readValue(announcementFormJson, AnnouncementV2WidgetData.class);
            return announcementV2WidgetData;
        } catch (Exception e) {
            log.error("AadharForm build Widget Data failed with error : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void updateValidations(GenericFormWidgetData creditScoreFormWidget) {
        updateMaxMinDateInCreditScoreForm(creditScoreFormWidget);
    }

    private void updateMaxMinDateInCreditScoreForm(GenericFormWidgetData creditScoreFormWidget){
        DateFormFieldValue dateFormFieldValue = (DateFormFieldValue) creditScoreFormWidget.getRenderableComponents().get(2).getValue();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime tenYearsAgo = now.minusYears(10);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        String formattedDate = tenYearsAgo.format(formatter);
        dateFormFieldValue.setMaxValue(formattedDate);
    }

    private void updateBasicDetails(GenericFormWidgetData creditScoreFormWidget, BureauDetailsPageDataSourceResponse bureauDetailsPageDataSourceResponse){
        ProfileDetailedResponse profile  = bureauDetailsPageDataSourceResponse.getProfile();
        if (Objects.nonNull(profile)){
            updatePhoneNum(creditScoreFormWidget,profile);
            updateDob(creditScoreFormWidget, profile);
            updateEmail(creditScoreFormWidget, profile);
            updateFirstName(creditScoreFormWidget, profile);
            updateLastName(creditScoreFormWidget, profile);
            updatePan(creditScoreFormWidget, profile);
        }
        updateSubmitButton(creditScoreFormWidget, bureauDetailsPageDataSourceResponse);
    }
    private static void updatePhoneNum(GenericFormWidgetData creditScoreFormWidget, ProfileDetailedResponse profile){
        if(Objects.nonNull(profile.getPhoneNo())){
            TextBoxFormFieldValueV0 TextBoxFormFieldValueV0 = (TextBoxFormFieldValueV0) creditScoreFormWidget.getRenderableComponents().get(0).getValue();
            TextBoxFormFieldValueV0.setValue(profile.getPhoneNo());
        }
    }
    private static void updatePan(GenericFormWidgetData creditScoreFormWidget, ProfileDetailedResponse profile){
        TextBoxFormFieldValueV0 TextBoxFormFieldValueV0 = (TextBoxFormFieldValueV0) creditScoreFormWidget.getRenderableComponents().get(1).getValue();
        if(Objects.nonNull(profile.getPan())){
            if(profile.getPan().length()>10) {
                TextBoxFormFieldValueV0.setValue(new DecrypterImpl().decryptString(profile.getPan()));
            }
            else{
                TextBoxFormFieldValueV0.setValue(profile.getPan());
            }
        }
    }
    private static void updateFirstName(GenericFormWidgetData creditScoreFormWidget, ProfileDetailedResponse profile){
        if(Objects.nonNull(profile.getFirstName())){
            TextBoxFormFieldValueV0 TextBoxFormFieldValueV0 = (TextBoxFormFieldValueV0) creditScoreFormWidget.getRenderableComponents().get(3).getValue();
            if(!StringUtils.isBlank(profile.getFirstName())){
                TextBoxFormFieldValueV0.setValue(new DecrypterImpl().decryptString(profile.getFirstName()));
            }
        }
    }
    private static void updateLastName(GenericFormWidgetData creditScoreFormWidget, ProfileDetailedResponse profile){
        if(Objects.nonNull(profile.getLastName())){
            TextBoxFormFieldValueV0 TextBoxFormFieldValueV0 = (TextBoxFormFieldValueV0) creditScoreFormWidget.getRenderableComponents().get(4).getValue();
            if(!StringUtils.isBlank(profile.getLastName())){
                TextBoxFormFieldValueV0.setValue(new DecrypterImpl().decryptString(profile.getLastName()));
            }
        }
    }
    private static void updateEmail(GenericFormWidgetData creditScoreFormWidget, ProfileDetailedResponse profile){
        if(Objects.nonNull(profile.getEmail())){
            TextBoxFormFieldValueV0 TextBoxFormFieldValueV0 = (TextBoxFormFieldValueV0) creditScoreFormWidget.getRenderableComponents().get(5).getValue();
            TextBoxFormFieldValueV0.setValue(new DecrypterImpl().decryptString(profile.getEmail()));
        }
    }
    private static void updateDob(GenericFormWidgetData creditScoreFormWidget, ProfileDetailedResponse profile){
        DateFormFieldValue dateFormFieldValue = (DateFormFieldValue) creditScoreFormWidget.getRenderableComponents().get(2).getValue();
        if (Objects.nonNull(profile.getDob())) {
            if (profile.getDob().length() > 10) {
                dateFormFieldValue.setValue(new DecrypterImpl().decryptString(profile.getDob()));
            } else {
                dateFormFieldValue.setValue(profile.getDob());
            }
        }
    }
    private static void updateSubmitButton(GenericFormWidgetData creditScoreFormWidget, BureauDetailsPageDataSourceResponse bureauDetailsPageDataSourceResponse){
       Action action = creditScoreFormWidget.getSubmitButton().getButton().getAction();
       action.setEncryption(bureauDetailsPageDataSourceResponse.getEncryptionData());
    }

}
