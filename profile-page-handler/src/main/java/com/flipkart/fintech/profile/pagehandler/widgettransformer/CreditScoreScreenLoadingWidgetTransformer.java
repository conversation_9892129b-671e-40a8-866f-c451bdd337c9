package com.flipkart.fintech.profile.pagehandler.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.CreditScoreWaitScreenDataSourceResponse;
import com.flipkart.fintech.profile.pagehandler.utils.TransformerUtils;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.LoadingWidgetDataV0;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

public class CreditScoreScreenLoadingWidgetTransformer {
    public LoadingWidgetDataV0 buildWidgetData(CreditScoreWaitScreenDataSourceResponse creditScoreWaitScreenDataSourceResponse)
            throws JsonProcessingException {
        String loadingWidgetJson = TransformerUtils.readFileasString("template/bureau/CreditScoreLoading.json");
        LoadingWidgetDataV0 loadingWidgetData =  ObjectMapperUtil.get().readValue(loadingWidgetJson, LoadingWidgetDataV0.class);
        loadingWidgetData.getPollingContext().getAction().setParams(creditScoreWaitScreenDataSourceResponse.getQueryParams());
        return loadingWidgetData;
    }
}
