package com.flipkart.fintech.profile.pagehandler;

import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.pagehandler.profileservice.impl.FetchPageDataHandlerV2Impl;
import com.google.inject.ImplementedBy;

@ImplementedBy(FetchPageDataHandlerV2Impl.class)
public interface FetchPageDataHandlerV2 {
    FetchBulkDataResponseV2 fetchBulkDataV3(PageServiceRequest pageServiceRequest)
            throws PinakaException, PinakaClientException;
}
