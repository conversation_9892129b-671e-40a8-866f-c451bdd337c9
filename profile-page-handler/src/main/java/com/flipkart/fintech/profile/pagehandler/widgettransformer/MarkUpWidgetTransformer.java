package com.flipkart.fintech.profile.pagehandler.widgettransformer;

import com.flipkart.fintech.profile.pagehandler.utils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.MarkupValue;
import com.flipkart.rome.datatypes.response.page.v4.mapiWidgetData.MarkupWidgetData;

import java.util.Collections;
import java.util.Map;

public class MarkUpWidgetTransformer {
    private static final Map<String, String> staticMarkupWidgetData;

    static {
        staticMarkupWidgetData = TransformerUtils.readFileAsMapOfString("staticwidgets/markupWidgets.json");
    }

    public MarkupWidgetData buildMarkUpWidgetData(String dataKey) {
        String markupData = staticMarkupWidgetData.get(dataKey);

        MarkupWidgetData markupWidgetData = new MarkupWidgetData();
        MarkupValue markupValue = new MarkupValue();
        markupValue.setContent(markupData);

        RenderableComponent<MarkupValue> value = new RenderableComponent<>();
        value.setValue(markupValue);
        markupWidgetData.setRenderableComponents(Collections.singletonList(value));

        return markupWidgetData;
    }
}
