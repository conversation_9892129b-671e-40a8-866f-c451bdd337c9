package com.flipkart.fintech.profile.pagehandler.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class ExperianUtils {
    public static long getRefreshButtonTimeDiff(String inputDate){
        SimpleDateFormat format = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
        format.setTimeZone(TimeZone.getTimeZone("IST")); // Set the time zone

        Date date = null;
        try {
            date = format.parse(inputDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        long currentTime = System.currentTimeMillis();
        long inputTime = date.getTime();
        long differenceInMinutes = (currentTime - inputTime) / (1000 * 60);
        return differenceInMinutes;
    }
}
