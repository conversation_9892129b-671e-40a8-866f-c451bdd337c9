CREATE TABLE lender_offer (
                              id VARCHAR(20) NOT NULL,
                              user_id BIGINT NOT NULL,
                              user_profile_id BIGINT,
                              lender VARCHAR(50) NOT NULL,
                              offer_state VARCHAR(50) NOT NULL,
                              offer_type VARCHAR(50) NOT NULL,
                              offer_details VARCHAR(3200),
                              created_at_ms BIGINT NOT NULL,
                              updated_at_ms BIGINT NOT NULL,
                              PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE lender_pincode_serviceability (
                                               id bigint unsigned NOT NULL,
                                               pincode VARCHAR(10) NOT NULL,
                                               lender VARCHAR(50) NOT NULL,
                                               active BOOLEAN NOT NULL,
                                               created_at_ms BIGINT NOT NULL,
                                               updated_at_ms BIGINT NOT NULL,
                                               PRIMARY KEY (id)
);
CREATE INDEX idx_pincode ON lender_pincode_serviceability(pincode);
CREATE INDEX idx_pincode_lender ON lender_pincode_serviceability(pincode, lender);
CREATE TABLE user_profile_cohort (
                                     id BIGINT AUTO_INCREMENT PRIMARY KEY,
                                     name VARCHAR(255) NOT NULL,
                                     active BOOLEAN NOT NULL,
                                     version VARCHAR(10),
                                     description VARCHAR(3200)
);

CREATE TABLE blacklisted_users (
                                   id BIGINT PRIMARY KEY,
                                   user_id VARCHAR(127),
                                   active BOOLEAN,
                                   created_at_ms BIGINT,
                                   updated_at_ms BIGINT,
                                   INDEX idx_user_id (user_id)
);