package com.flipkart.fintech.offer.orchestrator.dao;

import com.flipkart.fintech.offer.orchestrator.model.LenderPincodeServiceabilityEntity;
import com.flipkart.fintech.pinaka.api.enums.Lender;


import java.util.List;

public interface LenderPincodeServiceabilityDao {

    public List<LenderPincodeServiceabilityEntity> getAllActive(String pincode);

    public LenderPincodeServiceabilityEntity getActive(Lender lender, String pincode);

}
