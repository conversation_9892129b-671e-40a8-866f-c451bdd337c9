package com.flipkart.fintech.offer.orchestrator.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.offer.orchestrator.model.temp.BorrowerEntityNew;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import static com.flipkart.fintech.offer.orchestrator.model.Utility.generateHashedId;


@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LenderOfferEntity {

    private String id;

    private String userId;

    private String userProfileId;

    private Lender lender;

    private LenderOfferState status;

    private LenderOfferType offerType;

    private String offerDetails;

    private Long createdAtMS;

    private Long updatedAtMS;

    private String metadata;

    private Long amount;
    private Double roi;


    public LenderOfferEntity(String userId, String userProfileId, Lender lender, LenderOfferState status,
                             LenderOfferType offerType) {
        this.id = generateHashedId("LO");
        this.userId = userId;
        this.userProfileId = userProfileId;
        this.lender = lender;
        this.status = status;
        this.offerType = offerType;
    }

    public LenderOfferEntity(BorrowerEntityNew borrowerEntityNew) {
        this.id = String.valueOf(borrowerEntityNew.getId());
        this.userId = borrowerEntityNew.getExternalId();
        this.offerDetails = borrowerEntityNew.getMetadata();
        this.status = mapEnabledToOfferState(borrowerEntityNew.isEnabled());
        this.lender = Lender.valueOf(borrowerEntityNew.getWhitelist().getLender());
        this.metadata = borrowerEntityNew.getMetadata();
        this.offerType = LenderOfferType.PRE_APPROVED;
    }

    public LenderOfferEntity() {

    }

    private LenderOfferState mapEnabledToOfferState(boolean enabled) {
        return enabled ? LenderOfferState.ACTIVE : LenderOfferState.INACTIVE;
    }
}
