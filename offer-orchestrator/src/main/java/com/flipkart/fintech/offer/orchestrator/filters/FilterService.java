package com.flipkart.fintech.offer.orchestrator.filters;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.response.FilterServiceFilterResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;

import java.util.List;

public interface FilterService {

    public List<String> getFilters(List<LenderOfferEntity> offers);

    public FilterServiceFilterResponse filter(List<LenderOfferEntity> offers, List<String> eligibleFilters,
                                              UserProfileCohortEntity userProfileCohort,
                                              ProfileDetailedResponse profileDetailedResponse, MerchantUser merchantUser);

}
