package com.flipkart.fintech.offer.orchestrator.dao;

import com.flipkart.fintech.offer.orchestrator.model.OfferEvaluatorEntity;
import com.flipkart.fintech.offer.orchestrator.model.OfferEvaluatorState;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import java.util.List;


public class OfferEvaluatorDaoImpl extends AbstractDAO<OfferEvaluatorEntity> implements OfferEvaluatorDao {

    public OfferEvaluatorDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    @Override
    public List<OfferEvaluatorEntity> getEvaluators(List<Lender> lenders, String userProfileCohortId) {
        Criteria criteria = criteria();
        criteria.add(Restrictions.in("lenders", lenders));
        criteria.add(Restrictions.eq("user_profile_cohort_id", userProfileCohortId));
        criteria.add(Restrictions.eq("state", OfferEvaluatorState.ACTIVE));
        return list(criteria);
    }
}