package com.flipkart.fintech.offer.orchestrator.model.temp;

import com.flipkart.fintech.pinaka.api.enums.MerchantStatus;
import lombok.Data;

import javax.persistence.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/08/17.
 */
@Entity
@Table(name = "merchants")
@Data
public class MerchantEntityNew extends BaseEntity {

    @Column(name = "merchant_key")
    private String merchantKey;

    @Column(name = "merchant_name")
    private String merchantName;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private MerchantStatus status;

    @Column(name = "mod_count")
    private int modCount;

}
