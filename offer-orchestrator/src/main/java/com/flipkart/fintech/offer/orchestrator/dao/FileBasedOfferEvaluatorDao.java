package com.flipkart.fintech.offer.orchestrator.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.offer.orchestrator.model.OfferEvaluatorEntity;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.common.configBucket.ConfigBucket;
import com.google.inject.Inject;
import lombok.CustomLog;


import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@CustomLog
public class FileBasedOfferEvaluatorDao implements OfferEvaluatorDao {

    private final static String FILE_NAME = "OFFER_EVALUATORS.json";
    private final ConfigBucket configBucket;
    private static final String activeEvaluatorVersionKey = "ACTIVE_EVALUATOR_VERSION";
    private List<OfferEvaluatorEntity> cachedEvaluators;

    @Inject
    public FileBasedOfferEvaluatorDao(ConfigBucket configBucket) {
        this.configBucket = configBucket;
        this.cachedEvaluators = loadEvaluatorsFromFile();
    }

    @Override
    public List<OfferEvaluatorEntity> getEvaluators(List<Lender> lenders, String userProfileCohortId) {
        return filterEvaluatorsByLenders(cachedEvaluators, lenders, userProfileCohortId);
    }

    private List<OfferEvaluatorEntity> loadEvaluatorsFromFile() {
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(FILE_NAME);
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(inputStream, new TypeReference<List<OfferEvaluatorEntity>>() {});
        } catch (IOException e) {
            throw new RuntimeException("Error reading evaluators from file", e);
        }
    }

    private List<OfferEvaluatorEntity> filterEvaluatorsByLenders(List<OfferEvaluatorEntity> evaluators, List<Lender> lenders, String userProfileCohortId) {
        Double activeEvaluatorVersion = getActiveEvaluatorVersion();
        List<OfferEvaluatorEntity> cohortFilteredEvaluators = evaluators.stream()
                .filter(evaluator -> Objects.equals(evaluator.getUserProfileCohortId(), userProfileCohortId))
                .filter(evaluator -> evaluator.getVersion().equals(activeEvaluatorVersion))
                .collect(Collectors.toList());
        return cohortFilteredEvaluators.stream()
                .filter(evaluator -> Arrays.stream(evaluator.getLenders().split(","))
                        .anyMatch(lenderName -> lenders.contains(Lender.valueOf(lenderName.trim()))))
                .collect(Collectors.toList());
    }

    private Double getActiveEvaluatorVersion(){
        try {
            Double version = configBucket.getDouble(activeEvaluatorVersionKey);
            if (version == null) {
                throw new IllegalStateException("Active evaluator version is null");
            }
            log.info(String.format("Got version %s", version)); // todo: remove log
            return version;
        } catch (Exception e) {
            throw new RuntimeException("Unable to fetch active evaluator version due to", e);
        }
    }
}
