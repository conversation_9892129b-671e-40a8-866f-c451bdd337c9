package com.flipkart.fintech.offer.orchestrator.dao.temp;

import com.flipkart.fintech.offer.orchestrator.model.temp.BorrowerEntityNew;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.GetBorrowerCountRequest;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/08/17.
 */
public interface BorrowerEntityDaoNew {

    BorrowerEntityNew saveOrUpdate(BorrowerEntityNew entity);

    List<BorrowerEntityNew> getByExternalId(String externalId);

    List<BorrowerEntityNew> getByExternalIdAndMerchantId(String externalId, Long merchantId);

    List<BorrowerEntityNew> getAllBorrowersByExternalIdAndMerchantId(String externalId, Long merchantId);

    BorrowerEntityNew getActiveBorrower(String externalId, Long merchantId, ProductType productType);

    List<BorrowerEntityNew> getAllBorrowers(String externalId, Long merchantId, List<ProductType> productTypeList);

    List<BorrowerEntityNew> getActiveBorrowers(String externalId, Long merchantId, ProductType productType);

    List<BorrowerEntityNew> getActiveBorrowers(String externalId, String merchantKey, ProductType productType);

    BorrowerEntityNew getById(Long id);

    BorrowerEntityNew getByExternalIdAndWhitelistId(String externalId, Long whitelistId);

    List<BorrowerEntityNew> getBorrowersForWhitelistId(Long whitelistId);

    List<BorrowerEntityNew> getAllBorrowers(String userId, Long merchantId);

    Long getBorrowerCount(GetBorrowerCountRequest getBorrowerCountRequest);

    BorrowerEntityNew getActiveBorrower(String externalId, Long merchantId, ProductType productType, String lender);
}
