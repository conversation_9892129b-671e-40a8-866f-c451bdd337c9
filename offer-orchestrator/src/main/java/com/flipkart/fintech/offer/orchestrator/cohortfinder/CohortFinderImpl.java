package com.flipkart.fintech.offer.orchestrator.cohortfinder;

import com.flipkart.fintech.offer.orchestrator.dao.UserProfileCohortDao;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.response.GetCohortForUserProfileResponse;
import com.flipkart.fintech.offer.orchestrator.model.responsemetadata.CohortForUserProfileMetadata;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.Objects;

@CustomLog
public class CohortFinderImpl implements CohortFinder {

    private static final HashMap<String, UserProfileCohortEntity> cohortMap = new HashMap<>();
    private static final String activeVersion = "v1";

    static {
        cohortMap.put("PL_V0_COHORT_1", new UserProfileCohortEntity(
                1L,
                "PL_V0_COHORT_1",
                true,
                "v0"
        ));
        cohortMap.put("PL_V0_COHORT_2", new UserProfileCohortEntity(
                2L,
                "PL_V0_COHORT_2",
                true,
                "v0"
        ));
        cohortMap.put("PL_V0_COHORT_3", new UserProfileCohortEntity(
                3L,
                "PL_V0_COHORT_3",
                true,
                "v0"
        ));
        cohortMap.put("PL_V0_COHORT_4", new UserProfileCohortEntity(
                4L,
                "PL_V0_COHORT_4",
                true,
                "v0"
        ));
        cohortMap.put("PL_V0_COHORT_5", new UserProfileCohortEntity(
                5L,
                "PL_V0_COHORT_5",
                true,
                "v0"
        ));
        cohortMap.put("PL_V1_COHORT_1", new UserProfileCohortEntity(
                6L,
                "PL_V1_COHORT_1",
                true,
                "v1"
        ));
        cohortMap.put("PL_V1_COHORT_2", new UserProfileCohortEntity(
                7L,
                "PL_V1_COHORT_2",
                true,
                "v1"
        ));
        cohortMap.put("PL_V1_COHORT_3", new UserProfileCohortEntity(
                8L,
                "PL_V1_COHORT_3",
                true,
                "v1"
        ));
        cohortMap.put("PL_V1_COHORT_4", new UserProfileCohortEntity(
                9L,
                "PL_V1_COHORT_4",
                true,
                "v1"
        ));
    }

    private final UserProfileScores userProfileScores;
    private final UserProfileCohortDao userProfileCohortDao;

    @Inject
    public CohortFinderImpl(UserProfileScores userProfileScores, UserProfileCohortEntity userProfileCohortEntity,
                            UserProfileCohortDao userProfileCohortDao) {
        this.userProfileScores = userProfileScores;
        this.userProfileCohortDao = userProfileCohortDao;
    }

    @Override
    public GetCohortForUserProfileResponse getCohortForUserProfile(ProfileDetailedResponse userProfile, MerchantUser merchantUser, String requestId) {
        // enhance
        Double binScore;
        try {
            binScore = userProfileScores.getUserBin(merchantUser, requestId);
        } catch (Exception e) {
            log.error(String.format("Failed to find binScore for user profile %s due to %s", userProfile.getProfileId(), e.getMessage()));
            throw new RuntimeException(String.format("Unable to get cohort for user %s due to %s", userProfile.getMerchantUserId(), e.getMessage()));
        }
        CohortForUserProfileMetadata metadata = new CohortForUserProfileMetadata();
        GetCohortForUserProfileResponse response = new GetCohortForUserProfileResponse();
        metadata.setBinScore(binScore);
        response.setMetadata(metadata);
        if (Objects.equals(activeVersion, "v0")) {
            if (binScore == null || binScore <= 7) {
                response.setUserProfileCohortEntity(cohortMap.get("PL_V0_COHORT_1"));
            } else if (binScore >= 8 && binScore < 13) {
                if (userProfile.getEmploymentType() == EmploymentType.Salaried) {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V0_COHORT_2"));
                } else {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V0_COHORT_3"));
                }

            } else {
                if (userProfile.getEmploymentType() == EmploymentType.Salaried) {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V0_COHORT_4"));
                } else {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V0_COHORT_5"));
                }
            }
        } else if (Objects.equals(activeVersion, "v1")) {
            if (binScore == null || binScore <= 12) {
                if (userProfile.getEmploymentType() == EmploymentType.Salaried) {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V1_COHORT_1"));
                } else {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V1_COHORT_2"));
                }

            } else if (binScore > 12) {
                if (userProfile.getEmploymentType() == EmploymentType.Salaried) {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V1_COHORT_3"));
                } else {
                    response.setUserProfileCohortEntity(cohortMap.get("PL_V1_COHORT_4"));
                }

            }
        } else {
            throw new RuntimeException(String.format("Unable to get cohort for user %s", userProfile.getMerchantUserId()));
        }
        return response;
    }
}

