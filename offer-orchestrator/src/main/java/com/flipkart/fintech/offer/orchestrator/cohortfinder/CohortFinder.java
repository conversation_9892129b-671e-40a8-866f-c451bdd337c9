package com.flipkart.fintech.offer.orchestrator.cohortfinder;


import com.flipkart.fintech.offer.orchestrator.model.response.GetCohortForUserProfileResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;

public interface CohortFinder {
    GetCohortForUserProfileResponse getCohortForUserProfile(ProfileDetailedResponse userProfile, MerchantUser merchantUser, String requestId);

}
