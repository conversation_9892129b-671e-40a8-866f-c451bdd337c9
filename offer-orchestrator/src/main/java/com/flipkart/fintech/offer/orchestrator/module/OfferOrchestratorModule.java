package com.flipkart.fintech.offer.orchestrator.module;

import com.flipkart.fintech.offer.orchestrator.dao.*;
import com.flipkart.fintech.offer.orchestrator.dao.BlacklistedDaoImpl;
import com.flipkart.fintech.offer.orchestrator.dao.temp.BorrowerEntityDaoImplNew;
import com.flipkart.fintech.offer.orchestrator.dao.temp.BorrowerEntityDaoNew;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import org.hibernate.SessionFactory;


public class OfferOrchestratorModule extends AbstractModule {

    private final SessionFactory sessionFactory;

    public OfferOrchestratorModule(SessionFactory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }

    @Override
    protected void configure() {

    }

    @Provides
    @Singleton
    public LenderOfferDao provideLenderOffer(){
        return new LenderOfferDaoImpl(sessionFactory);
    }

    @Provides
    @Singleton
    public LenderPincodeServiceabilityDao provideLenderPincodeDao(){
        return new LenderPincodeServiceabilityDaoImpl(sessionFactory);
    }

    @Provides
    @Singleton
    public BlacklistedDao provideBlacklistedDao(){
        return new BlacklistedDaoImpl(sessionFactory);
    }

    @Provides
    @Singleton
    public BorrowerEntityDaoNew provideBorrowerEntityDaoNew() {
        return new BorrowerEntityDaoImplNew(sessionFactory);
    }

    @Provides
    @Singleton
    public UserProfileCohortDao provideUserProfileCohortDao(){
        return new UserProfileCohortDaoImpl(sessionFactory);
    }
}