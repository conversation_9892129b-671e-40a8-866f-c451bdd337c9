package com.flipkart.fintech.offer.orchestrator.usereligibility;

import com.flipkart.fintech.offer.orchestrator.dao.BlacklistedDao;
import com.flipkart.fintech.offer.orchestrator.model.BlacklistedEntity;
import com.google.inject.Inject;

public class UserEligibilityImpl implements UserEligibility{


    private final BlacklistedDao blacklistedDao;

    @Inject
    public UserEligibilityImpl(BlacklistedDao blacklistedDao) {
        this.blacklistedDao = blacklistedDao;
    }

    @Override
    public Boolean isUserEligible(String userId) {
        Boolean blacklisted = isUserBlacklisted(userId);
        return !blacklisted;
    }

    private Boolean isUserBlacklisted(String userId) {
        BlacklistedEntity blacklistedEntity = blacklistedDao.getActive(userId);
        return blacklistedEntity != null;
    }
}
