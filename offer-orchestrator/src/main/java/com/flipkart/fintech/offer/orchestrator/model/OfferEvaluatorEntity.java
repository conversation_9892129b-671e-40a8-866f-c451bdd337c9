package com.flipkart.fintech.offer.orchestrator.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;

import javax.persistence.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import static com.flipkart.fintech.offer.orchestrator.model.Utility.generateHashedId;


@Entity
@Table(name = "offer_evaluator")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfferEvaluatorEntity{

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "user_profile_cohort_id")
    private String userProfileCohortId;

    @Getter
    @Column(name = "weight")
    private Double weight;

    @Getter
    @Column(name = "lenders")
    private String lenders;

    @Getter
    @Column(name = "state")
    private OfferEvaluatorState state;

    @Column(name = "version")
    private Double version;
    // will store lender rank as config
    @Column(name = "created_by")
    private String created_by;

    @Column(name = "created_at_ms")
    private Long created_at_ms;

    @Column(name = "updated_at_ms")
    private Long updated_at_ms;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        this.created_at_ms = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
        this.updated_at_ms = this.created_at_ms;
    }

    @PreUpdate
    protected void onUpdate() {
        Instant now = Instant.now();
        this.updated_at_ms = now.atZone(ZoneId.of("Asia/Kolkata")).toEpochSecond() * 1000;
    }

    public LocalDateTime getCreatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(created_at_ms), ZoneId.of("Asia/Kolkata"));
    }

    public LocalDateTime getUpdatedAt() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(created_at_ms), ZoneId.of("Asia/Kolkata"));
    }

    public OfferEvaluatorEntity(){
        this.id = generateHashedId("OEE");
    }

}
