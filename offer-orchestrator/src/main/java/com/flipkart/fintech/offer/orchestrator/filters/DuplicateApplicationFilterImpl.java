package com.flipkart.fintech.offer.orchestrator.filters;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pandora.api.model.request.lenderdedupe.LenderDedupeRequest;
import com.flipkart.fintech.pandora.api.model.request.lenderdedupe.LenderDedupeResponse;
import com.flipkart.fintech.pandora.client.PlOnboardingClient;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.configBucket.ConfigBucket;
import com.flipkart.fintech.pinaka.common.metricRegistry.PinakaMetricRegistry;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.google.inject.Inject;
import lombok.CustomLog;


@CustomLog
public class DuplicateApplicationFilterImpl implements DuplicateApplicationFilter {

    private final static String DEDUPE_PASS = "DEDUPE_PASS";
    private final static String DEDUPE_FAIL = "DEDUPE_FAIL";
    private final static String DEDUPE_REQUEST_FAIL = "DEDUPE_REQUEST_FAIL";
    private final FilterConfiguration filterConfiguration;
    private final PlOnboardingClient plOnboardingClient;
    private final ConfigBucket configBucket;
    private final String DUPLICATE_APPLICATION_FILTER_ENABLED_KEY = "orchestratorDedupeEnabled";

    @Inject
    public DuplicateApplicationFilterImpl(FilterConfiguration filterConfiguration, PlOnboardingClient plOnboardingClient, ConfigBucket configBucket) {
        this.filterConfiguration = filterConfiguration;
        this.plOnboardingClient = plOnboardingClient;
        this.configBucket = configBucket;
    }

    @Override
    public Boolean filter(LenderOfferEntity lenderOfferEntity, UserProfileCohortEntity userCohort,
                          ProfileDetailedResponse userProfile, MerchantUser merchantUser) {
        Boolean duplicateApplicationFilterEnabled = configBucket.getBoolean(DUPLICATE_APPLICATION_FILTER_ENABLED_KEY);
        if (duplicateApplicationFilterEnabled == null) duplicateApplicationFilterEnabled = false;
        if (!duplicateApplicationFilterEnabled) return true;
        LenderFilterDetail filterDetail = filterConfiguration.getConfiguration(
                lenderOfferEntity.getLender(),
                DuplicateApplicationFilter.class
        );
        if (filterDetail == null || !filterDetail.getEnabled()) return true;
        LenderDedupeRequest dedupeRequest = new LenderDedupeRequest(
                userProfile.getPan(), null, userProfile.getPhoneNo(), String.valueOf(lenderOfferEntity.getLender())
        );
        LenderDedupeResponse dedupeResponse = plOnboardingClient.dedupeCheck(dedupeRequest,
                String.valueOf(userProfile.getProfileId()), merchantUser.getMerchantKey());
        if (dedupeResponse.getRequestSuccessful()) {
            log.info(String.format("DEDUPE_REQUEST_PASS, got %s", dedupeResponse.getIsDuplicateApplication())); // temporary log to monitor
            if (dedupeResponse.getIsDuplicateApplication()) {
                PinakaMetricRegistry.getMetricRegistry().meter(DEDUPE_FAIL).mark();
                return false;
            }
            PinakaMetricRegistry.getMetricRegistry().meter(DEDUPE_PASS).mark();
            return true;
        } else {
            log.info(String.format("DEDUPE_REQUEST_FAIL %s %s %s %s", dedupeRequest.getLender(), dedupeRequest.getMobileNumber(), dedupeRequest.getPan(), dedupeResponse.getMessage())); // temporary log to monitor
            PinakaMetricRegistry.getMetricRegistry().meter(DEDUPE_REQUEST_FAIL).mark();
            return true; // not blocking if request to lender fails
        }
    }
}
