package com.flipkart.fintech.offer.orchestrator.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.offer.orchestrator.Utility;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.google.inject.Inject;
import lombok.CustomLog;
import lombok.Data;

import java.time.format.DateTimeParseException;


@CustomLog
public class AgeFilterImpl implements AgeFilter{

    private final FilterConfiguration filterConfiguration;

    private final Decrypter decrypter;

    private final Utility utility;

    @Inject
    public AgeFilterImpl(FilterConfiguration filterConfiguration, Decrypter decrypter, Utility utility) {
        this.filterConfiguration = filterConfiguration;
        this.decrypter = decrypter;
        this.utility = utility;
    }


    @Override
    public Boolean filter(LenderOfferEntity lenderOfferEntity, UserProfileCohortEntity userCohort,
                          ProfileDetailedResponse userProfile, MerchantUser merchantUser) {
        LenderFilterDetail filterDetail = filterConfiguration.getConfiguration(
                lenderOfferEntity.getLender(),
                AgeFilter.class
        );
        if(filterDetail == null  || !filterDetail.getEnabled()) return true;
        String dob = null;
        int age;
        String details = filterDetail.getDescriptor();
        AgeFilterDetails ageFilterDetails;
        try{
            ageFilterDetails = parse(details);
            dob = decrypter.decryptString(userProfile.getDob());
            age = utility.calculateAge(dob);
        } catch (JsonProcessingException e){
            throw new RuntimeException(String.format("Failed to parse %s", details), e);
        } catch (DateTimeParseException e){
            throw new RuntimeException(String.format("Failed to find age for string %s", dob), e);
        }
        catch (Exception e){
            throw new RuntimeException(String.format("Failed fetching age for %s", userProfile.getDob()), e);
        }
        return age >= ageFilterDetails.minAge && age <= ageFilterDetails.maxAge;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    static
    class AgeFilterDetails{
        private int minAge;
        private int maxAge;
    }

    private static AgeFilterDetails parse(String json) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, AgeFilterDetails.class);
    }

}