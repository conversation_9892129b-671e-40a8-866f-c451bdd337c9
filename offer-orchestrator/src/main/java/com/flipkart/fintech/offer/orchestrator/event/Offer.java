package com.flipkart.fintech.offer.orchestrator.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferState;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferType;
import com.flipkart.fintech.pinaka.api.enums.Lender;

public class Offer {

    @JsonProperty("id")
    String id;
    @JsonProperty("lender")
    Lender lender;
    @JsonProperty("offer_type")
    LenderOfferType lenderOfferType;
    @JsonProperty("offer_state")
    LenderOfferState lenderOfferState;
    @JsonProperty("rejection_reason")
    String rejectionReason;

    public Offer(LenderOfferEntity lenderOfferEntity) {
        this.id = lenderOfferEntity.getId();
        this.lender = lenderOfferEntity.getLender();
        this.lenderOfferType = lenderOfferEntity.getOfferType();
        this.lenderOfferState = lenderOfferEntity.getStatus();
        this.rejectionReason = null;
    }

    public Offer(String id, String rejectionReason, Lender lender) {
        this.id = id;
        this.rejectionReason = rejectionReason;
        this.lender = lender;
    }

}
