package com.flipkart.fintech.offer.orchestrator.orchestratorWhitelistManager;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class OfferOrchestratorWhitelistManagerImpl implements OfferOrchestratorWhitelistManager {

    private final DynamicBucket dynamicBucket;
    public static final String ORCHESTRATOR_WHITELIST_KEY = "orchestratorWhitelist";

    @Inject
    public OfferOrchestratorWhitelistManagerImpl(DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
    }

    @Override
    public Optional<LenderOfferEntity> getWhitelistedOffer(String userAccountId) {
        String whitelist = dynamicBucket.getString(ORCHESTRATOR_WHITELIST_KEY);
        List<LenderOfferEntity> whitelistedOffers = deserialize(whitelist);
        if(whitelistedOffers == null) return Optional.empty();
        List<LenderOfferEntity> userOffers = whitelistedOffers.stream().filter(it -> Objects.equals(it.getUserId(), userAccountId)).collect(Collectors.toList());
        if(!userOffers.isEmpty()) return Optional.ofNullable(userOffers.get(0));
        return Optional.empty();
    }

    private static List<LenderOfferEntity> deserialize(String jsonString) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            TypeReference<List<LenderOfferEntity>> typeReference = new TypeReference<List<LenderOfferEntity>>() {
            };
            return objectMapper.readValue(jsonString, typeReference);
        } catch (Exception e) {
            return null;
        }
    }


}
