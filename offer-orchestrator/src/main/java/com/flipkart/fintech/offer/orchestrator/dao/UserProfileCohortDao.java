package com.flipkart.fintech.offer.orchestrator.dao;


import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.google.inject.ImplementedBy;

import java.util.List;

@ImplementedBy(UserProfileCohortDaoImpl.class)
public interface UserProfileCohortDao {

    UserProfileCohortEntity getCohortById(String id);

    UserProfileCohortEntity getCohortByName(String name);

    List<UserProfileCohortEntity> getAllCohorts();

}
