package com.flipkart.fintech.offer.orchestrator.dao;

import com.flipkart.fintech.offer.orchestrator.model.BlacklistedEntity;
import io.dropwizard.hibernate.AbstractDAO;
import lombok.CustomLog;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

@CustomLog
public class BlacklistedDaoImpl extends AbstractDAO<BlacklistedEntity> implements BlacklistedDao {
    public BlacklistedDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }
    @Override
    public BlacklistedEntity getActive(String userId) {
        Criteria criteria = criteria();
        criteria.add(Restrictions.eq("userId", userId));
        criteria.add(Restrictions.eq("active", true));
        return uniqueResult(criteria);
    }
}
