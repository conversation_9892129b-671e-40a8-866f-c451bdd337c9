package com.flipkart.fintech.offer.orchestrator.model.response;

import com.flipkart.fintech.offer.orchestrator.model.responsemetadata.CohortForUserProfileMetadata;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class GetCohortForUserProfileResponse {
    UserProfileCohortEntity userProfileCohortEntity;
    CohortForUserProfileMetadata metadata;
}
