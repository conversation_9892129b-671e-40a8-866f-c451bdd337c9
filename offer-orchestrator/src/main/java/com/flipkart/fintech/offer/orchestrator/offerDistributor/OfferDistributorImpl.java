package com.flipkart.fintech.offer.orchestrator.offerDistributor;

import com.flipkart.fintech.offer.orchestrator.dao.FileBasedOfferEvaluatorDao;
import com.flipkart.fintech.offer.orchestrator.dao.OfferEvaluatorDao;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.OfferEvaluatorEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.google.inject.Inject;

import java.util.List;
import java.util.stream.Collectors;

public class OfferDistributorImpl implements OfferDistributor {


    private final OfferEvaluatorDao offerEvaluatorDao;

    @Inject
    public OfferDistributorImpl(FileBasedOfferEvaluatorDao fileBasedOfferEvaluatorDao) {
        this.offerEvaluatorDao = fileBasedOfferEvaluatorDao;
    }

    @Override
    public OfferEvaluatorEntity getActiveEvaluator(List<LenderOfferEntity> offers, UserProfileCohortEntity cohort) {
        List<OfferEvaluatorEntity> activeEvaluators = offerEvaluatorDao.getEvaluators(
                offers.stream()
                        .map(LenderOfferEntity::getLender)
                        .collect(Collectors.toList()), String.valueOf(cohort.getName())
        );
        if(activeEvaluators.isEmpty()) return null;
        return selectRandomlyByWeight(activeEvaluators);
    }

    private static OfferEvaluatorEntity selectRandomlyByWeight(List<OfferEvaluatorEntity> evaluators) {
        double totalWeight = evaluators.stream()
                .mapToDouble(OfferEvaluatorEntity::getWeight)
                .sum();
        double randomWeight = Math.random() * totalWeight;
        double currentWeight = 0;
        for (OfferEvaluatorEntity evaluator : evaluators) {
            currentWeight += evaluator.getWeight();
            if (currentWeight >= randomWeight) {
                return evaluator;
            }
        }
        return null;
    }
}
