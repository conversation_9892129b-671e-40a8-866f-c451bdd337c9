package com.flipkart.fintech.offer.orchestrator.filters;


import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;

public interface LenderBasedFilter {
    // add filter type
    public Boolean filter(LenderOfferEntity lenderOfferEntity, UserProfileCohortEntity userCohort,
                          ProfileDetailedResponse userProfile, MerchantUser merchantUser);

}
