package com.flipkart.fintech.offer.orchestrator.filters;


import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.google.inject.ImplementedBy;

import java.util.List;
@ImplementedBy(FilterConfigurationImpl.class)
public interface FilterConfiguration {

    public List<String> getEnabledFilters(Lender lender);

    public LenderFilterDetail getConfiguration(Lender lender, Class<? extends LenderBasedFilter> filterClass);
}
