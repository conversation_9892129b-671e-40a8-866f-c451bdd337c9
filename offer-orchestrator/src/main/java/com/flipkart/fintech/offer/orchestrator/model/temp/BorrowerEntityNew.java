package com.flipkart.fintech.offer.orchestrator.model.temp;

import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/08/17.
 */
@Entity
@Data
@Table(name = "borrowers")
public class BorrowerEntityNew extends BaseEntity{

    @Column(name = "external_id")
    private String externalId;

    private String metadata;
    private boolean enabled;

    @NotNull
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "whitelist_id", nullable = false, foreignKey = @ForeignKey(name = "whitelist_id"))
    private WhitelistEntityNew whitelist;
}
