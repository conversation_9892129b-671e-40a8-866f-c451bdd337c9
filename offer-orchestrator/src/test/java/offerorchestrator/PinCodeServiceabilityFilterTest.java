package offerorchestrator;

import com.flipkart.fintech.offer.orchestrator.dao.LenderPincodeServiceabilityDao;
import com.flipkart.fintech.offer.orchestrator.filters.FilterConfiguration;
import com.flipkart.fintech.offer.orchestrator.filters.PinCodeServiceabilityFilter;
import com.flipkart.fintech.offer.orchestrator.filters.PinCodeServiceabilityFilterImpl;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderPincodeServiceabilityEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class PinCodeServiceabilityFilterTest {
// todo add more testcases
    @Test
    public void testFilter_ServiceabilityAvailable() {
        FilterConfiguration filterConfiguration = Mockito.mock(FilterConfiguration.class);
        LenderPincodeServiceabilityDao lenderPincodeServiceabilityDao = Mockito.mock(LenderPincodeServiceabilityDao.class);
        Lender lender = Lender.AXIS;
        LenderOfferEntity lenderOfferEntity = new LenderOfferEntity();
        lenderOfferEntity.setLender(lender);

        ProfileDetailedResponse userProfile = new ProfileDetailedResponse();
        userProfile.setUserEnteredPincode(560035);
        userProfile.setShippingPincode(null);

        LenderFilterDetail filterDetail = new LenderFilterDetail();
        filterDetail.setEnabled(true);
        filterDetail.setDescriptor("{\"pincodeTypes\":  [\"shippingPincode\", \"userEnteredPincode\"]}");
        when(filterConfiguration.getConfiguration(lenderOfferEntity.getLender(), PinCodeServiceabilityFilter.class)).thenReturn(filterDetail);
        when(lenderPincodeServiceabilityDao.getActive(anyObject(), anyString())).thenReturn(new LenderPincodeServiceabilityEntity());
        PinCodeServiceabilityFilter pinCodeServiceabilityFilter = new PinCodeServiceabilityFilterImpl(filterConfiguration, lenderPincodeServiceabilityDao);
        assertTrue(pinCodeServiceabilityFilter.filter(lenderOfferEntity, null, userProfile, MerchantUser.getMerchantUser("FLIPKART", "1", "1")));
    }
}
