package com.flipkart.fintech.offer.orchestrator.filters;

import com.flipkart.fintech.citadel.api.models.ApplicationPivotsResponse;
import com.flipkart.fintech.citadel.api.models.ApplicationStateResponse;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import org.apache.commons.lang.time.DateUtils;
import javax.validation.constraints.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ApplicationCoolOffFilterTest {

    @Mock
    private FilterConfiguration filterConfiguration;

    @NotNull
    private static LenderOfferEntity getLenderOfferEntity(Lender lender) {
        LenderOfferEntity lenderOfferEntity = new LenderOfferEntity();
        lenderOfferEntity.setLender(lender);
        return lenderOfferEntity;
    }

    @NotNull
    private static ProfileDetailedResponse getProfileDetailedResponse() {
        ProfileDetailedResponse userProfile = new ProfileDetailedResponse();
        userProfile.setUserEnteredPincode(560035);
        userProfile.setShippingPincode(null);
        return userProfile;
    }

    @NotNull
    private static LenderFilterDetail getLenderFilterDetail() {
        LenderFilterDetail filterDetail = new LenderFilterDetail();
        filterDetail.setEnabled(true);
        filterDetail.setDescriptor("{\"timeUnit\": \"MINUTES\",\"timeValue\": 15}");
        return filterDetail;
    }

    @NotNull
    private static ApplicationPivotsResponse axisDiscardedApplication(int createdBeforeInMinutes) {
        ApplicationPivotsResponse applicationPivotsResponse = new ApplicationPivotsResponse();
        applicationPivotsResponse.setApplicationId("APP1");
        applicationPivotsResponse.setApplicationState("DISCARDED");
        applicationPivotsResponse.setApplicationType("PERSONAL_LOAN");
        applicationPivotsResponse.setCreatedAt(DateUtils.addMinutes(new Date(), -createdBeforeInMinutes));
        applicationPivotsResponse.setUpdatedAt(DateUtils.addMinutes(new Date(), 0));
        return applicationPivotsResponse;
    }

    @NotNull
    private static ApplicationPivotsResponse idfcDiscardedApplication(int createdBeforeInMinutes) {
        ApplicationPivotsResponse applicationPivotsResponse = new ApplicationPivotsResponse();
        applicationPivotsResponse.setApplicationId("APP1");
        applicationPivotsResponse.setApplicationState("DISCARDED");
        applicationPivotsResponse.setApplicationType("PERSONAL_LOAN_IDFC");
        applicationPivotsResponse.setCreatedAt(DateUtils.addMinutes(new Date(), -createdBeforeInMinutes));
        applicationPivotsResponse.setUpdatedAt(DateUtils.addMinutes(new Date(), 0));
        return applicationPivotsResponse;
    }

    @Test
    public void test() {
        ApplicationStateResponse applicationStateResponse = new ApplicationStateResponse();
        applicationStateResponse.setApplicationsList(new ArrayList<>());

        UserProfileCohortEntity userCohort = new UserProfileCohortEntity();
        LenderOfferEntity lenderOfferEntity = getLenderOfferEntity(Lender.AXIS);

        ProfileDetailedResponse userProfile = getProfileDetailedResponse();

        MerchantUser merchantUser = MerchantUser.getMerchantUser("FLIPKART", "1", "1");

        LenderFilterDetail filterDetail = getLenderFilterDetail();
        when(filterConfiguration.getConfiguration(lenderOfferEntity.getLender(), ApplicationCoolOffFilter.class)).thenReturn(filterDetail);

        ApplicationCoolOffFilter filter = new ApplicationCoolOffFilter(filterConfiguration, applicationStateResponse);
        assertTrue(filter.filter(lenderOfferEntity, userCohort, userProfile, merchantUser));
    }

    @Test
    public void test2() {
        ApplicationStateResponse applicationStateResponse = new ApplicationStateResponse();
        List<ApplicationPivotsResponse> applicationsList = new ArrayList<>();
        ApplicationPivotsResponse applicationPivotsResponse = axisDiscardedApplication(30);
        applicationsList.add(applicationPivotsResponse);
        applicationStateResponse.setApplicationsList(applicationsList);

        UserProfileCohortEntity userCohort = new UserProfileCohortEntity();
        LenderOfferEntity lenderOfferEntity = getLenderOfferEntity(Lender.AXIS);

        ProfileDetailedResponse userProfile = getProfileDetailedResponse();
        MerchantUser merchantUser = MerchantUser.getMerchantUser("FLIPKART", "1", "1");

        LenderFilterDetail filterDetail = getLenderFilterDetail();
        when(filterConfiguration.getConfiguration(lenderOfferEntity.getLender(), ApplicationCoolOffFilter.class)).thenReturn(filterDetail);

        ApplicationCoolOffFilter filter = new ApplicationCoolOffFilter(filterConfiguration, applicationStateResponse);
        assertTrue(filter.filter(lenderOfferEntity, userCohort, userProfile, merchantUser));
    }

    @Test
    public void test3() {
        ApplicationStateResponse applicationStateResponse = new ApplicationStateResponse();
        ArrayList<ApplicationPivotsResponse> applicationsList = new ArrayList<>();
        ApplicationPivotsResponse applicationPivotsResponse = axisDiscardedApplication(
                10);
        applicationsList.add(applicationPivotsResponse);
        applicationStateResponse.setApplicationsList(applicationsList);

        UserProfileCohortEntity userCohort = new UserProfileCohortEntity();
        LenderOfferEntity lenderOfferEntity = getLenderOfferEntity(Lender.AXIS);

        ProfileDetailedResponse userProfile = getProfileDetailedResponse();

        MerchantUser merchantUser = MerchantUser.getMerchantUser("FLIPKART", "1", "1");

        LenderFilterDetail filterDetail = getLenderFilterDetail();
        when(filterConfiguration.getConfiguration(lenderOfferEntity.getLender(), ApplicationCoolOffFilter.class)).thenReturn(filterDetail);

        ApplicationCoolOffFilter filter = new ApplicationCoolOffFilter(filterConfiguration, applicationStateResponse);
        assertFalse(filter.filter(lenderOfferEntity, userCohort, userProfile, merchantUser));
    }

    @Test
    public void test4() {
        ApplicationStateResponse applicationStateResponse = new ApplicationStateResponse();
        ArrayList<ApplicationPivotsResponse> applicationsList = new ArrayList<>();
        ApplicationPivotsResponse applicationPivotsResponse = idfcDiscardedApplication(
                10);
        applicationsList.add(applicationPivotsResponse);
        applicationStateResponse.setApplicationsList(applicationsList);

        UserProfileCohortEntity userCohort = new UserProfileCohortEntity();
        LenderOfferEntity lenderOfferEntity = getLenderOfferEntity(Lender.AXIS);

        ProfileDetailedResponse userProfile = getProfileDetailedResponse();
        MerchantUser merchantUser = MerchantUser.getMerchantUser("FLIPKART", "1", "1");

        LenderFilterDetail filterDetail = getLenderFilterDetail();
        when(filterConfiguration.getConfiguration(lenderOfferEntity.getLender(), ApplicationCoolOffFilter.class)).thenReturn(filterDetail);

        ApplicationCoolOffFilter filter = new ApplicationCoolOffFilter(filterConfiguration, applicationStateResponse);
        assertTrue(filter.filter(lenderOfferEntity, userCohort, userProfile, merchantUser));
    }
}