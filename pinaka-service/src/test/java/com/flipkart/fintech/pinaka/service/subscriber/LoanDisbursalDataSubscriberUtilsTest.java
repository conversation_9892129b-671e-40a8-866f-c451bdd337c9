package com.flipkart.fintech.pinaka.service.subscriber;

import com.supermoney.schema.PandoraService.OfferEventV1;
import com.supermoney.schema.PandoraService.OfferDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class LoanDisbursalDataSubscriberUtilsTest {

    @Test
    public void testParseOfferEventV1() throws IOException {
        String json = "{\n" +
                "  \"event_id\": { \"string\": \"APP2504240559581191995596489424757366248_B2C03069968\" },\n" +
                "  \"date\": { \"string\": \"2025-07-17T17:00:57.760\" },\n" +
                "  \"additional_data\": null,\n" +
                "  \"lender\": { \"string\": \"ABFL\" },\n" +
                "  \"account_id\": { \"string\": \"SMA65ADA567141E4F25905D60C8ECD24649\" },\n" +
                "  \"program_id\": null,\n" +
                "  \"validity\": { \"long\": **************** },\n" +
                "  \"application_id\": { \"string\": \"APP2504240559581191995596489424757366248\" },\n" +
                "  \"offer_details\": [\n" +
                "    {\n" +
                "      \"min_amount\": { \"double\": 50000 },\n" +
                "      \"max_amount\": { \"double\": 50000 },\n" +
                "      \"min_tenure\": { \"double\": 540 },\n" +
                "      \"max_tenure\": { \"double\": 540 },\n" +
                "      \"roi\": { \"double\": 24.75 },\n" +
                "      \"pf_amount\": { \"double\": 3 },\n" +
                "      \"pf_type\": null,\n" +
                "      \"gst\": { \"double\": 18 },\n" +
                "      \"stamp_duty\": null,\n" +
                "      \"net_disbursed\": { \"long\": 49876 },\n" +
                "      \"emi\": null,\n" +
                "      \"first_emi_date\": null,\n" +
                "      \"validity\": null\n" +
                "    }\n" +
                "  ],\n" +
                "  \"lead\": null,\n" +
                "  \"offer_type\": { \"string\": \"INITIAL\" },\n" +
                "  \"offer_id\": { \"string\": \"B2C03069968\" },\n" +
                "  \"external_user_id\": { \"string\": \"ACCA8DA3C3F797348C1A14AD8C139172AAB5\" }\n" +
                "}";

        OfferEventV1 offer = LoanDisbursalDataSubscriberUtils.parseOfferEventV1(json);

        assertNotNull(offer);
        assertEquals("APP2504240559581191995596489424757366248_B2C03069968", offer.getEventId());
        assertEquals("2025-07-17T17:00:57.760", offer.getDate());
        assertNull(offer.getAdditionalData());
        assertEquals("ABFL", offer.getLender());
        assertEquals("SMA65ADA567141E4F25905D60C8ECD24649", offer.getAccountId());
        assertNull(offer.getProgramId());
        assertEquals(****************L, offer.getValidity());
        assertEquals("APP2504240559581191995596489424757366248", offer.getApplicationId());
        assertNull(offer.getLead());
        assertEquals("INITIAL", offer.getOfferType());
        assertEquals("B2C03069968", offer.getOfferId());
        assertEquals("ACCA8DA3C3F797348C1A14AD8C139172AAB5", offer.getExternalUserId());

        List<OfferDetail> details = offer.getOfferDetails();
        assertNotNull(details);
        assertEquals(1, details.size());
        OfferDetail detail = details.get(0);
        assertEquals(50000.0, detail.getMinAmount());
        assertEquals(50000.0, detail.getMaxAmount());
        assertEquals(540.0, detail.getMinTenure());
        assertEquals(540.0, detail.getMaxTenure());
        assertEquals(24.75, detail.getRoi());
        assertEquals(3.0, detail.getPfAmount());
        assertNull(detail.getPfType());
        assertEquals(18.0, detail.getGst());
        assertNull(detail.getStampDuty());
        assertEquals(49876L, detail.getNetDisbursed());
        assertNull(detail.getEmi());
        assertNull(detail.getFirstEmiDate());
        assertNull(detail.getValidity());
    }
}