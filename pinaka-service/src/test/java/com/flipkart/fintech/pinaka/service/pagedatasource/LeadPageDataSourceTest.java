package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pinaka.api.model.FullName;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.Utils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.ProfileService;
import com.flipkart.fintech.profile.service.UpiUserService;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.upi.user.service.api.models.base.v1.enums.GenericSearchType;
import com.flipkart.upi.user.service.api.models.base.v1.request.search_management.GenericSearchRequestDTO;
import com.flipkart.upi.user.service.api.models.base.v1.response.search_management.GenericSearchResponseDTO;
import com.flipkart.upi.user.service.api.models.base.v1.commons.PayeeDetails;
import com.supermoney.ams.bridge.utils.UrlUtil;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({UrlUtil.class, MerchantUserUtils.class, QueryParamUtils.class, Utils.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class LeadPageDataSourceTest {

    @Mock
    private ConfigUtils mockConfigUtils;
    
    @Mock
    private ProfileService mockProfileService;
    
    @Mock
    private UpiUserService mockUpiUserService;
    
    @Mock
    private ApplicationDataResponse mockApplicationDataResponse;
    
    @Mock
    private MerchantUser mockMerchantUser;
    
    @Mock
    private GenericSearchResponseDTO mockVerifyVpaResponse;
    
    @Mock
    private PayeeDetails mockPayeeDetails;

    private LeadPageDataSource leadPageDataSource;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        leadPageDataSource = new LeadPageDataSource();
        
        // Set up static mocks using PowerMock
        PowerMockito.mockStatic(UrlUtil.class);
        PowerMockito.mockStatic(MerchantUserUtils.class);
        PowerMockito.mockStatic(QueryParamUtils.class);
        PowerMockito.mockStatic(Utils.class);
        
        // Inject mocked dependencies into static fields
        Whitebox.setInternalState(LeadPageDataSource.class, "configUtils", mockConfigUtils);
        Whitebox.setInternalState(LeadPageDataSource.class, "profileService", mockProfileService);
        Whitebox.setInternalState(LeadPageDataSource.class, "upiUserService", mockUpiUserService);
    }

    @Test
    public void testGetData_ApplicationDataResponse_Success() throws Exception {
        // Arrange
        List<NameValuePair> queryParams = Arrays.asList(
            new BasicNameValuePair("applicationId", "test-app-id"),
            new BasicNameValuePair("token", "test-token")
        );
        Map<String, Object> queryParamsMap = new HashMap<>();
        queryParamsMap.put("applicationId", "test-app-id");
        queryParamsMap.put("token", "test-token");
        
        when(mockApplicationDataResponse.getExternalUserId()).thenReturn("external-user-123");
        when(mockApplicationDataResponse.getSmUserId()).thenReturn("sm-user-123");
        
        PowerMockito.when(UrlUtil.getQueryParams(mockApplicationDataResponse)).thenReturn(queryParams);
        PowerMockito.when(MerchantUserUtils.getMerchantUser("external-user-123", "sm-user-123"))
                   .thenReturn(mockMerchantUser);
        PowerMockito.when(QueryParamUtils.getQueryParams(queryParams)).thenReturn(queryParamsMap);
        
        // Mock the second getData method
        setupSuccessfulProfileResponse();
        
        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockApplicationDataResponse);
        
        // Assert
        assertNotNull(result);
        assertEquals(queryParamsMap, result.getQueryParams());
        assertNotNull(result.getProfile());
        assertEquals("John", result.getProfile().getFirstName());
        assertEquals("Doe", result.getProfile().getLastName());
        assertTrue(result.getIsNameEncrypted());
    }

    @Test
    public void testGetData_WithMerchantUser_ProfileServiceSuccess() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        setupSuccessfulProfileResponse();
        
        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);
        
        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertNotNull(result.getProfile());
        assertEquals("John", result.getProfile().getFirstName());
        assertEquals("Doe", result.getProfile().getLastName());
        assertTrue(result.getIsNameEncrypted());
        
        verify(mockProfileService).getProfileByUserId("merchant-user-123", "sm-user-123", false);
        verify(mockUpiUserService, never()).verifyVpa(any());
    }

    @Test
    public void testGetData_WithEncryptionData() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        EncryptionData encryptionData = new EncryptionData("public-key", "key-id");

        // Set up profile response first
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName("John");
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        // Set up encryption data after profile setup
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.of(encryptionData));

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals(encryptionData, result.getEncryptionData());
        assertEquals("John", result.getProfile().getFirstName());
        assertEquals("Doe", result.getProfile().getLastName());
    }

    @Test
    public void testGetData_WithoutEncryptionData() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        setupSuccessfulProfileResponse();
        
        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);
        
        // Assert
        assertNotNull(result);
        assertNull(result.getEncryptionData());
    }

    @Test
    public void testGetData_ProfileServiceException_NonMyntraMerchant() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");
        
        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenThrow(new RuntimeException("Profile service error"));
        
        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);
        
        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertNotNull(result.getProfile());
        assertNull(result.getProfile().getFirstName());
        assertTrue(result.getIsNameEncrypted());
    }

    @Test
    public void testGetData_ProfileServiceException_MyntraMerchant() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn(MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY);
        
        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenThrow(new RuntimeException("Profile service error"));
        
        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);
        
        // Assert
        assertNotNull(result);
        assertEquals(queryParams, result.getQueryParams());
        assertNotNull(result.getProfile());
        assertNull(result.getProfile().getFirstName());
        assertTrue(result.getIsNameEncrypted());
    }

    private void setupSuccessfulProfileResponse() throws Exception {
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName("John");
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");
        
        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");
        
        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);
    }

    @Test
    public void testGetData_EmptyFirstName_UpiServiceSuccess() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(""); // Empty first name
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        FullName fullName = new FullName();
        fullName.setFirstName("Jane");
        fullName.setLastName("Smith");

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        when(mockVerifyVpaResponse.getPayeeDetails()).thenReturn(mockPayeeDetails);
        when(mockPayeeDetails.getPayeeName()).thenReturn("Jane Smith");
        when(mockUpiUserService.verifyVpa(any(GenericSearchRequestDTO.class)))
            .thenReturn(mockVerifyVpaResponse);

        PowerMockito.when(Utils.splitName("Jane Smith")).thenReturn(fullName);

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals("Jane", result.getProfile().getFirstName());
        assertEquals("Smith", result.getProfile().getLastName());
        assertFalse(result.getIsNameEncrypted());

        verify(mockUpiUserService).verifyVpa(argThat(request ->
            request.getGenericPaymentAddress().equals("9876543210") &&
            request.getPossibleTypes().contains(GenericSearchType.MOBILE_NUMBER)
        ));
    }

    @Test
    public void testGetData_NullFirstName_UpiServiceSuccess() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(null); // Null first name
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        FullName fullName = new FullName();
        fullName.setFirstName("Alice");
        fullName.setLastName("Johnson");

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        when(mockVerifyVpaResponse.getPayeeDetails()).thenReturn(mockPayeeDetails);
        when(mockPayeeDetails.getPayeeName()).thenReturn("Alice Johnson");
        when(mockUpiUserService.verifyVpa(any(GenericSearchRequestDTO.class)))
            .thenReturn(mockVerifyVpaResponse);

        PowerMockito.when(Utils.splitName("Alice Johnson")).thenReturn(fullName);

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals("Alice", result.getProfile().getFirstName());
        assertEquals("Johnson", result.getProfile().getLastName());
        assertFalse(result.getIsNameEncrypted());
    }

    @Test
    public void testGetData_EmptyFirstName_UpiServiceException() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(""); // Empty first name
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        when(mockUpiUserService.verifyVpa(any(GenericSearchRequestDTO.class)))
            .thenThrow(new RuntimeException("UPI service error"));

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals("", result.getProfile().getFirstName()); // Should remain empty
        assertEquals("Doe", result.getProfile().getLastName());
        assertTrue(result.getIsNameEncrypted()); // Should remain true (default)

        verify(mockUpiUserService).verifyVpa(any(GenericSearchRequestDTO.class));
    }

    @Test
    public void testGetData_EmptyFirstName_NullPhoneNumber() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(""); // Empty first name
        profile.setLastName("Doe");
        profile.setPhoneNo(null); // Null phone number

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        when(mockUpiUserService.verifyVpa(any(GenericSearchRequestDTO.class)))
            .thenThrow(new RuntimeException("UPI service error due to null phone"));

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals("", result.getProfile().getFirstName());
        assertEquals("Doe", result.getProfile().getLastName());
        assertTrue(result.getIsNameEncrypted());
    }

    @Test
    public void testGetData_WhitespaceFirstName_NoUpiServiceCall() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName("   "); // Whitespace first name - StringUtils.isEmpty("   ") returns false
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        // StringUtils.isEmpty("   ") returns false, so UPI service should NOT be called
        // The firstName should remain as whitespace
        assertEquals("   ", result.getProfile().getFirstName());
        assertEquals("Doe", result.getProfile().getLastName());
        assertTrue(result.getIsNameEncrypted()); // Should remain true (default)

        // Verify UPI service was NOT called
        verify(mockUpiUserService, never()).verifyVpa(any(GenericSearchRequestDTO.class));
    }

    @Test
    public void testGetData_EmptyQueryParams() throws Exception {
        // Arrange
        Map<String, Object> queryParams = new HashMap<>(); // Empty query params
        setupSuccessfulProfileResponse();

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertTrue(result.getQueryParams().isEmpty());
        assertNotNull(result.getProfile());
        assertEquals("John", result.getProfile().getFirstName());
    }

    @Test
    public void testGetData_NullQueryParams() throws Exception {
        // Arrange
        Map<String, Object> queryParams = null;
        setupSuccessfulProfileResponse();

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertNull(result.getQueryParams());
        assertNotNull(result.getProfile());
        assertEquals("John", result.getProfile().getFirstName());
    }

    @Test(expected = RuntimeException.class)
    public void testGetData_ConfigUtilsException() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();

        // Config utils throws exception - this should propagate since it's not caught
        when(mockConfigUtils.getEncryptionData()).thenThrow(new RuntimeException("Config error"));

        // Act - This should throw the RuntimeException
        leadPageDataSource.getData(mockMerchantUser, queryParams);
    }

    @Test
    public void testGetData_UpiServiceReturnsNullPayeeDetails() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(""); // Empty first name
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        when(mockVerifyVpaResponse.getPayeeDetails()).thenReturn(null); // Null payee details
        when(mockUpiUserService.verifyVpa(any(GenericSearchRequestDTO.class)))
            .thenReturn(mockVerifyVpaResponse);

        // Act & Assert - Should throw NullPointerException when trying to access payeeName
        try {
            leadPageDataSource.getData(mockMerchantUser, queryParams);
            // If no exception is thrown, the test should still pass as the exception is caught
        } catch (Exception e) {
            // Expected behavior - exception is caught and logged
        }

        // Verify UPI service was called
        verify(mockUpiUserService).verifyVpa(any(GenericSearchRequestDTO.class));
    }

    @Test
    public void testGetData_UpiServiceReturnsEmptyPayeeName() throws Exception {
        // Arrange
        Map<String, Object> queryParams = createTestQueryParams();
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(""); // Empty first name
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        FullName fullName = new FullName();
        fullName.setFirstName("");
        fullName.setLastName("");

        when(mockConfigUtils.getEncryptionData()).thenReturn(Optional.empty());
        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);

        when(mockVerifyVpaResponse.getPayeeDetails()).thenReturn(mockPayeeDetails);
        when(mockPayeeDetails.getPayeeName()).thenReturn(""); // Empty payee name
        when(mockUpiUserService.verifyVpa(any(GenericSearchRequestDTO.class)))
            .thenReturn(mockVerifyVpaResponse);

        PowerMockito.when(Utils.splitName("")).thenReturn(fullName);

        // Act
        LeadPageDataSourceResponse result = leadPageDataSource.getData(mockMerchantUser, queryParams);

        // Assert
        assertNotNull(result);
        assertEquals("", result.getProfile().getFirstName());
        assertEquals("", result.getProfile().getLastName());
        assertFalse(result.getIsNameEncrypted());
    }

    private void setupSuccessfulProfileResponseWithoutConfig() throws Exception {
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName("John");
        profile.setLastName("Doe");
        profile.setPhoneNo("9876543210");

        when(mockMerchantUser.getMerchantUserId()).thenReturn("merchant-user-123");
        when(mockMerchantUser.getSmUserId()).thenReturn("sm-user-123");
        when(mockMerchantUser.getMerchantKey()).thenReturn("FLIPKART");

        when(mockProfileService.getProfileByUserId("merchant-user-123", "sm-user-123", false))
            .thenReturn(profile);
    }

    private Map<String, Object> createTestQueryParams() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("applicationId", "test-app-id");
        queryParams.put("token", "test-token");
        return queryParams;
    }
}
