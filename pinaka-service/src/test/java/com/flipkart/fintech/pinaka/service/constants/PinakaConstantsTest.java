package com.flipkart.fintech.pinaka.service.constants;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateStatus;
import com.flipkart.fintech.pinaka.api.enums.*;
import com.flipkart.fintech.pinaka.api.response.dataprovider.Option;
import org.junit.Test;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class PinakaConstantsTest {

    @Test
    public void testTenantFlipkart() {
        assertEquals("flipkart", PinakaConstants.TENANT_FLIPKART);
    }

    @Test
    public void testIgnoreRhEventPlStateList() {
        List<String> ignoreList = PinakaConstants.IGNORE_RH_EVENT_PL_STATE_LIST;
        assertNotNull(ignoreList);
        assertEquals(9, ignoreList.size());
        assertTrue(ignoreList.contains("CUSTOMER_IDENTIFICATION_START"));
        assertTrue(ignoreList.contains("CUSTOMER_IDENTIFICATION_END"));
        assertTrue(ignoreList.contains("ELIGIBLE_OFFER_START"));
        assertTrue(ignoreList.contains("ELIGIBLE_OFFER_END"));
        assertTrue(ignoreList.contains("SUBMIT_OFFER_START"));
        assertTrue(ignoreList.contains("SUBMIT_OFFER_END"));
        assertTrue(ignoreList.contains("CREATE_PROFILE_END"));
        assertTrue(ignoreList.contains("CREATE_PROFILE_START"));
        assertTrue(ignoreList.contains("REJECTED"));
    }

    @Test
    public void testWorkflowFormKeyContextMap() {
        Map<UserNodeFormKey, List<ApplicationUpdateContext>> workflowMap = PinakaConstants.workflowFormKeyContextMap;
        assertNotNull(workflowMap);
        assertTrue(workflowMap.size() > 0);
        
        // Test specific mappings
        assertTrue(workflowMap.containsKey(UserNodeFormKey.EKYC_PAN_AADHAAR_SUBMIT));
        assertTrue(workflowMap.containsKey(UserNodeFormKey.FETCH_AADHAAR_OTP));
        assertTrue(workflowMap.containsKey(UserNodeFormKey.REVIEW_SCREEN));
        assertTrue(workflowMap.containsKey(UserNodeFormKey.COLLECT_PERMISSIONS));
        assertTrue(workflowMap.containsKey(UserNodeFormKey.KYC_REJECTED_RETRIABLE));
        
        // Test that some lists have expected content
        List<ApplicationUpdateContext> ekycContexts = workflowMap.get(UserNodeFormKey.EKYC_PAN_AADHAAR_SUBMIT);
        assertNotNull(ekycContexts);
        assertTrue(ekycContexts.contains(ApplicationUpdateContext.EKYC_PAN_AADHAAR_SUBMIT));
        
        List<ApplicationUpdateContext> reviewContexts = workflowMap.get(UserNodeFormKey.REVIEW_SCREEN);
        assertNotNull(reviewContexts);
        assertTrue(reviewContexts.contains(ApplicationUpdateContext.KYC_DISCARD));
        assertTrue(reviewContexts.contains(ApplicationUpdateContext.KYC_REVIEW_AND_SUBMIT));
    }

    @Test
    public void testWorkflowKycUpgradeFormKeyContextMap() {
        Map<UserNodeFormKey, List<KycUpdateContext>> kycUpgradeMap = PinakaConstants.workflowkycUpgradeFormKeyContextMap;
        assertNotNull(kycUpgradeMap);
        assertTrue(kycUpgradeMap.size() > 0);
        
        // Test specific mappings
        assertTrue(kycUpgradeMap.containsKey(UserNodeFormKey.AADHAAR_XML_KYC_PAN_SUBMIT));
        assertTrue(kycUpgradeMap.containsKey(UserNodeFormKey.INITIATE_AADHAAR_VERIFICATION));
        assertTrue(kycUpgradeMap.containsKey(UserNodeFormKey.REVIEW_SCREEN));
        
        // Test that some lists have expected content
        List<KycUpdateContext> xmlKycContexts = kycUpgradeMap.get(UserNodeFormKey.AADHAAR_XML_KYC_PAN_SUBMIT);
        assertNotNull(xmlKycContexts);
        assertTrue(xmlKycContexts.contains(KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
    }

    @Test
    public void testCbcUsageType() {
        List<Option> cbcUsageType = PinakaConstants.CBC_USAGE_TYPE;
        assertNotNull(cbcUsageType);
        assertEquals(2, cbcUsageType.size());
        
        Option internationalOption = cbcUsageType.stream()
                .filter(option -> "international".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(internationalOption);
        assertEquals("International & Domestic", internationalOption.getTitle());
        
        Option domesticOption = cbcUsageType.stream()
                .filter(option -> "domestic".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(domesticOption);
        assertEquals("Domestic", domesticOption.getTitle());
    }

    @Test
    public void testCbcAddressType() {
        List<Option> cbcAddressType = PinakaConstants.CBC_ADDRESS_TYPE;
        assertNotNull(cbcAddressType);
        assertEquals(2, cbcAddressType.size());
        
        Option currentOption = cbcAddressType.stream()
                .filter(option -> "current".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(currentOption);
        assertEquals("Current Residence", currentOption.getTitle());
        
        Option workOption = cbcAddressType.stream()
                .filter(option -> "work".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(workOption);
        assertEquals("Work", workOption.getTitle());
    }

    @Test
    public void testCbcYesNoType() {
        List<Option> cbcYesNoType = PinakaConstants.CBC_YES_NO_TYPE;
        assertNotNull(cbcYesNoType);
        assertEquals(2, cbcYesNoType.size());
        
        Option yesOption = cbcYesNoType.stream()
                .filter(option -> "yes".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(yesOption);
        assertEquals("Yes", yesOption.getTitle());
        
        Option noOption = cbcYesNoType.stream()
                .filter(option -> "no".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(noOption);
        assertEquals("No", noOption.getTitle());
    }

    @Test
    public void testStates() {
        List<Option> states = PinakaConstants.STATES;
        assertNotNull(states);
        assertTrue(states.size() > 30); // India has many states and UTs
        
        // Test a few specific states
        Option karnataka = states.stream()
                .filter(option -> "karnataka".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(karnataka);
        assertEquals("Karnataka", karnataka.getTitle());
        
        Option delhi = states.stream()
                .filter(option -> "delhi".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(delhi);
        assertEquals("Delhi", delhi.getTitle());
    }

    @Test
    public void testGenders() {
        List<Option> genders = PinakaConstants.GENDERS;
        assertNotNull(genders);
        assertEquals(3, genders.size());
        
        Option male = genders.stream()
                .filter(option -> "male".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(male);
        assertEquals("Male", male.getTitle());
        
        Option female = genders.stream()
                .filter(option -> "female".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(female);
        assertEquals("Female", female.getTitle());
        
        Option transgender = genders.stream()
                .filter(option -> "transgender".equals(option.getId()))
                .findFirst()
                .orElse(null);
        assertNotNull(transgender);
        assertEquals("Transgender", transgender.getTitle());
    }

    @Test
    public void testCbcGenderOptions() {
        Map<String, String> cbcGenderOptions = PinakaConstants.CBC_GENDER_OPTIONS;
        assertNotNull(cbcGenderOptions);
        assertEquals(3, cbcGenderOptions.size());
        assertEquals("Male", cbcGenderOptions.get("male"));
        assertEquals("Female", cbcGenderOptions.get("female"));
        assertEquals("Transgender", cbcGenderOptions.get("transgender"));
    }

    @Test
    public void testEducationOptions() {
        Map<String, String> educationOptions = PinakaConstants.EDUCATION_OPTIONS;
        assertNotNull(educationOptions);
        assertEquals(3, educationOptions.size());
        assertEquals("Undergraduate", educationOptions.get("undergraduate"));
        assertEquals("Graduate", educationOptions.get("graduate"));
        assertEquals("Post Graduate", educationOptions.get("post graduate"));
    }

    @Test
    public void testConfigFeature() {
        assertEquals(".cug.feature", PinakaConstants.ConfigFeature.FEATURE_NAME_PREFIX);
    }

    @Test
    public void testOfferOrchestratorConstants() {
        assertEquals("offerOrchestratorMode", PinakaConstants.OFFER_ORCHESTRATOR_MODE);
        assertEquals("SHADOW", PinakaConstants.OFFER_ORCHESTRATOR_MODE_SHADOW);
        assertEquals("LIVE", PinakaConstants.OFFER_ORCHESTRATOR_MODE_LIVE);
    }

    @Test
    public void testCacheContext() {
        assertEquals("PAN_RETRY_CLC_IB", PinakaConstants.CacheContext.PAN_RETRY_CLC_IB);
    }

    @Test
    public void testMandateStateMappers() {
        // Test pinakaToPandoraMandateStateMapper
        Map<MandateState, MandateStatus> pinakaToPandora = PinakaConstants.pinakaToPandoraMandateStateMapper;
        assertNotNull(pinakaToPandora);
        assertEquals(MandateStatus.SUCCESS, pinakaToPandora.get(MandateState.SUCCESS));
        assertEquals(MandateStatus.FAILURE, pinakaToPandora.get(MandateState.FAILED));

        // Test pandoraToPinakaMandateStateMapper
        Map<STATUS, MandateState> pandoraToPinaka = PinakaConstants.pandoraToPinakaMandateStateMapper;
        assertNotNull(pandoraToPinaka);
        assertEquals(MandateState.SUCCESS, pandoraToPinaka.get(STATUS.SUCCESS));
        assertEquals(MandateState.FAILED, pandoraToPinaka.get(STATUS.FAILURE));
    }

    @Test
    public void testKycConstants() {
        assertEquals("%s.%s.%s.kyc_reject.page.content", PinakaConstants.KYC.KYC_REJECT_PAGE_CONTENT_KEY);
        assertEquals("%s.%s.%s.%s.%s.kyc_reject.page.content", PinakaConstants.KYC.JOURNEY_SPECIFIC_KYC_REJECT_PAGE_CONTENT_KEY);
    }

    @Test
    public void testCbcTransactionValidToShowMap() {
        Map<String, Integer> transactionMap = PinakaConstants.CBC_TRANSACTION_VALID_TO_SHOW_MAP;
        assertNotNull(transactionMap);
        assertTrue(transactionMap.size() > 20);
        assertEquals(Integer.valueOf(1), transactionMap.get("CRADJ"));
        assertEquals(Integer.valueOf(1), transactionMap.get("05"));
        assertEquals(Integer.valueOf(1), transactionMap.get("PAYMT"));
        assertEquals(Integer.valueOf(1), transactionMap.get("CASH"));
    }

    @Test
    public void testCbcMccToIconMap() {
        Map<String, Integer> mccIconMap = PinakaConstants.CBC_MCC_TO_ICON_MAP;
        assertNotNull(mccIconMap);
        assertTrue(mccIconMap.size() > 10);
        assertEquals(Integer.valueOf(0), mccIconMap.get("FOOD"));
        assertEquals(Integer.valueOf(1), mccIconMap.get("TRAVEL"));
        assertEquals(Integer.valueOf(2), mccIconMap.get("HEALTH"));
        assertEquals(Integer.valueOf(10), mccIconMap.get("CASH"));
        assertEquals(Integer.valueOf(11), mccIconMap.get("ENTERTAINMENT"));
    }

    @Test
    public void testCompanyMasterDataConstants() {
        assertEquals("cbc.employer.index.pattern", PinakaConstants.CompanyMasterData.EMPLOYER_INDEX_PATTERN);
        assertEquals("employerSector", PinakaConstants.CompanyMasterData.EMPLOYER_SECTOR);
        assertEquals("industryType", PinakaConstants.CompanyMasterData.INDUSTRY_TYPE);
        assertEquals("Employer_Name", PinakaConstants.CompanyMasterData.EMPLOYER_NAME);
        assertEquals("cbcEmployerAutoSuggestSize", PinakaConstants.CompanyMasterData.EMPLOYER_AUTO_SUGGEST_SIZE);
        assertEquals(Integer.valueOf(10), PinakaConstants.CompanyMasterData.EMPLOYER_AUTO_SUGGEST_DEFAULT_SIZE);
        assertEquals("OTHERS", PinakaConstants.CompanyMasterData.OTHERS);
        assertEquals("fintech_cbc_employer_details*", PinakaConstants.CompanyMasterData.EMPLOYER_INDEX_PATTERN_DEFAULT);
    }

    @Test
    public void testGeneralConstants() {
        assertEquals("|", PinakaConstants.PIPE_SEPARATOR);
        assertEquals("", PinakaConstants.EMPTY_STRING);
        assertEquals("yyyy-MM-dd HH:mm:ss", PinakaConstants.DATE_FORMAT_PATTERN);
    }

    @Test
    public void testPLConstants() {
        assertEquals("applicationId", PinakaConstants.PLConstants.APPLICATION_ID);
        assertEquals("https://www.shopsy.in/sm-3p/pl/pages/status", PinakaConstants.PLConstants.PL_STATUS_URL);
        assertEquals("taskId", PinakaConstants.PLConstants.TASK_ID);
        assertEquals("Please check the details and Retry", PinakaConstants.PLConstants.RETRY_WITH_EDIT_MESSAGE);
        assertEquals("Please retry after sometime", PinakaConstants.PLConstants.RETRY_WITHOUT_EDIT_MESSAGE);
        assertEquals("CCv7SRrJuatGmf3C", PinakaConstants.PLConstants.PL_ENCRYPTION_KEY);
        assertEquals(5, PinakaConstants.PLConstants.ROW_COUNT);
        assertEquals(" ", PinakaConstants.PLConstants.SPACE);
        
        // Test terminal states
        List<String> terminalStates = PinakaConstants.PLConstants.PL_TERMINAL_STATES;
        assertNotNull(terminalStates);
        assertEquals(4, terminalStates.size());
        assertTrue(terminalStates.contains("APPLICATION_COMPLETED"));
        assertTrue(terminalStates.contains("SUCCESS"));
        assertTrue(terminalStates.contains("REJECTED"));
        assertTrue(terminalStates.contains("DISBURSAL_IN_PROGRESS"));
    }

    @Test
    public void testPLAlfredConstants() {
        assertEquals("20230301", PinakaConstants.PLAlfredConstants.ALFRED_CLIENT_VERSION);
        assertEquals("CBC-JRM-V1", PinakaConstants.PLAlfredConstants.CBC_JRM_V1);
        assertEquals("CBC-JRM-V3", PinakaConstants.PLAlfredConstants.CBC_JRM_V3);
        assertEquals("Address-Confidence-V1", PinakaConstants.PLAlfredConstants.ADDRESS_CONFIDENCE_V1);
        assertEquals("most_used_address_id", PinakaConstants.PLAlfredConstants.MOST_USED_ADDRESS_ID);
        assertEquals("SCORE", PinakaConstants.PLAlfredConstants.SCORE);
        assertEquals("BIN", PinakaConstants.PLAlfredConstants.BIN);
        assertEquals("cremo_band", PinakaConstants.PLAlfredConstants.CREMO_BAND);
        assertEquals("BNPL_UNDERWRITING", PinakaConstants.PLAlfredConstants.BNPL_UNDERWRITING);
    }

    @Test
    public void testNestedClassInstantiation() {
        // Test that nested classes can be instantiated
        PinakaConstants pinakaConstants = new PinakaConstants();
        PinakaConstants.ConfigFeature configFeature = pinakaConstants.new ConfigFeature();
        assertNotNull(configFeature);

        PinakaConstants.CacheContext cacheContext = pinakaConstants.new CacheContext();
        assertNotNull(cacheContext);

        PinakaConstants.KYC kyc = new PinakaConstants.KYC();
        assertNotNull(kyc);

        PinakaConstants.CompanyMasterData companyMasterData = new PinakaConstants.CompanyMasterData();
        assertNotNull(companyMasterData);

        PinakaConstants.PLConstants plConstants = new PinakaConstants.PLConstants();
        assertNotNull(plConstants);

        PinakaConstants.PLAlfredConstants plAlfredConstants = new PinakaConstants.PLAlfredConstants();
        assertNotNull(plAlfredConstants);
    }
}
