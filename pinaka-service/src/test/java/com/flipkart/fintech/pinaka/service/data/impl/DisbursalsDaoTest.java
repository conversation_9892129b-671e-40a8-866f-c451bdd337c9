package com.flipkart.fintech.pinaka.service.data.impl;

import com.flipkart.fintech.pinaka.service.data.model.DisbursalsEntity;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import javax.persistence.criteria.*;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DisbursalsDaoTest {

    @Mock
    private SessionFactory sessionFactory;
    @Mock
    private Session session;
    @Mock
    private CriteriaBuilder criteriaBuilder;
    @Mock
    private CriteriaQuery<DisbursalsEntity> criteriaQuery;
    @Mock
    private Root<DisbursalsEntity> root;
    @Mock
    private Predicate predicate;
    @Mock
    private Query<DisbursalsEntity> query;

    private DisbursalsDao disbursalsDao;

    @Before
    public void setUp() {
        lenient().when(sessionFactory.getCurrentSession()).thenReturn(session);
        disbursalsDao = new DisbursalsDao(sessionFactory);
    }

    @Test
    public void testGetByApplicationId_Found() {
        DisbursalsEntity entity = new DisbursalsEntity();
        String appId = "app123";
        // AbstractDAO#get delegates to session.get
        when(session.get(DisbursalsEntity.class, appId)).thenReturn(entity);

        Optional<DisbursalsEntity> result = disbursalsDao.getByApplicationId(appId);

        assertTrue(result.isPresent());
        assertEquals(entity, result.get());
    }

    @Test
    public void testGetByApplicationId_NotFound() {
        String appId = "app123";
        when(session.get(DisbursalsEntity.class, appId)).thenReturn(null);

        Optional<DisbursalsEntity> result = disbursalsDao.getByApplicationId(appId);

        assertFalse(result.isPresent());
    }

    @Test
    public void testGetBySmUserId() {
        String smUserId = "user123";
        DisbursalsEntity entity1 = new DisbursalsEntity();
        DisbursalsEntity entity2 = new DisbursalsEntity();
        List<DisbursalsEntity> expectedList = Arrays.asList(entity1, entity2);

        when(session.getCriteriaBuilder()).thenReturn(criteriaBuilder);
        when(criteriaBuilder.createQuery(DisbursalsEntity.class)).thenReturn(criteriaQuery);
        when(criteriaQuery.from(DisbursalsEntity.class)).thenReturn(root);
        when(criteriaBuilder.equal(root.get(DisbursalsDao.SM_USER_ID), smUserId)).thenReturn(predicate);
        when(session.createQuery(criteriaQuery)).thenReturn(query);
        when(query.getResultList()).thenReturn(expectedList);

        List<DisbursalsEntity> result = disbursalsDao.getBySmUserId(smUserId);

        assertEquals(expectedList, result);
    }

    @Test
    public void testSaveOrUpdate() {
        DisbursalsEntity entity = new DisbursalsEntity();
        doNothing().when(session).saveOrUpdate(entity);

        disbursalsDao.saveOrUpdate(session, entity);

        verify(session, times(1)).saveOrUpdate(entity);
    }
}