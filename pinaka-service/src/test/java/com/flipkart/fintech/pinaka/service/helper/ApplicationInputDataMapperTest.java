package com.flipkart.fintech.pinaka.service.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ApplicationInputDataMapper.class, ObjectMapperUtil.class})
public class ApplicationInputDataMapperTest {

    private ObjectMapper mockObjectMapper;

    @Before
    public void setup() throws Exception {
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        mockObjectMapper = Mockito.mock(ObjectMapper.class);
        Mockito.when(ObjectMapperUtil.get()).thenReturn(mockObjectMapper);
    }

    @Test
    public void testGetIncomeDetails_withBasicDetails() {
        Map<String, Object> input = new HashMap<>();
        input.put("basicDetails", "basicValue");

        Map<String, Object> expected = Collections.singletonMap("income", "value");
        Mockito.when(mockObjectMapper.convertValue(eq("basicValue"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getIncomeDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetBasicDetails_withLeadV4LandingPage() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV4Page1", "nameValue");
        Map<String, Object> expected = Collections.singletonMap("income", "value");
        Mockito.when(mockObjectMapper.convertValue(eq("nameValue"), any(TypeReference.class))).thenReturn(expected);
        Map<String, Object> result = ApplicationInputDataMapper.getBasicDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetNameDetails_withLeadV4LandingPage() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV4LandingPage", "basicValue");
        Map<String, Object> expected = Collections.singletonMap("income", "value");
        Mockito.when(mockObjectMapper.convertValue(eq("basicValue"), any(TypeReference.class))).thenReturn(expected);
        Map<String, Object> result = ApplicationInputDataMapper.getNameDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetIncomeDetails_withNullResult() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV3Page1", "fallback");

        Mockito.when(mockObjectMapper.convertValue(eq("fallback"), any(TypeReference.class))).thenReturn(null);

        Map<String, Object> result = ApplicationInputDataMapper.getIncomeDetails(input);
        assertNull(result);
    }

    @Test
    public void testGetBasicDetails_withFallback() {
        Map<String, Object> input = new HashMap<>();
        input.put("reviewPage1", "review");

        Map<String, Object> expected = Collections.singletonMap("gender", "M");
        Mockito.when(mockObjectMapper.convertValue(eq("review"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getBasicDetails(input);
        assertEquals("M", result.get("gender"));
    }

    @Test
    public void testGetNameDetails_withFallback() {
        Map<String, Object> input = new HashMap<>();
        input.put("namePage", "nameData");

        Map<String, Object> expected = Collections.singletonMap("firstName", "John");
        Mockito.when(mockObjectMapper.convertValue(eq("nameData"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getNameDetails(input);
        assertEquals("John", result.get("firstName"));
    }

    @Test
    public void testPrivateConstructor_throwsException() throws Exception {
        try {
            ApplicationInputDataMapper.class.getDeclaredConstructor().newInstance();
            fail("Expected IllegalStateException");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof IllegalStateException);
            assertEquals("Utility class", e.getCause().getMessage());
        }
    }

    @Test
    public void testGetIncomeDetails_withReviewPage2() {
        Map<String, Object> input = new HashMap<>();
        input.put("reviewPage2", "review2Data");

        Map<String, Object> expected = Collections.singletonMap("key", "value2");
        Mockito.when(mockObjectMapper.convertValue(eq("review2Data"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getIncomeDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetIncomeDetails_withLeadV3Page2() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV3Page2", "leadV3Page2Data");

        Map<String, Object> expected = Collections.singletonMap("key", "value3");
        Mockito.when(mockObjectMapper.convertValue(eq("leadV3Page2Data"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getIncomeDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetIncomeDetails_withLeadV4Page1() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV4Page1", "leadV4Page1Data");

        Map<String, Object> expected = Collections.singletonMap("income", "12000");
        Mockito.when(mockObjectMapper.convertValue(eq("leadV4Page1Data"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getIncomeDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetIncomeDetails_withLeadV4Page2() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV4Page2", "leadV4Page2Data");

        Map<String, Object> expected = Collections.singletonMap("income", "12000");
        Mockito.when(mockObjectMapper.convertValue(eq("leadV4Page2Data"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getIncomeDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetBasicDetails_withLeadV3Page1() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV3Page1", "leadV3Page1Data");

        Map<String, Object> expected = Collections.singletonMap("basicKey", "basicValue");
        Mockito.when(mockObjectMapper.convertValue(eq("leadV3Page1Data"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getBasicDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetNameDetails_withLeadV3NamePage() {
        Map<String, Object> input = new HashMap<>();
        input.put("leadV3NamePage", "leadV3NameData");

        Map<String, Object> expected = Collections.singletonMap("nameKey", "nameValue");
        Mockito.when(mockObjectMapper.convertValue(eq("leadV3NameData"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getNameDetails(input);
        assertEquals(expected, result);
    }

    @Test
    public void testGetBasicDetails_returnsEmptyMap_whenConvertValueReturnsNull() {
        Map<String, Object> input = new HashMap<>();
        input.put("basicDetails", "someValue");

        // Mock convertValue to return null to simulate that branch
        Mockito.when(mockObjectMapper.convertValue(eq("someValue"), any(TypeReference.class)))
                .thenReturn(null);

        Map<String, Object> result = ApplicationInputDataMapper.getBasicDetails(input);

        assertNull(result);
    }

    @Test
    public void testGetNameDetails_withBasicDetailsKey() {
        Map<String, Object> input = new HashMap<>();
        input.put("basicDetails", "basicDetailsData");

        Map<String, Object> expected = Collections.singletonMap("key", "value");
        Mockito.when(mockObjectMapper.convertValue(eq("basicDetailsData"), any(TypeReference.class))).thenReturn(expected);

        Map<String, Object> result = ApplicationInputDataMapper.getNameDetails(input);

        assertEquals(expected, result);
    }

}
