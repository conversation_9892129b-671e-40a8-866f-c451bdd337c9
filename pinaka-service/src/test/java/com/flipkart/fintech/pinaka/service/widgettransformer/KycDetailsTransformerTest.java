package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.response.*;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResult;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.flipkart.fintech.pinaka.service.core.v6.DocumentService;
import com.flipkart.fintech.pinaka.service.response.KycDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.*;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.KeyValueWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.testng.AssertJUnit.assertTrue;

@RunWith(MockitoJUnitRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@PrepareForTest({TransformerUtils.class, ObjectMapperUtil.class})
public class KycDetailsTransformerTest {

    @Mock
    private DocumentService documentService;

    @Mock
    private TransformerUtils transformerUtils;

    private KycDetailsTransformer kycDetailsTransformer;
    private KycDetailsPageDataSourceResponse dataSourceResponse;
    private KeyValueWidgetData keyValueWidgetData;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        kycDetailsTransformer = new KycDetailsTransformer(documentService);

        // Set up mock KeyValueWidgetData with basic structure
        keyValueWidgetData = new KeyValueWidgetData();
        List<KeyValue> keyValuePairs = new ArrayList<>();
        KeyValue imageKeyValue = new KeyValue();
        ImageValue imageValue = new ImageValue();
        imageValue.setDynamicImageUrl("");
        imageKeyValue.setImageValue(imageValue);
        keyValuePairs.add(imageKeyValue);
        keyValueWidgetData.setKeyValuePairs(keyValuePairs);

        // Set up submit button
        SubmitButtonValue submitButton = new SubmitButtonValue();
        RenderableComponent<RichButtonValue> button = new RenderableComponent<>();
        Action action = new Action();
        button.setAction(action);
        submitButton.setButton(button);
        keyValueWidgetData.setSubmitButton(submitButton);

        // Prepare dataSourceResponse
        dataSourceResponse = new KycDetailsPageDataSourceResponse();
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("testKey", "testValue");
        dataSourceResponse.setQueryParams(queryParams);

    }

    @Test
    public void testBuildKeyValueWidgetData_WithDigilockerResponse() throws JsonProcessingException {
        // Prepare test data for digilocker response
        DigilockerAadhaarResponse digilockerResponse = new DigilockerAadhaarResponse();
        DigilockerAadhaarResult digilockerAadhaarResult = new DigilockerAadhaarResult();
        digilockerAadhaarResult.setName("Test User");
        digilockerAadhaarResult.setDob("01-01-1990");
        digilockerAadhaarResult.setAddress("123 Test Street, Test City");
        digilockerAadhaarResult.setPhoto("base64EncodedPhoto");
        digilockerResponse.setResult(digilockerAadhaarResult);

        dataSourceResponse.setDigilockerAadhaarResponse(digilockerResponse);

        // Mock ObjectMapperUtil to return our prepared keyValueWidgetData
//        ObjectMapperUtil.get().readValue(any(String.class), any(Class.class));

        // Execute the method under test
        KeyValueWidgetData result = kycDetailsTransformer.buildkeyValueWidgetData(dataSourceResponse, false);

        // Verify results
        assertNotNull(result);
        List<KeyValue> keyValuePairs = result.getKeyValuePairs();

        // First item should be the image
        assertEquals("data:image/jpeg;base64,base64EncodedPhoto", keyValuePairs.get(0).getImageValue().getDynamicImageUrl());

        // Check that proper key-value pairs are added
        boolean hasFullName = false;
        boolean hasDOB = false;
        boolean hasAddress = false;

        for (KeyValue kv : keyValuePairs) {
            if ("Full name".equals(kv.getKey()) && "Test User".equals(kv.getValue())) {
                hasFullName = true;
            }
            if ("DOB".equals(kv.getKey()) && "01-01-1990".equals(kv.getValue())) {
                hasDOB = true;
            }
            if ("Address".equals(kv.getKey()) && "123 Test Street, Test City".equals(kv.getValue())) {
                hasAddress = true;
            }
        }

        assertTrue(hasFullName);
        assertTrue(hasDOB);
        assertTrue(hasAddress);
    }

    @Test
    public void testBuildKeyValueWidgetData_WithEkyc() throws Exception {
        // Prepare test data for eKYC
        AadhaarValidateOtpResponse aadhaarResponse = new AadhaarValidateOtpResponse();
        AadhaarValidateOtpKyc kyc = new AadhaarValidateOtpKyc();
        kyc.setName("Test User EKYC");
        kyc.setGender("Male");
        kyc.setDob("01-01-1990");
        kyc.setImageId("image123");

        // Create address
        AadhaarValidateOtpAddress address = new AadhaarValidateOtpAddress();
        address.setHouse("House 123");
        address.setVtc("Test City");
        address.setState("Test State");
        address.setCountry("Test Country");
        address.setPc("123456");
        kyc.setAddress(address);

        aadhaarResponse.setKyc(kyc);
        dataSourceResponse.setAadhaarValidateOtpResponse(aadhaarResponse);

        // Mock document service response
        DocumentDownloadResponse downloadResponse = new DocumentDownloadResponse();
        downloadResponse.setBase64EncodedImage("imageData123");
        Map<String, String> metadata = new HashMap<>();
        metadata.put("documenttype", "KYC_IMAGE");
        downloadResponse.setImageMetaData(metadata);
        when(documentService.downloadDocument("image123")).thenReturn(downloadResponse);

        // Execute the method under test
        KeyValueWidgetData result = kycDetailsTransformer.buildkeyValueWidgetData(dataSourceResponse, true);

        // Verify results
        assertNotNull(result);
        List<KeyValue> keyValuePairs = result.getKeyValuePairs();

        // First item should be the image
        assertEquals("data:image/jpeg;base64,imageData123", keyValuePairs.get(0).getImageValue().getDynamicImageUrl());

        // Check that proper key-value pairs are added
        boolean hasFullName = false;
        boolean hasGender = false;
        boolean hasDOB = false;
        boolean hasAddress = false;

        for (KeyValue kv : keyValuePairs) {
            if ("Full name".equals(kv.getKey()) && "Test User EKYC".equals(kv.getValue())) {
                hasFullName = true;
            }
            if ("Gender".equals(kv.getKey()) && "Male".equals(kv.getValue())) {
                hasGender = true;
            }
            if ("DOB".equals(kv.getKey()) && "01-01-1990".equals(kv.getValue())) {
                hasDOB = true;
            }
            if ("Address".equals(kv.getKey()) && "House 123, Test City, Test State, Test Country, 123456".equals(kv.getValue())) {
                hasAddress = true;
            }
        }

        assertTrue(hasFullName);
        assertTrue(hasGender);
        assertTrue(hasDOB);
        assertTrue(hasAddress);
    }

//    @Test
//    public void testBuildKeyValueWidgetData_WithCkycResponse() throws Exception {
//        // Prepare test data for CKYC
//        SearchCkycResponse ckycResponse = new SearchCkycResponse();
//        CkycKyc kyc = new CkycKyc();
//
//        // Create personal details
//        PersonalDetail personalDetail = new PersonalDetail();
//        personalDetail.setCKycFullName("Test User CKYC");
//        personalDetail.setCKycFatherFullName("Father Name");
//        personalDetail.setCkycdob("01-01-1990");
//        List<PersonalDetail> personalDetails = new ArrayList<>();
//        personalDetails.add(personalDetail);
//        kyc.setPersonalDetails(personalDetails);
//
//        // Create image
//        CkycImage image = new CkycImage();
//        image.setId("image456");
//        List<CkycImage> images = new ArrayList<>();
//        images.add(image);
//        kyc.setImages(images);
//
//        ckycResponse.setKyc(kyc);
//
//        // Create address
//        CkycAddress address = new CkycAddress();
//        address.setAddressLine1("123 CKYC Street");
//        address.setCity("CKYC City");
//        address.setState("CKYC State");
//        address.setCountry("CKYC Country");
//        address.setPincode("654321");
//        ckycResponse.setAddress(address);
//
//        dataSourceResponse.setSearchCkycResponse(ckycResponse);
//
//        // Mock document service response
//        DocumentDownloadResponse downloadResponse = new DocumentDownloadResponse();
//        downloadResponse.setBase64EncodedImage("imageData456");
//        Map<String, String> metadata = new HashMap<>();
//        metadata.put("documenttype", "KYC_IMAGE");
//        downloadResponse.setImageMetaData(metadata);
//        when(documentService.downloadDocument("image456")).thenReturn(downloadResponse);
//
//        // Execute the method under test
//        KeyValueWidgetData result = kycDetailsTransformer.buildkeyValueWidgetData(dataSourceResponse, false);
//
//        // Verify results
//        assertNotNull(result);
//        List<KeyValue> keyValuePairs = result.getKeyValuePairs();
//
//        // First item should be the image
//        assertEquals("data:image/jpeg;base64,imageData456", keyValuePairs.get(0).getImageValue().getDynamicImageUrl());
//
//        // Check that proper key-value pairs are added
//        boolean hasFullName = false;
//        boolean hasFatherName = false;
//        boolean hasDOB = false;
//        boolean hasAddress = false;
//
//        for (KeyValue kv : keyValuePairs) {
//            if ("Full name".equals(kv.getKey()) && "Test User CKYC".equals(kv.getValue())) {
//                hasFullName = true;
//            }
//            if ("Father's name".equals(kv.getKey()) && "Father Name".equals(kv.getValue())) {
//                hasFatherName = true;
//            }
//            if ("DOB".equals(kv.getKey()) && "01-01-1990".equals(kv.getValue())) {
//                hasDOB = true;
//            }
//            if ("Address".equals(kv.getKey()) && "123 CKYC Street, CKYC City, CKYC State, CKYC Country, 654321".equals(kv.getValue())) {
//                hasAddress = true;
//            }
//        }
//
//        assertTrue(hasFullName);
//        assertTrue(hasFatherName);
//        assertTrue(hasDOB);
//        assertTrue(hasAddress);
//    }

    @Test
    public void testBuildTextV2WidgetData_Normal() throws JsonProcessingException {
        RichMessageWidgetData mockWidgetData = new RichMessageWidgetData();
        RenderableComponent<RichMessageValue> renderableComponent = new RenderableComponent<>();
        RichMessageValue richMessageValue = new RichMessageValue();
        RichTextValue richTextValue = new RichTextValue();
        richTextValue.setText("The below KYC has been retrieved from central KYC registry.");
        richMessageValue.setTitle(richTextValue);
        renderableComponent.setValue(richMessageValue);
        mockWidgetData.setMessage(renderableComponent);

        // Execute the method under test
        RichMessageWidgetData result = kycDetailsTransformer.buildtextV2WidgetData(false);

        // Verify results
        assertNotNull(result);
        assertEquals(mockWidgetData.getMessage().getValue().getTitle().getText(), result.getMessage().getValue().getTitle().getText());
    }

    @Test
    public void testBuildTextV2WidgetData_Ekyc() throws JsonProcessingException {
        // Mock ObjectMapperUtil
        RichMessageWidgetData mockWidgetData = new RichMessageWidgetData();
        RenderableComponent<RichMessageValue> renderableComponent = new RenderableComponent<>();
        RichMessageValue richMessageValue = new RichMessageValue();
        RichTextValue richTextValue = new RichTextValue();
        richTextValue.setText("The below KYC has been retrieved from KYC registry.");
        richMessageValue.setTitle(richTextValue);
        renderableComponent.setValue(richMessageValue);
        mockWidgetData.setMessage(renderableComponent);

        // Execute the method under test
        RichMessageWidgetData result = kycDetailsTransformer.buildtextV2WidgetData( true);

        // Verify results
        assertNotNull(result);
        assertEquals(mockWidgetData.getMessage().getValue().getTitle().getText(), result.getMessage().getValue().getTitle().getText());
    }

    @Test
    public void testUpdateSubmitButton() {
        // Execute the method under test
        kycDetailsTransformer.updateSubmitButton(dataSourceResponse, keyValueWidgetData);

        // Verify results
        Action resultAction = keyValueWidgetData.getSubmitButton().getButton().getAction();
        assertEquals(dataSourceResponse.getQueryParams(), resultAction.getParams());
        assertEquals(dataSourceResponse.getEncryptionData(), resultAction.getEncryption());
    }

}
