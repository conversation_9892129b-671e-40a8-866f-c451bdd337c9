package com.flipkart.fintech.pinaka.service.subscriber;

import org.junit.Test;
import static org.junit.Assert.*;

public class LoanDisbursalDataSubscriberConfigTest {

    @Test
    public void testGettersAndSetters() {
        LoanDisbursalDataSubscriberConfig config = new LoanDisbursalDataSubscriberConfig();
        config.setProjectId("test-project");
        config.setSubscriptionId("test-subscription");

        assertEquals("test-project", config.getProjectId());
        assertEquals("test-subscription", config.getSubscriptionId());
    }

    @Test
    public void testEqualsAndHashCode() {
        LoanDisbursalDataSubscriberConfig config1 = new LoanDisbursalDataSubscriberConfig();
        config1.setProjectId("p1");
        config1.setSubscriptionId("s1");

        LoanDisbursalDataSubscriberConfig config2 = new LoanDisbursalDataSubscriberConfig();
        config2.setProjectId("p1");
        config2.setSubscriptionId("s1");

        assertEquals(config1, config2);
        assertEquals(config1.hashCode(), config2.hashCode());

        config2.setSubscriptionId("s2");
        assertNotEquals(config1, config2);
    }

    @Test
    public void testToString() {
        LoanDisbursalDataSubscriberConfig config = new LoanDisbursalDataSubscriberConfig();
        config.setProjectId("p1");
        config.setSubscriptionId("s1");

        String str = config.toString();
        assertTrue(str.contains("p1"));
        assertTrue(str.contains("s1"));
    }
}