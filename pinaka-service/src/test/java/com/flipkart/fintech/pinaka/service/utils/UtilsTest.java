package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.fintech.pinaka.api.model.FullName;
import org.junit.Test;

import static org.junit.Assert.*;

public class UtilsTest {

    @Test
    public void testSplitName_FullNameWithTwoParts() {
        // Arrange
        String fullName = "John <PERSON>e";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("<PERSON>", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }

    @Test
    public void testSplitName_FullNameWithThreeParts() {
        // Arrange
        String fullName = "<PERSON>";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("<PERSON> Michael", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }

    @Test
    public void testSplitName_FullNameWithFourParts() {
        // Arrange
        String fullName = "<PERSON>";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("<PERSON> <PERSON>", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }

    @Test
    public void testSplitName_SingleName() {
        // Arrange
        String fullName = "John";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John", result.getFirstName());
        assertNull(result.getLastName());
    }

    @Test
    public void testSplitName_EmptyString() {
        // Arrange
        String fullName = "";

        // Act
        FullName result = Utils.splitName(fullName);

        // Assert
        assertNotNull(result);
        // Empty string after trim and split gives [""], so firstName will be ""
        assertEquals("", result.getFirstName());
        assertNull(result.getLastName());
    }

    @Test
    public void testSplitName_WhitespaceOnly() {
        // Arrange
        String fullName = "   ";

        // Act
        FullName result = Utils.splitName(fullName);

        // Assert
        assertNotNull(result);
        // Whitespace only after trim becomes "", then split gives [""], so firstName will be ""
        assertEquals("", result.getFirstName());
        assertNull(result.getLastName());
    }

    @Test
    public void testSplitName_MultipleSpacesBetweenNames() {
        // Arrange
        String fullName = "John    Doe";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }

    @Test
    public void testSplitName_LeadingAndTrailingSpaces() {
        // Arrange
        String fullName = "  John Doe  ";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }

    @Test
    public void testSplitName_SingleNameWithSpaces() {
        // Arrange
        String fullName = "  John  ";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John", result.getFirstName());
        assertNull(result.getLastName());
    }

    @Test
    public void testSplitName_ComplexNameWithMultipleSpaces() {
        // Arrange
        String fullName = "  John   Michael   Smith   Doe  ";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John Michael Smith", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }

    @Test
    public void testSplitName_NameWithSpecialCharacters() {
        // Arrange
        String fullName = "John-Michael O'Doe";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John-Michael", result.getFirstName());
        assertEquals("O'Doe", result.getLastName());
    }

    @Test
    public void testSplitName_VeryLongName() {
        // Arrange
        String fullName = "John Michael Alexander Christopher Benjamin Doe";
        
        // Act
        FullName result = Utils.splitName(fullName);
        
        // Assert
        assertNotNull(result);
        assertEquals("John Michael Alexander Christopher Benjamin", result.getFirstName());
        assertEquals("Doe", result.getLastName());
    }
}
