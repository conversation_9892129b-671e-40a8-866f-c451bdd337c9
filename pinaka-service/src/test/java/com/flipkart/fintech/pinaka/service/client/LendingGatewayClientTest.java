package com.flipkart.fintech.pinaka.service.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.configuration.requests.ApiRequest;
import com.sumo.bff.models.runtime_get_api.request.RuntimeGetApiResponse;
import org.junit.Before;
import org.junit.Test;

import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.Response;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class LendingGatewayClientTest {

    private WebTarget webTarget;
    private ObjectMapper objectMapper;
    private LendingGatewayClient client;
    private ApiRequest apiRequest;
    private Invocation.Builder builder;
    private Response response;

    @Before
    public void setUp() {
        webTarget = mock(WebTarget.class);
        objectMapper = mock(ObjectMapper.class);
        client = new LendingGatewayClient(webTarget, objectMapper);
        apiRequest = mock(ApiRequest.class);
        builder = mock(Invocation.Builder.class);
        response = mock(Response.class);

        when(apiRequest.getDataApiName()).thenReturn("APPLY_NOW");
        when(apiRequest.getUserRequestContext()).thenReturn(
                com.sumo.bff.models.common.UserRequestContext.builder().accountId("user1").build()
        );
    }

    @Test
    public void testGetData_Success() throws Exception {
        String requestStr = "{}";
        String jsonResponse = "{\"data\":{}}";
        RuntimeGetApiResponse<PageActionResponse> expected = new RuntimeGetApiResponse<>();

        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(builder);
        when(builder.header(anyString(), anyString())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(200);
        when(response.readEntity(String.class)).thenReturn(jsonResponse);
        when(objectMapper.writeValueAsString(apiRequest)).thenReturn(requestStr);
        when(objectMapper.readValue(eq(jsonResponse), any(TypeReference.class))).thenReturn(expected);

        RuntimeGetApiResponse<PageActionResponse> result = client.getData(apiRequest);

        assertEquals(expected, result);
        verify(objectMapper).writeValueAsString(apiRequest);
        verify(objectMapper).readValue(eq(jsonResponse), any(TypeReference.class));
    }

    @Test
    public void testGetData_ErrorStatus() throws Exception {
        String requestStr = "{}";
        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(builder);
        when(builder.header(anyString(), anyString())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(500);
        when(objectMapper.writeValueAsString(apiRequest)).thenReturn(requestStr);

        RuntimeException ex = assertThrows(RuntimeException.class, () -> client.getData(apiRequest));
        assertTrue(ex.getMessage().contains("Got Error In Lending Gateway client"));
    }

    @Test
    public void testGetData_JsonProcessingException() throws Exception {
        when(objectMapper.writeValueAsString(apiRequest)).thenThrow(new JsonProcessingException("error") {});

        assertThrows(RuntimeException.class, () -> client.getData(apiRequest));
    }

    @Test
    public void testPostSubmit_Success() throws Exception {
        String requestStr = "{}";
        String jsonResponse = "{\"data\":{}}";
        RuntimeGetApiResponse<PageActionResponse> expected = new RuntimeGetApiResponse<>();

        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(builder);
        when(builder.header(anyString(), anyString())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(200);
        when(response.readEntity(String.class)).thenReturn(jsonResponse);
        when(objectMapper.writeValueAsString(apiRequest)).thenReturn(requestStr);
        when(objectMapper.readValue(eq(jsonResponse), any(TypeReference.class))).thenReturn(expected);

        RuntimeGetApiResponse<PageActionResponse> result = client.postSubmit(apiRequest);

        assertEquals(expected, result);
    }

    @Test
    public void testPostSubmit_ErrorStatus() throws Exception {
        String requestStr = "{}";
        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(builder);
        when(builder.header(anyString(), anyString())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(400);
        when(objectMapper.writeValueAsString(apiRequest)).thenReturn(requestStr);

        assertThrows(RuntimeException.class, () -> client.postSubmit(apiRequest));
    }
}