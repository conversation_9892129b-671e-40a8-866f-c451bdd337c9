package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import static org.testng.AssertJUnit.assertEquals;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV3NamePageFormTransformer.class,
        InitialUserReviewDataSource.class,
        ReviewUserDataSourceResponse.class,
        LeadPageDataSourceResponse.class,
        LeadPageDataSource.class,
        ObjectMapperUtil.class,
        LV3Util.class,
})
public class LV3NamePageFormTransformerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private LocationRequestHandler locationRequestHandler;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private Decrypter decrypter;

    @Mock
    private InitialUserReviewDataSource initialUserReviewDataSource;
    @Mock
    private LeadPageDataSource leadPageDataSource;
    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    private LeadPageDataSourceResponse leadPageDataSourceResponse;

    @Mock
    private FormWidgetDataFetcher formWidgetDataFetcher;
    @Mock
    private FormWidgetDataJsonParser formWidgetDataJsonParser;
    @Mock
    private FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    @Mock
    private BqIngestionHelper bqIngestionHelper;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(LV3Util.class);
        PowerMockito.doNothing().when(LV3Util.class, "updateGroupedWidgetSubmitButton", any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        PowerMockito.when(LV3Util.class, "getLeadV3Events", any(ApplicationDataResponse.class), any(String.class), any(String.class)).thenReturn(LeadV3Events.newBuilder().build());
    }

    @Test
    public void testLv3NamePageBannerWidgetData() throws PinakaException {
        String bannerUrl = "https://example.com/banner.jpg";
        LV3NamePageFormTransformer.LV3NamePageBannerFormTransformer bannerFormTransformer = new LV3NamePageFormTransformer.LV3NamePageBannerFormTransformer(dynamicBucket);
        when(dynamicBucket.getString("dynamicImageBannerUrl")).thenReturn(bannerUrl);
        BannerWidgetData bannerWidgetData = bannerFormTransformer.buildBannerWidgetData(applicationDataResponse);
        assertEquals(bannerUrl, bannerWidgetData.getRenderableComponents().get(0).getValue().getDynamicImageUrl());
    }

    @Test(expected = PinakaException.class)
    public void testExceptionWhileParsingJson() throws JsonProcessingException, PinakaException {
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(BannerWidgetData.class))).thenThrow(new RuntimeException());
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);
        String bannerUrl = "https://example.com/banner.jpg";
        LV3NamePageFormTransformer.LV3NamePageBannerFormTransformer bannerFormTransformer = new LV3NamePageFormTransformer.LV3NamePageBannerFormTransformer(dynamicBucket);
        when(dynamicBucket.getString("dynamicImageBannerUrl")).thenReturn(bannerUrl);
        bannerFormTransformer.buildBannerWidgetData(applicationDataResponse);
    }

    @Test
    public void testUpdateGroupedWidgetSubmitButton() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);
        String bannerUrl = "https://example.com/banner.jpg";
        when(dynamicBucket.getString("dynamicImageBannerUrl")).thenReturn(bannerUrl);
        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        FormWidgetDataFetcher mockFormWidgetDataFetcher = mock(FormWidgetDataFetcher.class);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = new HashMap<>();

        when(mockFormWidgetDataFetcher.getDataForFields(any(Set.class), any(LeadPageDataSourceResponse.class), eq(decrypter)))
                .thenReturn(userData);

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMap(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3NamePageFormTransformer lv3NamePageFormTransformer = getLv3NamePageFormTransformer();
        lv3NamePageFormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_NAME_PAGE);

        verify(this.formWidgetDataPrefillUtils, times(1)).prefillFormFieldValues(any(Map.class), any(Map.class));
        verify(this.formWidgetDataJsonParser, times(1)).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        verify(this.formWidgetDataPrefillUtils, times(1)).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
    }

    @Test(expected = PinakaException.class)
    public void testExceptionThrownWhenSomethingGoesWrong() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        FormWidgetDataFetcher mockFormWidgetDataFetcher = mock(FormWidgetDataFetcher.class);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = new HashMap<>();

        when(mockFormWidgetDataFetcher.getDataForFields(any(Set.class), any(LeadPageDataSourceResponse.class),eq(decrypter)))
                .thenReturn(userData);

        // Throwing exception here...
        doThrow(new RuntimeException("")).when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3NamePageFormTransformer lv3NamePageFormTransformer = getLv3NamePageFormTransformer();
        lv3NamePageFormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_NAME_PAGE);
    }

    @NotNull
    private LV3NamePageFormTransformer getLv3NamePageFormTransformer() {
        return new LV3NamePageFormTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataPrefillUtils,
                formWidgetDataFetcher,
                formWidgetDataJsonParser,
                locationRequestHandler,
                bqIngestionHelper);
    }

}
