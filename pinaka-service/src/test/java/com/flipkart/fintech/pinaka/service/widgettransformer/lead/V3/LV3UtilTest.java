package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichButtonValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import static org.testng.AssertJUnit.assertFalse;
import static org.testng.AssertJUnit.assertTrue;

@RunWith(PowerMockRunner.class)
@PrepareForTest({LV3Util.class, Random.class})
public class LV3UtilTest {

    @Mock
    private SubmitButtonValue submitButton;

    @Mock
    private RenderableComponent<RichButtonValue> renderableComponent;


    @Mock
    private Action action;

    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;

    private Map<String, Object> mockParams;
    private EncryptionData encryptionData;

    @Mock
    private DynamicBucket dynamicBucket;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Prepare test data
        mockParams = new HashMap<>();
        mockParams.put("param1", "value1");

        encryptionData = new EncryptionData("testPublicKey", "testKeyId");

        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(mockParams);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
    }

    @Test
    public void testUpdateGroupedWidgetSubmitButton_validCase() throws PinakaException {
        when(submitButton.getButton()).thenReturn(renderableComponent);
        when(renderableComponent.getAction()).thenReturn(action);

        LV3Util.updateGroupedWidgetSubmitButton(submitButton, reviewUserDataSourceResponse);

        verify(action).setParams(mockParams);
        verify(action).setEncryption(encryptionData);
    }

    @Test(expected = PinakaException.class)
    public void testUpdateGroupedWidgetSubmitButton_nullSubmitButton() throws PinakaException {
        LV3Util.updateGroupedWidgetSubmitButton(null, reviewUserDataSourceResponse);
    }

    @Test(expected = PinakaException.class)
    public void testUpdateGroupedWidgetSubmitButton_nullAction() throws PinakaException {
        when(submitButton.getButton()).thenReturn(renderableComponent);
        when(renderableComponent.getAction()).thenReturn(null);

        LV3Util.updateGroupedWidgetSubmitButton(submitButton, reviewUserDataSourceResponse);
    }

    @Test
    public void testUpdateSubmitButtons_multipleButtons() throws PinakaException {
        SubmitButtonValue button1 = mock(SubmitButtonValue.class);
        SubmitButtonValue button2 = mock(SubmitButtonValue.class);
        RenderableComponent<RichButtonValue> component1 = mock(RenderableComponent.class);
        RenderableComponent<RichButtonValue> component2 = mock(RenderableComponent.class);
        Action action1 = mock(Action.class);
        Action action2 = mock(Action.class);

        when(button1.getButton()).thenReturn(component1);
        when(component1.getAction()).thenReturn(action1);

        when(button2.getButton()).thenReturn(component2);
        when(component2.getAction()).thenReturn(action2);

        Map<String, SubmitButtonValue> formFieldSubmitButtons = new HashMap<>();
        formFieldSubmitButtons.put("btn1", button1);
        formFieldSubmitButtons.put("btn2", button2);

        LV3Util.updateSubmitButtons(formFieldSubmitButtons, reviewUserDataSourceResponse);

        verify(action1).setParams(mockParams);
        verify(action1).setEncryption(encryptionData);

        verify(action2).setParams(mockParams);
        verify(action2).setEncryption(encryptionData);
    }

    @Test
    public void testIsLv3Enabled_userInWhitelist() {
        String userId = "user123";
        when(dynamicBucket.getStringArray(PinakaConstants.PLConstants.LEAD_V3_WL_USERS))
                .thenReturn(Collections.singletonList(userId));
        when(dynamicBucket.getInt(PinakaConstants.PLConstants.LEAD_V3_WL_USERS)).thenReturn(0);

        boolean result = LV3Util.isLv3Enabled(dynamicBucket, userId);
        assertTrue(result);
    }

    @Test
    public void testIsLv3Enabled_userNotInWhitelistButUnderTrafficLimit() {
        String userId = "user456";
        when(dynamicBucket.getStringArray(PinakaConstants.PLConstants.LEAD_V3_WL_USERS))
                .thenReturn(Collections.emptyList());
        when(dynamicBucket.getInt(PinakaConstants.PLConstants.LEAD_V3_TRAFFIC)).thenReturn(100);

        boolean result = LV3Util.isLv3Enabled(dynamicBucket, userId);
        assertTrue(result);
    }

    @Test
    public void testIsLv3Application_withApplicationStateContainingLeadV3() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V3");

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertTrue(result);
    }

    @Test
    public void testIsLv3Application_withApplicationStateNotContainingLeadV3() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationState()).thenReturn("OTHER_STATE");

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertFalse(result);
    }

    @Test
    public void testIsLv3Application_withApplicationDataContainingLeadV3Key() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV3Key", "value");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertTrue(result);
    }

    @Test
    public void testIsLv3Application_withApplicationDataNotContainingLeadV3Key() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("otherKey", "value");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertFalse(result);
    }

    @Test
    public void testIsLv3Application_withEmptyApplicationData() {
        Map<String, Object> applicationData = new HashMap<>();

        boolean result = LV3Util.isLv3Application(applicationData);

        assertFalse(result);
    }

    @Test
    public void testIsLv3Application_withNonEmptyApplicationDataContainingLeadV3Key() {
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV3Key", "value");

        boolean result = LV3Util.isLv3Application(applicationData);

        assertTrue(result);
    }

    @Test
    public void testIsLv3Application_withNonEmptyApplicationDataNotContainingLeadV3Key() {
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("otherKey", "value");

        boolean result = LV3Util.isLv3Application(applicationData);

        assertFalse(result);
    }

    // Add the following test cases to LV3UtilTest.java

    @Test
    public void testGetLeadV3Events_validInputs() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getSmUserId()).thenReturn("smUserId");

        String pageState = "PAGE_STATE";
        String applicationState = "APPLICATION_STATE";

        LeadV3Events leadV3Events = LV3Util.getLeadV3Events(applicationDataResponse, pageState, applicationState);

        assertEquals("app123", leadV3Events.getApplicationId());
        assertEquals("V3_smUserId_PAGE_STATE_app123", leadV3Events.getEventId());
        assertEquals(pageState, leadV3Events.getPageState());
        assertEquals(applicationState, leadV3Events.getApplicationState());
        assertNotNull(leadV3Events.getEventTimestamp());
    }

    @Test(expected = NullPointerException.class)
    public void testGetLeadV3Events_nullApplicationDataResponse() {
        LV3Util.getLeadV3Events(null, "PAGE_STATE", "APPLICATION_STATE");
    }

    @Test
    public void testGetLeadV3Events_emptyPageState() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getSmUserId()).thenReturn("smUserId");

        LeadV3Events leadV3Events = LV3Util.getLeadV3Events(applicationDataResponse, "", "APPLICATION_STATE");

        assertEquals("V3_smUserId__app123", leadV3Events.getEventId());
        assertEquals("", leadV3Events.getPageState());
    }

    @Test
    public void testGetLeadV3Events_emptyApplicationState() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getSmUserId()).thenReturn("smUserId");

        LeadV3Events leadV3Events = LV3Util.getLeadV3Events(applicationDataResponse, "PAGE_STATE", "");

        assertEquals("V3_smUserId_PAGE_STATE_app123", leadV3Events.getEventId());
        assertEquals("", leadV3Events.getApplicationState());
    }

    @Test
    public void testGetVersion_withLeadV3State() throws PinakaException {
        LeadDetails.LeadState leadState = LeadDetails.LeadState.LEAD_V3_PAGE_2;
        String version = LV3Util.getVersion(leadState);
        assertEquals("V3", version);
    }

    @Test
    public void testGetVersion_withLeadV4State() throws PinakaException {
        LeadDetails.LeadState leadState = LeadDetails.LeadState.LEAD_V4_PAGE_1;
        String version = LV3Util.getVersion(leadState);
        assertEquals("V4", version);
    }

    @Test(expected = PinakaException.class)
    public void testGetVersion_withUnknownLeadState() throws PinakaException {
        LV3Util.getVersion(LeadDetails.LeadState.CREATE_PROFILE_START);
    }

    @Test
    public void returnsTrueWhenApplicationStateContainsLeadV3() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V3");

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertTrue(result);
    }

    @Test
    public void returnsFalseWhenApplicationStateDoesNotContainLeadV3() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationState()).thenReturn("OTHER_STATE");

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertFalse(result);
    }

    @Test
    public void returnsTrueWhenApplicationDataContainsLeadV3Key() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV3Key", "value");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertTrue(result);
    }

    @Test
    public void returnsFalseWhenApplicationDataDoesNotContainLeadV3Key() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("otherKey", "value");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertFalse(result);
    }

    @Test
    public void returnsFalseWhenApplicationDataIsNull() {
        ApplicationDataResponse applicationDataResponse = mock(ApplicationDataResponse.class);
        when(applicationDataResponse.getApplicationData()).thenReturn(null);

        boolean result = LV3Util.isLv3Application(applicationDataResponse);

        assertFalse(result);
    }

}
