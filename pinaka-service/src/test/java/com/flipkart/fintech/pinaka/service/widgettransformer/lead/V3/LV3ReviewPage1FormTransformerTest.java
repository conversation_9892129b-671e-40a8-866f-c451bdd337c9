package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import static org.testng.AssertJUnit.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV3ReviewPage1FormTransformer.class,
        InitialUserReviewDataSource.class,
        ReviewUserDataSourceResponse.class,
        LeadPageDataSourceResponse.class,
        LeadPageDataSource.class,
        ObjectMapperUtil.class,
        LV3Util.class,
        LV4Util.class,
})
public class LV3ReviewPage1FormTransformerTest {

    private static final String PHONE_NUMBER_STRING = "phoneNumber";
    private static final String FULL_NAME_STRING = "fullName";
    public static final String PAN_NUMBER = "panNumber";
    public static final String DOB_STRING = "dob";
    public static final String GENDER_STRING = "gender";
    public static final String GENDER_STRING_2 = "gender_2";
    public static final String COMPANY_NAME_STRING = "companyName";
    public static final String ORGANIZATION_ID_STRING = "organizationId";
    public static final String INDUSTRY_NAME_STRING = "industryName";
    public static final String INDUSTRY_ID_STRING = "industryId";
    public static final String HOUSE_NUMBER_STRING = "houseNumber";
    public static final String AREA_STRING = "area";
    public static final String PINCODE_DETAILS_STRING = "pincodeDetails";
    public static final String MONTHLY_INCOME_STRING = "monthlyIncome";
    public static final String INCOME_SOURCE_STRING = "incomeSource";
    public static final String EMPLOYMENT_TYPE_STRING = "employmentType";
    public static final String EMAIL_STRING = "email";
    public static final String ADDRESSES_STRING = "addresses";
    public static final String BONUS_INCOME_STRING = "bonusIncome";

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private LocationRequestHandler locationRequestHandler;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private Decrypter decrypter;

    @Mock
    private InitialUserReviewDataSource initialUserReviewDataSource;
    @Mock
    private LeadPageDataSource leadPageDataSource;
    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    private LeadPageDataSourceResponse leadPageDataSourceResponse;

    @Mock
    private FormWidgetDataFetcher formWidgetDataFetcher;
    @Mock
    private FormWidgetDataJsonParser formWidgetDataJsonParser;
    @Mock
    private FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    @Mock
    private BqIngestionHelper bqIngestionHelper;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(LV3Util.class);
        PowerMockito.mockStatic(LV4Util.class);
        PowerMockito.doNothing().when(LV3Util.class, "updateGroupedWidgetSubmitButton", any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        PowerMockito.when(LV3Util.class, "getLeadV3Events", any(ApplicationDataResponse.class), any(String.class), any(String.class)).thenReturn(LeadV3Events.newBuilder().build());
        // Default mock for LV4Util.getCachedUserDataOrFallback - will be overridden in individual tests
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(new HashMap<>());
    }

    @Test
    public void testLV3BannerTransformer() throws PinakaException {
        LV3ReviewPage1FormTransformer.LV3ReviewPageBannerFormTransformer bannerFormTransformer = new LV3ReviewPage1FormTransformer.LV3ReviewPageBannerFormTransformer();
        BannerWidgetData bannerWidgetData = bannerFormTransformer.buildBannerWidgetData(applicationDataResponse);
        assertEquals(BannerWidgetData.class, bannerWidgetData.getClass());
    }

    @Test(expected = PinakaException.class)
    public void testExceptionWhileParsingJson() throws JsonProcessingException, PinakaException {
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(BannerWidgetData.class))).thenThrow(new RuntimeException());
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);
        LV3ReviewPage1FormTransformer.LV3ReviewPageBannerFormTransformer bannerFormTransformer = new LV3ReviewPage1FormTransformer.LV3ReviewPageBannerFormTransformer();
        BannerWidgetData bannerWidgetData = bannerFormTransformer.buildBannerWidgetData(applicationDataResponse);
        assertEquals(BannerWidgetData.class, bannerWidgetData.getClass());
    }

    @Test
    public void testPersonalDetailsNotFound() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getNoPrefillData();

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());


        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testAddressDetailsNotFound() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getOnlyBasicDetailsNoAddressNoWorkDetails();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testWorkDetailsNotFound() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getOnlyBasicDetailsWithMultipleAddresses();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testAllDetailsFound() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getAllPrefilledData();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());


        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(1)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(1)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(1)).getFormFieldSubmitButtons(any(List.class));
    }


    @Test(expected = PinakaException.class)
    public void testExpectedException() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(BannerWidgetData.class))).thenThrow(new RuntimeException());
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);
        Map<String, Object> userData = getAllPrefilledData();

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V3_PAGE_2);
        verify(lv3ReviewPage1FormTransformer, times(0)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(0));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(0)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(0)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(0)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testPersonalDetailsNotFoundV4() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getNoPrefillData();

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());


        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V4_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testAddressDetailsNotFoundV4() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getOnlyBasicDetailsNoAddressNoWorkDetails();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V4_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testWorkDetailsNotFoundV4() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getOnlyBasicDetailsWithMultipleAddresses();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V4_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(2)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test
    public void testAllDetailsFoundV4() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        Map<String, Object> userData = getAllPrefilledData();

        FormConfig formConfig = mock(FormConfig.class);
        whenNew(FormConfig.class).withArguments(anyString(), anyString(), anyMap(), anyMap()).thenReturn(formConfig);
        when(formConfig.getFormConfigMapForPage3(anyString(), any(DynamicBucket.class))).thenReturn(new HashMap<>());


        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        // Mock LV4Util.getCachedUserDataOrFallback to return the expected userData
        PowerMockito.when(LV4Util.getCachedUserDataOrFallback(any(ApplicationDataResponse.class), any(Decrypter.class), any(LocationRequestHandler.class), any(FormWidgetDataFetcher.class), any())).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V4_PAGE_1);
        verify(lv3ReviewPage1FormTransformer, times(1)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(1)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(1));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(1)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(1)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(1)).getFormFieldSubmitButtons(any(List.class));
    }


    @Test(expected = PinakaException.class)
    public void testExpectedExceptionV4() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(BannerWidgetData.class))).thenThrow(new RuntimeException());
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);
        Map<String, Object> userData = getAllPrefilledData();

        when(this.formWidgetDataFetcher.getDataForFields(any(Set.class), /*any(LeadPageDataSourceResponse.class),*/ any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler))).thenReturn(userData);

        doNothing().when(this.formWidgetDataPrefillUtils).prefillFormFieldValues(any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataJsonParser).updateFormFieldValueMapToPrefill(any(List.class), any(Map.class), any(Map.class));
        doNothing().when(this.formWidgetDataPrefillUtils).prefillGroupedFormFieldValues(any(Map.class), any(Map.class));
        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        lv3ReviewPage1FormTransformer.buildWidgetGroupData(applicationDataResponse, LeadDetails.LeadState.LEAD_V4_PAGE_2);
        verify(lv3ReviewPage1FormTransformer, times(0)).personalDetailsNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).addressNotFound(userData);
        verify(lv3ReviewPage1FormTransformer, times(0)).workDetailsNotFound(userData);
        PowerMockito.verifyStatic(LV3Util.class, times(0));
        LV3Util.updateGroupedWidgetSubmitButton(any(SubmitButtonValue.class), any(ReviewUserDataSourceResponse.class));
        verify(formWidgetDataJsonParser, times(0)).getFormFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(0)).getGroupFieldValueMapToPrefill(any(List.class));
        verify(formWidgetDataJsonParser, times(0)).getFormFieldSubmitButtons(any(List.class));
    }

    @Test()
    public void testAtleastOneValidAddressPresentTrue() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);

        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        boolean answer = lv3ReviewPage1FormTransformer.atLeastOneValidAddressPresent(experianAllAccountDetails());
        assertTrue(answer);
    }

    @Test()
    public void testAtleastOneValidAddressPresentFalse() throws Exception {
        Map<String, Object> queryParams = new HashMap<>();
        EncryptionData encryptionData = new EncryptionData();
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);

        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);
        when(reviewUserDataSourceResponse.getEncryptionData()).thenReturn(encryptionData);

        LV3ReviewPage1FormTransformer lv3ReviewPage1FormTransformer = spy(getLv3ReviewPageTransformer());
        boolean answer = lv3ReviewPage1FormTransformer.atLeastOneValidAddressPresent(experianAllAccountDetailsINVALID());
        assertFalse(answer);
    }

    private Map<String, Object> getNoPrefillData() {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        userData.put(FULL_NAME_STRING, "Prasoon Birla");
        userData.put(PHONE_NUMBER_STRING, "**********");
        // ReviewUserDataSourceResponse
        userData.put(PAN_NUMBER, "");
        userData.put(DOB_STRING, "");
        userData.put(GENDER_STRING, "");
        userData.put(GENDER_STRING_2, "");
        userData.put(COMPANY_NAME_STRING, "");
        userData.put(ORGANIZATION_ID_STRING, "");
        userData.put(INDUSTRY_NAME_STRING, "");
        userData.put(INDUSTRY_ID_STRING, "");
        userData.put(HOUSE_NUMBER_STRING, "");
        userData.put(AREA_STRING, "");
        userData.put(ADDRESSES_STRING, Collections.emptyList());
        userData.put(PINCODE_DETAILS_STRING, "");
        userData.put(MONTHLY_INCOME_STRING, "");
        userData.put(BONUS_INCOME_STRING, "");
        userData.put(INCOME_SOURCE_STRING, "");
        userData.put(EMPLOYMENT_TYPE_STRING, "");
        userData.put(EMAIL_STRING, "");
        return userData;
    }

    private Map<String, Object> getAllPrefilledData() {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        userData.put(FULL_NAME_STRING, "Prasoon Birla");
        userData.put(PHONE_NUMBER_STRING, "+919191919191");
        // ReviewUserDataSourceResponse
        userData.put(PAN_NUMBER, "**********");
        userData.put(DOB_STRING, "19/02/1998");
        userData.put(GENDER_STRING, "M");
        userData.put(GENDER_STRING_2, "M");
        userData.put(COMPANY_NAME_STRING, "Supermoney");
        userData.put(ORGANIZATION_ID_STRING, "1234");
        userData.put(INDUSTRY_NAME_STRING, "Cafeteria");
        userData.put(INDUSTRY_ID_STRING, "1234");
        userData.put(HOUSE_NUMBER_STRING, "Address line 1");
        userData.put(AREA_STRING, "Address line 2");
        userData.put(ADDRESSES_STRING, Collections.emptyList());
        PincodeDetailsResponse pincodeDetailsResponse= new PincodeDetailsResponse();
        pincodeDetailsResponse.setPincode("123123");
        userData.put(PINCODE_DETAILS_STRING, pincodeDetailsResponse);
        userData.put(MONTHLY_INCOME_STRING, "1234123");
        userData.put(BONUS_INCOME_STRING, "123123");
        userData.put(INCOME_SOURCE_STRING, "ONLINE");
        userData.put(EMPLOYMENT_TYPE_STRING, "SelfEmployed");
        userData.put(EMAIL_STRING, "<EMAIL>");
        return userData;
    }

    private Map<String, Object> getOnlyBasicDetailsNoAddressNoWorkDetails() {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        userData.put(FULL_NAME_STRING, "Prasoon Birla");
        userData.put(PHONE_NUMBER_STRING, "+919191919191");
        // ReviewUserDataSourceResponse
        userData.put(PAN_NUMBER, "**********");
        userData.put(DOB_STRING, "19/02/1998");
        userData.put(GENDER_STRING, "M");
        userData.put(GENDER_STRING_2, "M");
        userData.put(COMPANY_NAME_STRING, "");
        userData.put(ORGANIZATION_ID_STRING, "");
        userData.put(INDUSTRY_NAME_STRING, "");
        userData.put(INDUSTRY_ID_STRING, "");
        userData.put(HOUSE_NUMBER_STRING, "");
        userData.put(AREA_STRING, "");
        userData.put(ADDRESSES_STRING, Collections.emptyList());
        PincodeDetailsResponse pincodeDetailsResponse= new PincodeDetailsResponse();
        pincodeDetailsResponse.setPincode("123123");
        userData.put(PINCODE_DETAILS_STRING, pincodeDetailsResponse);
        userData.put(MONTHLY_INCOME_STRING, "");
        userData.put(BONUS_INCOME_STRING, "");
        userData.put(INCOME_SOURCE_STRING, "");
        userData.put(EMPLOYMENT_TYPE_STRING, "");
        userData.put(EMAIL_STRING, "");
        return userData;
    }

    private Map<String, Object> getOnlyBasicDetailsWithMultipleAddresses() {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        userData.put(FULL_NAME_STRING, "Prasoon Birla");
        userData.put(PHONE_NUMBER_STRING, "+919191919191");
        // ReviewUserDataSourceResponse
        userData.put(PAN_NUMBER, "**********");
        userData.put(DOB_STRING, "19/02/1998");
        userData.put(GENDER_STRING, "M");
        userData.put(GENDER_STRING_2, "M");
        userData.put(COMPANY_NAME_STRING, "");
        userData.put(ORGANIZATION_ID_STRING, "");
        userData.put(INDUSTRY_NAME_STRING, "");
        userData.put(INDUSTRY_ID_STRING, "");
        userData.put(HOUSE_NUMBER_STRING, "ABCLKJLSKDF");
        userData.put(AREA_STRING, "LKASJDFlkjl");
        userData.put(ADDRESSES_STRING, experianAllAccountDetails());
        PincodeDetailsResponse pincodeDetailsResponse= new PincodeDetailsResponse();
        pincodeDetailsResponse.setPincode("123123");
        userData.put(PINCODE_DETAILS_STRING, pincodeDetailsResponse);
        userData.put(MONTHLY_INCOME_STRING, "");
        userData.put(BONUS_INCOME_STRING, "");
        userData.put(INCOME_SOURCE_STRING, "");
        userData.put(EMPLOYMENT_TYPE_STRING, "");
        userData.put(EMAIL_STRING, "");
        return userData;
    }

    private List<CAISHolderAddressDetails> experianAllAccountDetails() {
        List<CAISHolderAddressDetails> caisHolderAddressDetailsList = new ArrayList<>();
        CAISHolderAddressDetails caisHolderAddressDetails = new CAISHolderAddressDetails();
        caisHolderAddressDetails.setFirstLineOfAddress("First Line");
        caisHolderAddressDetails.setSecondLineOfAddress("Second Line");
        caisHolderAddressDetails.setZipPostalCodeOfAddress("123123");
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        return caisHolderAddressDetailsList;
    }

    private List<CAISHolderAddressDetails> experianAllAccountDetailsINVALID() {
        List<CAISHolderAddressDetails> caisHolderAddressDetailsList = new ArrayList<>();
        CAISHolderAddressDetails caisHolderAddressDetails = new CAISHolderAddressDetails();
        caisHolderAddressDetails.setFirstLineOfAddress("");
        caisHolderAddressDetails.setSecondLineOfAddress("Second Line");
        caisHolderAddressDetails.setZipPostalCodeOfAddress("123123");
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        caisHolderAddressDetailsList.add(caisHolderAddressDetails);
        return caisHolderAddressDetailsList;
    }

    private LV3ReviewPage1FormTransformer getLv3ReviewPageTransformer() {
        return new LV3ReviewPage1FormTransformer(
                decrypter,
                dynamicBucket,
                formWidgetDataPrefillUtils,
                formWidgetDataFetcher,
                formWidgetDataJsonParser,
                locationRequestHandler,
                bqIngestionHelper,
                null); // Mock BureauDataManager not needed for tests
    }
}
