package com.flipkart.fintech.pinaka.service.helper;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataEncryption;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.schema.PinakaService.LeadV3Events;
import lombok.val;
import org.junit.runner.RunWith;
import org.junit.Test;
import org.junit.Before;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper.*;
import static com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper.validateRequest;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserActionSubmitRequestHelperTest {

    private FormDataEncryption formDataEncryption;
    private FormDataDecryption formDataDecryption;
    private FormSubmitRequest formSubmitRequest;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        formDataEncryption = mock(FormDataEncryption.class);
        formDataDecryption = mock(FormDataDecryption.class);
        formSubmitRequest = mock(FormSubmitRequest.class);
    }

    @Test
    public void testConstructor() {
        try {
            Constructor<UserActionSubmitRequestHelper> constructor = UserActionSubmitRequestHelper.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
            fail("Expected UnsupportedOperationException");
        } catch (Exception e) {
            assertTrue(e instanceof InvocationTargetException);
        }
    }

    @Test
    public void testAdjustAnnualIncome_ValidIncome_SelfEmployed() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "5000");
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // When
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);

        // Then
        assertEquals(60000, formData.get("annualTurnOver"));
    }

    @Test(expected = PinakaException.class)
    public void testAdjustAnnualIncome_InvalidIncome_ThrowsException() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "invalid");
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // Then
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);
    }

    @Test(expected = PinakaException.class)
    public void testAdjustAnnualIncome_IncomeNotString_ThrowsException() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", 5000); // Not a string
        formData.put("employmentType", "SelfEmployed");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // Then
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);
    }

    @Test
    public void testAdjustAnnualIncome_NotSelfEmployed_NoChange() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "5000");
        formData.put("employmentType", "Salaried");
//        when(formSubmitRequest.getTaskKey()).thenReturn("leadV3Page1");
        when(formSubmitRequest.getFormData()).thenReturn(formData);

        // When
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);

        // Then
        assertFalse(formData.containsKey("annualTurnOver"));
    }

    @Test
    public void testAdjustAnnualIncome_TaskKeyNotMatching_NoChange() throws PinakaException {
        // Given
        Map<String, Object> formData = new HashMap<>();
        formData.put("income", "5000");
        formData.put("employmentType", "SelfEmployed");
//        when(formSubmitRequest.getTaskKey()).thenReturn("otherTaskKey");

        // When
        UserActionSubmitRequestHelper.adjustAnnualIncome(formSubmitRequest);

        // Then
        assertFalse(formData.containsKey("annualTurnOver"));
    }

    @Test
    public void testAdjustFullName_ValidName_SplitsCorrectly() throws PinakaException {
        // Given
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("fullName", "John Doe");
        request.setFormData(formData);

        when(formDataDecryption.getDecryptedPlainTextString("John Doe")).thenReturn("John Doe");
        when(formDataEncryption.encryptString("John")).thenReturn("encryptedFirstName");
        when(formDataEncryption.encryptString("Doe")).thenReturn("encryptedLastName");

        // When
        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        // Then
        assertEquals("encryptedFirstName", request.getFormData().get("firstName"));
        assertEquals("encryptedLastName", request.getFormData().get("lastName"));
        assertFalse(request.getFormData().containsKey("fullName"));
    }

    @Test
    public void testAdjustFullName_SingleWordName_SameFirstAndLast() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("fullName", "Madonna");
        request.setFormData(formData);

        when(formDataDecryption.getDecryptedPlainTextString("Madonna")).thenReturn("Madonna");
        when(formDataEncryption.encryptString("Madonna")).thenReturn("encryptedMadonna");

        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        assertEquals("encryptedMadonna", request.getFormData().get("firstName"));
        assertEquals("encryptedMadonna", request.getFormData().get("lastName"));
        assertFalse(request.getFormData().containsKey("fullName"));
    }

    @Test(expected = PinakaException.class)
    public void testAdjustFullName_InvalidEmptyName_ThrowsException() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("fullName", "");
        request.setFormData(formData);
        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);
    }

    @Test
    public void testAdjustOrganizationName_CopiesAndRemovesBusinessName() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        formData.put("businessName", "Acme Corp");
        request.setFormData(formData);

        // Only test organization logic
        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        assertEquals("Acme Corp", request.getFormData().get("organization"));
        assertFalse(request.getFormData().containsKey("businessName"));
    }

    @Test
    public void testAdjustOrganizationName_NoBusinessName_NoChange() throws PinakaException {
        FormSubmitRequest request = FormSubmitRequest.builder().build();
        Map<String, Object> formData = new HashMap<>();
        request.setFormData(formData);

        UserActionSubmitRequestHelper.processFormDataFields(request, formDataEncryption, formDataDecryption);

        assertFalse(request.getFormData().containsKey("organization"));
    }

    @Test
    public void getSubmitEventState_ReturnsNamePageSubmit_WhenLandingPageWithFullName() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(FULL_NAME, "John Doe");
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);
        when(req.getFormData()).thenReturn(formData);

        String state = getSubmitEventState(req);
        assertEquals(NAME_PAGE_SUBMIT, state);
    }

    @Test
    public void getSubmitEventState_throwsException() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);
        when(req.getFormData()).thenReturn(formData);
        String state = getSubmitEventState(req);
        assertEquals(LV4_LANDING_PAGE_INVALID_STATE, state);
    }

    @Test
    public void getSubmitEventState_ReturnsAllFilledSubmit_WhenPage1WithAllFields() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(AREA, "Area1");
        formData.put(PINCODE_DETAILS, new HashMap<>());
        formData.put(EMAIL, "<EMAIL>");
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        String state = getSubmitEventState(req);
        assertEquals(LV4_ALL_FILLED_SUBMIT, state);
    }

    @Test
    public void getSubmitEventState_ReturnsHalfFilledSubmit_WhenPage1WithoutEmail() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(AREA, "Area1");
        formData.put(PINCODE_DETAILS, new HashMap<>());
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        String state = getSubmitEventState(req);
        assertEquals(LV4_HALF_FILLED_SUBMIT, state);
    }

    @Test
    public void getSubmitEventState_ReturnsWorkDetailsSubmit_WhenPage2() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_2);
        when(req.getFormData()).thenReturn(new HashMap<>());

        String state = getSubmitEventState(req);
        assertEquals(LV4_WORK_DETAILS_SUBMIT, state);
    }

    @Test
    public void getSubmitEventState_ReturnsEmptySubmit_WhenFormDataNull() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);
        when(req.getFormData()).thenReturn(null);

        String state = getSubmitEventState(req);
        assertEquals(EMPTY_SUBMIT, state);
    }

    @Test
    public void getSubmitEventState_ReturnsInvalidState_WhenUnknownTaskKey() {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        when(req.getTaskKey()).thenReturn("unknownTask");
        when(req.getFormData()).thenReturn(new HashMap<>());

        String state = getSubmitEventState(req);
        assertEquals(LV4_INVALID_STATE, state);
    }

    @Test
    public void getLeadEvents_BuildsEventWithCorrectFields() {
        ApplicationDataResponse appResp = mock(ApplicationDataResponse.class);
        when(appResp.getApplicationId()).thenReturn("appId123");
        when(appResp.getSmUserId()).thenReturn("smUserId123");
        when(appResp.getApplicationState()).thenReturn("STATE1");

        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(FULL_NAME, "John Doe");
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);
        when(req.getFormData()).thenReturn(formData);

        LeadV3Events event = getLeadEvents(appResp, req);

        assertEquals("appId123", event.getApplicationId());
        assertEquals("STATE1", event.getApplicationState());
        assertTrue(event.getEventId().contains("V4_smUserId123_LV4_NAME_PAGE_SUBMIT_appId123"));
        assertEquals(NAME_PAGE_SUBMIT, event.getPageState());
        assertNotNull(event.getEventTimestamp());
    }

    @Test(expected = PinakaException.class)
    public void validateRequest_ThrowsException_WhenFormDataNull() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);

        validateRequest(req);
    }

    @Test
    public void validateRequest_ReturnsTrue_WhenLandingPageWithValidFields() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(FULL_NAME, "John Doe");
        formData.put(PHONE_NUMBER, "1234567890");
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);
        when(req.getFormData()).thenReturn(formData);

        assertTrue(validateRequest(req));
    }

    @Test(expected = PinakaException.class)
    public void validateRequest_ThrowsException_WhenLandingPageMissingField() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(FULL_NAME, "John Doe");
        when(req.getTaskKey()).thenReturn(LEAD_V4_LANDING_PAGE);
        when(req.getFormData()).thenReturn(formData);
        validateRequest(req);
    }

    @Test
    public void returnsTrueForValidHalfFilledSubmitState() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(PAN_NUMBER, "**********");
        formData.put(DOB, "1990-01-01");
        formData.put(GENDER, "Male");
        formData.put(AREA, "Area1");
        formData.put(HOUSE_NUMBER, "123");
        final HashMap<Object, Object> pincodeDetails = new HashMap<>();
        pincodeDetails.put("pincode", "123123");
        pincodeDetails.put("city", "Kasganj");
        pincodeDetails.put("state", "U.P.");
        formData.put(PINCODE_DETAILS, pincodeDetails);
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        boolean result = validateRequest(req);
        assertTrue(result);
    }

    @Test
    public void returnsTrueForValidAllFilledSubmitState() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(PAN_NUMBER, "**********");
        formData.put(DOB, "1990-01-01");
        formData.put(GENDER, "Male");
        formData.put(AREA, "Area1");
        formData.put(HOUSE_NUMBER, "123");
        final HashMap<Object, Object> pincodeDetails = new HashMap<>();
        pincodeDetails.put("pincode", "123123");
        pincodeDetails.put("city", "Kasganj");
        pincodeDetails.put("state", "U.P.");
        formData.put(PINCODE_DETAILS, pincodeDetails);
        formData.put(EMAIL, "<EMAIL>");
        formData.put(LOAN_PURPOSE, "Personal Loan");
        formData.put(EMPLOYMENT_TYPE, "Salaried");
        formData.put(INCOME, "50000");
        formData.put(BONUS_INCOME, "5000");
        formData.put(INCOME_SOURCE, "Salary");
        final HashMap<Object, Object> organization = new HashMap<>();
        organization.put("id", "123123");
        organization.put("title", "Kasganj");
        formData.put(ORGANIZATION, organization);
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        boolean result = validateRequest(req);
        assertTrue(result);
    }

    @Test(expected = PinakaException.class)
    public void throwsExceptionForInvalidHalfFilledSubmitState() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(PAN_NUMBER, "**********");
        formData.put(DOB, "1990-01-01");
        formData.put(GENDER, "Male");
        formData.put(AREA, "Area1");
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        validateRequest(req);
    }

    @Test(expected = PinakaException.class)
    public void throwsExceptionForInvalidAllFilledSubmitState() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(PAN_NUMBER, "**********");
        formData.put(DOB, "1990-01-01");
        formData.put(GENDER, "Male");
        formData.put(AREA, "Area1");
        formData.put(HOUSE_NUMBER, "123");
        formData.put(PINCODE_DETAILS, new HashMap<>());
        formData.put(EMAIL, "<EMAIL>");
        formData.put(LOAN_PURPOSE, "Personal Loan");
        formData.put(EMPLOYMENT_TYPE, "Salaried");
        formData.put(INCOME, "50000");
        formData.put(BONUS_INCOME, "5000");
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        validateRequest(req);
    }

    @Test
    public void returnsTrueForValidWorkDetailsSubmitState() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(EMAIL, "<EMAIL>");
        formData.put(LOAN_PURPOSE, "Personal Loan");
        formData.put(EMPLOYMENT_TYPE, "Salaried");
        formData.put(INCOME, "50000");
        formData.put(BONUS_INCOME, "5000");
        formData.put(INCOME_SOURCE, "Salary");
        final HashMap<Object, Object> organization = new HashMap<>();
        organization.put("id", "123123");
        organization.put("title", "Kasganj");
        formData.put(ORGANIZATION, organization);
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_2);
        when(req.getFormData()).thenReturn(formData);

        boolean result = validateRequest(req);
        assertTrue(result);
    }

    @Test(expected = PinakaException.class)
    public void throwsExceptionForInvalidWorkDetailsSubmitState() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(EMAIL, "<EMAIL>");
        formData.put(LOAN_PURPOSE, "Personal Loan");
        formData.put(EMPLOYMENT_TYPE, "Salaried");
        formData.put(INCOME, "50000");
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_2);
        when(req.getFormData()).thenReturn(formData);

        validateRequest(req);
    }

    @Test
    public void validateOrganizationField_ReturnsTrue_WhenBusinessNameExists() throws PinakaException {
        Map<String, Object> formData = new HashMap<>();
        formData.put(BUSINESS_NAME, "Acme Corp");

        assertTrue(UserActionSubmitRequestHelper.validateOrganizationField(formData));
    }

    @Test(expected = PinakaException.class)
    public void validateOrganizationField_ThrowsException_WhenBusinessNameAndOrganizationMissing() throws PinakaException {
        Map<String, Object> formData = new HashMap<>();

        UserActionSubmitRequestHelper.validateOrganizationField(formData);
    }

    @Test(expected = PinakaException.class)
    public void validateRequest_ThrowsException_WhenPincodeDetailsMissingOrInvalid() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(PAN_NUMBER, "**********");
        formData.put(DOB, "1990-01-01");
        formData.put(GENDER, "Male");
        formData.put(AREA, "Area1");
        formData.put(HOUSE_NUMBER, "123");
        // Missing PINCODE_DETAILS
        when(req.getTaskKey()).thenReturn(LEAD_V4_PAGE_1);
        when(req.getFormData()).thenReturn(formData);

        validateRequest(req);
    }

    @Test(expected = PinakaException.class)
    public void validateRequest_ThrowsException_WhenTaskKeyIsMissingOrInvalid() throws PinakaException {
        FormSubmitRequest req = mock(FormSubmitRequest.class);
        Map<String, Object> formData = new HashMap<>();
        formData.put(PAN_NUMBER, "**********");
        formData.put(DOB, "1990-01-01");
        formData.put(GENDER, "Male");
        formData.put(AREA, "Area1");
        formData.put(HOUSE_NUMBER, "123");
        // Missing PINCODE_DETAILS
        when(req.getTaskKey()).thenReturn("invalid");
        when(req.getFormData()).thenReturn(formData);

        validateRequest(req);
    }

    @Test(expected = PinakaException.class)
    public void validSubMapFieldsInMap_ThrowsException() throws PinakaException {
        validSubMapFieldsInMap(new HashMap<>(), "abcd", new ArrayList<>());
    }
}
