package com.flipkart.fintech.pinaka.service.widgettransformer;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.UNALLOCATED_USER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.flipkart.annotations.JsonFile;
import com.flipkart.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(JsonFileParameterResolver.class)
class ErrorScreenTransformerTest {

  private static final String MYNTRA = "MYNTRA";
  private static final String FLIPKART = "FLIPKART";
  private ErrorScreenTransformer errorScreenTransformer;

  private SlotInfo slotInfo;

  @AfterEach
  void tearDown() {
    errorScreenTransformer = null;
    slotInfo = null;
  }

  @BeforeEach
  void setUp(
      @JsonFile("template/idfc/EMandateWaitingScreen.json") String waitingScreenJson,
      @JsonFile("template/idfc/OfferRejectionError.json") String offerRejectionJson,
      @JsonFile("template/common/OfferRejectionErrorMyntra.json") String myntraOfferRejectionJson,
      @JsonFile("template/sandbox/CheckCreditScoreButton.json") String submitButtonJson,
      @JsonFile("template/common/TryWithAnotherLenderAnnouncement.json") String tryWithAnotherLenderAnnouncementJson,
      @JsonFile("template/common/NoLenderAssignedAnnouncement.json") String noLenderAssignedAnnouncementJson,
      @JsonFile("template/common/TryWithAnotherLenderButtons.json") String tryWithAnotherLenderButtonJson
  ) {
    Map<String, String> rejectScreens = new HashMap<>();
    rejectScreens.put(MYNTRA, myntraOfferRejectionJson);
    errorScreenTransformer = new ErrorScreenTransformer(
        waitingScreenJson,
        offerRejectionJson,
        submitButtonJson,
        rejectScreens,
        tryWithAnotherLenderButtonJson,
        tryWithAnotherLenderAnnouncementJson,
        noLenderAssignedAnnouncementJson
    );
    slotInfo = new SlotInfo();
  }

  @Test
  void buildOfferRejectionScreenForMyntra() throws PinakaClientException {
    String rejectionMsg = "Your application did not meet the current requirements of our lending partners.";
    String richButtonActionType = "MERCHANT_HOMEPAGE";
    ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
    applicationDataResponse.setMerchantId(MYNTRA);
    AnnouncementV2WidgetData widgetData = errorScreenTransformer.buildofferRejectionErrorWidgetData(
        applicationDataResponse, slotInfo);
    assertNotNull(widgetData);
    assertNotNull(widgetData.getData());
    assertNotNull(widgetData.getData().getValue());
    assertNotNull(widgetData.getData().getValue().getRichButton());
    assertNotNull(widgetData.getData().getValue().getRichButton().getAction());
    assertNotNull(widgetData.getData().getValue().getRichButton().getAction().getType());
    assertNotNull(widgetData.getData().getValue().getSubTitle());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertEquals(rejectionMsg, widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertEquals(richButtonActionType,
        widgetData.getData().getValue().getRichButton().getAction().getType());
  }

  @Test
  void buildGenericOfferRejectionScreen() throws PinakaClientException {
    String rejectionMsg = "Sorry, your application was not accepted by the lender bank / NBFC.You can try again and apply to other lenders.";
    ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
    applicationDataResponse.setMerchantId(FLIPKART);
    AnnouncementV2WidgetData widgetData = errorScreenTransformer.buildofferRejectionErrorWidgetData(applicationDataResponse, slotInfo);
    assertNotNull(widgetData);
    assertNotNull(widgetData.getData());
    assertNotNull(widgetData.getData().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertEquals(rejectionMsg, widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertNull(widgetData.getData().getValue().getRichButton());
  }

  @Test
  void buildGenericOfferRejectionScreenWithContentId() throws PinakaClientException {
    String rejectionMsg = "Sorry, We could not proceed with your application at the moment. You can try after 30 days ";
    ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
    applicationDataResponse.setMerchantId(FLIPKART);
    slotInfo = new SlotInfo();
    slotInfo.setContentId(UNALLOCATED_USER);
    AnnouncementV2WidgetData widgetData = errorScreenTransformer.buildofferRejectionErrorWidgetData(applicationDataResponse, slotInfo);
    assertNotNull(widgetData);
    assertNotNull(widgetData.getData());
    assertNotNull(widgetData.getData().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertEquals(rejectionMsg, widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertNull(widgetData.getData().getValue().getRichButton());
  }

  @Test
  void buildGenericOfferRejectionScreenIfNoMerchantIdPresent() throws PinakaClientException {
    String rejectionMsg = "Sorry, your application was not accepted by the lender bank / NBFC.You can try again and apply to other lenders.";
    ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
    AnnouncementV2WidgetData widgetData = errorScreenTransformer.buildofferRejectionErrorWidgetData(applicationDataResponse, slotInfo);
    assertNotNull(widgetData);
    assertNotNull(widgetData.getData());
    assertNotNull(widgetData.getData().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertEquals(rejectionMsg, widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertNull(widgetData.getData().getValue().getRichButton());
  }

  @Test
  void buildMandateWaitingScreen() throws PinakaClientException {
    String title = "Your request is being processed by the banking systems.";
    String subTitle = "This can take up to 10 minutes.";
    AnnouncementV2WidgetData widgetData = errorScreenTransformer.buildemandateWaitingScreen();
    assertNotNull(widgetData);
    assertNotNull(widgetData.getData());
    assertNotNull(widgetData.getData().getValue());
    assertNotNull(widgetData.getData().getValue().getTitle());
    assertNotNull(widgetData.getData().getValue().getSubTitle());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue());
    assertNotNull(widgetData.getData().getValue().getSubTitle().getValue().getText());
    assertNotNull(widgetData.getData().getValue().getTitle().getValue());
    assertNotNull(widgetData.getData().getValue().getTitle().getValue().getText());
    assertEquals(title, widgetData.getData().getValue().getTitle().getValue().getText());
    assertEquals(subTitle, widgetData.getData().getValue().getSubTitle().getValue().getText());
  }

  @Test
  void buildSubmitButtonWidget() throws PinakaClientException {
    String primaryButtonActionUrl = "/api/sm/1/application/try-another-lender";
    String primaryButtonText = "Apply now";
    ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
    applicationDataResponse.setMerchantId(FLIPKART);
    SubmitButtonWidgetData submitButtonWidgetData = errorScreenTransformer.buildSubmitButtonWidgetData(
        applicationDataResponse);
    assertNotNull(submitButtonWidgetData);
    assertNotNull(submitButtonWidgetData.getSubmitButton());
    assertNotNull(submitButtonWidgetData.getSubmitButton().getButton());
    assertNotNull(submitButtonWidgetData.getSubmitButton().getButton().getAction());
    assertNotNull(submitButtonWidgetData.getSubmitButton().getButton().getAction().getUrl());
    assertNotNull(submitButtonWidgetData.getSubmitButton().getButton().getValue());
    assertNotNull(submitButtonWidgetData.getSubmitButton().getButton().getValue().getTitle());
    assertEquals(primaryButtonActionUrl,
        submitButtonWidgetData.getSubmitButton().getButton().getAction().getUrl());
    assertEquals(primaryButtonText,
        submitButtonWidgetData.getSubmitButton().getButton().getValue().getTitle());
  }

  @Test
  void buildSubmitButtonWidgetForMyntra() throws PinakaClientException {
    ApplicationDataResponse applicationDataResponse = new ApplicationDataResponse();
    applicationDataResponse.setMerchantId(MYNTRA);
    SubmitButtonWidgetData submitButtonWidgetData = errorScreenTransformer.buildSubmitButtonWidgetData(
        applicationDataResponse);
    assertNull(submitButtonWidgetData);
  }
}
