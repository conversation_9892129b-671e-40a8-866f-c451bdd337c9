package com.flipkart.fintech.pinaka.service.constants;

import com.flipkart.fintech.pinaka.api.request.v6.DataEnum;
import com.flipkart.fintech.pinaka.api.response.v6.HelpDataEnumResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Option;
import com.flipkart.fintech.pinaka.service.enums.PLUserCohort;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class PageConstantTest {

    @Test
    public void testSearchUrl() {
        assertEquals("/1/fintech/calm/search", PageConstant.SearchUrl);
    }

    @Test
    public void testKfsUrl() {
        assertEquals("/1/fintech/calm/kfs", PageConstant.KfsUrl);
    }

    @Test
    public void testPincodeExistenceUrl() {
        assertEquals("/6/pincode/existence", PageConstant.PincodeExistenceUrl);
    }

    @Test
    public void testContactDetails() {
        List<String> contactDetails = PageConstant.CONTACT_DETAILS;
        assertNotNull(contactDetails);
        assertEquals(2, contactDetails.size());
        assertTrue(contactDetails.contains("1860-419-5555"));
        assertTrue(contactDetails.contains("1860-500-5555"));
    }

    @Test
    public void testConstructHelpWidget() {
        // When
        HelpDataEnumResponse helpWidget = PageConstant.constructHelpWidget();

        // Then
        assertNotNull(helpWidget);
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/04/2023/199c1bc1-f9a1-49fc-90ef-626f40816f76.png?q={@quality}", 
                     helpWidget.getUrl());
        assertEquals(DataEnum.HELP, helpWidget.getDataEnum());
        assertEquals("Help", helpWidget.getTitle());
    }

    @Test
    public void testPLCustomerIdentificationFormConstants() {
        // Test header banner URLs
        Map<PLUserCohort, String> headerBannerUrls = PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.headerBannerUrls;
        assertNotNull(headerBannerUrls);
        assertEquals(2, headerBannerUrls.size());
        assertTrue(headerBannerUrls.containsKey(PLUserCohort.NTB));
        assertTrue(headerBannerUrls.containsKey(PLUserCohort.ETB));
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/03/2025/615a1cdf-5c5d-49e0-8c7f-51ac9768d885.png?q={@quality}", 
                     headerBannerUrls.get(PLUserCohort.NTB));
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/03/2025/615a1cdf-5c5d-49e0-8c7f-51ac9768d885.png?q={@quality}", 
                     headerBannerUrls.get(PLUserCohort.ETB));

        // Test header image URL
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/04/2023/fd04f64d-5617-48b0-b0b6-e9be86b6533e.png?q={@quality}", 
                     PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.headerImageURL);

        // Test gender options
        List<Option> genderOptions = PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.genderOptions;
        assertNotNull(genderOptions);
        assertEquals(3, genderOptions.size());
        
        Option maleOption = genderOptions.get(0);
        assertEquals("Male", maleOption.getTitle());
        assertEquals("M", maleOption.getId());
        
        Option femaleOption = genderOptions.get(1);
        assertEquals("Female", femaleOption.getTitle());
        assertEquals("F", femaleOption.getId());
        
        Option othersOption = genderOptions.get(2);
        assertEquals("Others", othersOption.getTitle());
        assertEquals("T", othersOption.getId());

        // Test regex patterns and placeholders
        assertEquals("[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.panRegex);
        assertEquals("EX: ANBLHVH", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.panPlaceholder);
        assertEquals("Eg. 97, Pramila Nilaya", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.housePlaceholder);
        assertEquals("Eg. 3rd Block Koramangala", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.areaPlaceholder);
        assertEquals("Eg. Bengaluru", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.cityPlaceholder);
        assertEquals("Eg. Karnataka", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.statePlaceholder);
        assertEquals("[1-9]{1}[0-9]{5}", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.pincodeRegex);
        assertEquals("Eg. 560034", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.pincodePlaceholder);

        // Test announcement image
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/04/2023/076b4c7e-08fd-49df-baf7-ff094987f5d7.png?q={@quality}", 
                     PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.announcementImageURL);
        assertEquals("201:32", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.announcementImageAspectRatio);

        // Test consent text (just check it's not null and has content)
        assertNotNull(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.consentText);
        assertTrue(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.consentText.length() > 100);
        assertTrue(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.consentText.contains("Scapic Innovations Pvt. Ltd."));

        // Test button text
        assertEquals("Next", PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.buttonText);
    }

    @Test
    public void testPLAdditionalDetailsFormConstants() {
        // Test header image URL
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/04/2023/bee9d3bc-69bf-4cc1-90cf-2c88bbc921cf.png?q={@quality}", 
                     PageConstant.PL_ADDITIONAL_DETAILS_FORM.headerImageURL);

        // Test employment type options
        List<Option> employmentTypeOptions = PageConstant.PL_ADDITIONAL_DETAILS_FORM.employmentTypeOptions;
        assertNotNull(employmentTypeOptions);
        assertEquals(2, employmentTypeOptions.size());
        
        Option salariedOption = employmentTypeOptions.get(0);
        assertEquals("Salaried", salariedOption.getTitle());
        assertEquals("salaried", salariedOption.getId());
        assertEquals("Select this, if you work for a company and receive monthly salary", salariedOption.getSubtitle());
        
        Option selfEmployedOption = employmentTypeOptions.get(1);
        assertEquals("Self-employed", selfEmployedOption.getTitle());
        assertEquals("self_employed", selfEmployedOption.getId());
        assertEquals("Select this, if you run your own business", selfEmployedOption.getSubtitle());

        // Test minimum income values
        assertEquals("20000", PageConstant.PL_ADDITIONAL_DETAILS_FORM.minMonthlyIncome);
        assertEquals("240000", PageConstant.PL_ADDITIONAL_DETAILS_FORM.minAnnualTurnOver);

        // Test industry type options (just check it's not null and has content)
        List<Option> industryTypeOptions = PageConstant.PL_ADDITIONAL_DETAILS_FORM.industryTypeOptions;
        assertNotNull(industryTypeOptions);
        assertTrue(industryTypeOptions.size() > 200); // Large list of industry types
        
        // Test first few industry options
        Option firstIndustry = industryTypeOptions.get(0);
        assertEquals("ACTIVITIES OF TRADE UNIONS", firstIndustry.getTitle());
        assertEquals("1", firstIndustry.getId());

        // Test announcement and other constants
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/04/2023/076b4c7e-08fd-49df-baf7-ff094987f5d7.png?q={@quality}", 
                     PageConstant.PL_ADDITIONAL_DETAILS_FORM.announcementImageURL);
        assertEquals("201:32", PageConstant.PL_ADDITIONAL_DETAILS_FORM.announcementImageAspectRatio);
        assertEquals("Sharing work Details will give you higher loan limits", PageConstant.PL_ADDITIONAL_DETAILS_FORM.noteTitle);
        assertEquals("See your loan offer", PageConstant.PL_ADDITIONAL_DETAILS_FORM.buttonText);
        assertEquals("Enter your monthly salary after the tax deduction", PageConstant.PL_ADDITIONAL_DETAILS_FORM.monthlyIncomeSubtext);
    }

    @Test
    public void testPLOfferScreenFormConstants() {
        assertEquals("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/19/04/2023/d8c2cc3d-d68e-48a1-b8e6-1da00e5f1c20.png?q={@quality}", 
                     PageConstant.PL_OFFER_SCREEN_FORM.announcementImageURL);
        assertEquals("160:24", PageConstant.PL_OFFER_SCREEN_FORM.announcementImageAspectRatio);
        assertEquals("%sHow much do you need?", PageConstant.PL_OFFER_SCREEN_FORM.loanAmountTitle);
        assertEquals("How would you like to repay?", PageConstant.PL_OFFER_SCREEN_FORM.loanEmiTitle);
        assertEquals("%s of interest rate applicable on all tenures", PageConstant.PL_OFFER_SCREEN_FORM.loanEmiSubTitle);
        assertEquals("No additional charges are levied for processing this loan", PageConstant.PL_OFFER_SCREEN_FORM.loanEmiNote);
        assertEquals("See loan summary", PageConstant.PL_OFFER_SCREEN_FORM.kfsButtonText);
        assertEquals("Confirm & Continue", PageConstant.PL_OFFER_SCREEN_FORM.buttonText);
        assertEquals(new BigDecimal("0.00"), PageConstant.PL_OFFER_SCREEN_FORM.flipkartChargesValue);
        assertEquals(new BigDecimal("0.00"), PageConstant.PL_OFFER_SCREEN_FORM.flipkartChargesGST);
        assertEquals("consentSubmitOffer", PageConstant.PL_OFFER_SCREEN_FORM.OFFER_CONSENT);
    }

    @Test
    public void testPLBasicDetailsScreenConsents() {
        // Test that consent maps are initialized
        assertNotNull(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap);
        assertNotNull(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantIDFCConsentMap);
        assertNotNull(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        assertNotNull(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdFkMap);
        assertNotNull(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSmMap);
        assertNotNull(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdIdfcMap);

        // Test that maps have expected content (basic checks)
        assertTrue(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdFkMap.size() > 0);
        assertTrue(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap.size() > 0);
        assertTrue(PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap.size() > 0);
    }

    @Test
    public void testActionHandlerV2Constants() {
        assertEquals("/credit-score/display", PageConstant.ActionHandlerV2Constants.CHECK_SCORE_DISPLAY);
        assertEquals("/credit-score/insights", PageConstant.ActionHandlerV2Constants.CHECK_SCORE_INSIGHTS);
        assertEquals("/credit-score/no-score-display", PageConstant.ActionHandlerV2Constants.NO_CHECK_SCORE_DISPLAY);
        assertEquals("showCheckScoreInsights", PageConstant.ActionHandlerV2Constants.SHOW_CHECK_SCORE_INSIGHTS);
        assertEquals("/ams/v1/unallocated", PageConstant.ActionHandlerV2Constants.NO_LENDER_ALLOCATED);
    }

    @Test
    public void testPageConstantClassInstantiation() {
        // Test that PageConstant can be instantiated (though it's mainly a constants class)
        PageConstant pageConstant = new PageConstant();
        assertNotNull(pageConstant);
    }

    @Test
    public void testNestedClassInstantiation() {
        // Test that nested classes can be instantiated
        PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM customerForm = new PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM();
        assertNotNull(customerForm);

        PageConstant.PL_ADDITIONAL_DETAILS_FORM additionalForm = new PageConstant.PL_ADDITIONAL_DETAILS_FORM();
        assertNotNull(additionalForm);

        PageConstant.PL_OFFER_SCREEN_FORM offerForm = new PageConstant.PL_OFFER_SCREEN_FORM();
        assertNotNull(offerForm);

        PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS consents = new PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS();
        assertNotNull(consents);

        PageConstant.ActionHandlerV2Constants actionHandler = new PageConstant.ActionHandlerV2Constants();
        assertNotNull(actionHandler);
    }
}
