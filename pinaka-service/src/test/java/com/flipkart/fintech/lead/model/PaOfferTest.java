package com.flipkart.fintech.lead.model;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.PaOffer;
import org.junit.Test;

import static org.junit.Assert.*;

public class PaOfferTest {

    @Test
    public void testPaOfferCreationWithAllArgsConstructor() {
        // Given
        Lender lender = Lender.KISSHT;
        String id = "offer123";
        Long amount = 50000L;

        // When
        PaOffer paOffer = new PaOffer(lender, id, amount);

        // Then
        assertEquals(lender, paOffer.getLender());
        assertEquals(id, paOffer.getId());
        assertEquals(amount, paOffer.getAmount());
    }

    @Test
    public void testPaOfferCreationWithNoArgsConstructor() {
        // When
        PaOffer paOffer = new PaOffer();

        // Then
        assertNull(paOffer.getLender());
        assertNull(paOffer.getId());
        assertNull(paOffer.getAmount());
    }

    @Test
    public void testPaOfferSettersAndGetters() {
        // Given
        PaOffer paOffer = new PaOffer();
        Lender lender = Lender.CITI;
        String id = "offer456";
        Long amount = 75000L;

        // When
        paOffer.setLender(lender);
        paOffer.setId(id);
        paOffer.setAmount(amount);

        // Then
        assertEquals(lender, paOffer.getLender());
        assertEquals(id, paOffer.getId());
        assertEquals(amount, paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithDifferentLenders() {
        // Test with various lenders
        Lender[] lenders = {
            Lender.KISSHT, Lender.CITI, Lender.INDIA_BULLS, Lender.MONEYVIEW,
            Lender.AXIS, Lender.IDFC, Lender.FIBE, Lender.HDFC, Lender.KOTAK
        };

        for (Lender lender : lenders) {
            // Given
            String id = "offer_" + lender.name();
            Long amount = 25000L;

            // When
            PaOffer paOffer = new PaOffer(lender, id, amount);

            // Then
            assertEquals(lender, paOffer.getLender());
            assertEquals(id, paOffer.getId());
            assertEquals(amount, paOffer.getAmount());
        }
    }

    @Test
    public void testPaOfferWithValidData() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "valid_offer_123", 100000L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("valid_offer_123", paOffer.getId());
        assertEquals(Long.valueOf(100000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithNullLender() {
        // Given
        PaOffer paOffer = new PaOffer(null, "offer123", 50000L);

        // Then
        assertNull("Lender can be null", paOffer.getLender());
        assertEquals("offer123", paOffer.getId());
        assertEquals(Long.valueOf(50000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithNullId() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, null, 50000L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertNull("ID can be null", paOffer.getId());
        assertEquals(Long.valueOf(50000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithBlankId() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "", 50000L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("", paOffer.getId());
        assertEquals(Long.valueOf(50000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithWhitespaceOnlyId() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "   ", 50000L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("   ", paOffer.getId());
        assertEquals(Long.valueOf(50000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithNullAmount() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "offer123", null);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("offer123", paOffer.getId());
        assertNull("Amount can be null", paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithZeroAmount() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "offer123", 0L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("offer123", paOffer.getId());
        assertEquals(Long.valueOf(0L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithNegativeAmount() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "offer123", -1000L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("offer123", paOffer.getId());
        assertEquals(Long.valueOf(-1000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithLargeAmount() {
        // Given
        Long largeAmount = Long.MAX_VALUE;
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "offer123", largeAmount);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals("offer123", paOffer.getId());
        assertEquals(largeAmount, paOffer.getAmount());
    }

    @Test
    public void testPaOfferEqualsAndHashCode() {
        // Given
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "offer123", 50000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, "offer123", 50000L);
        PaOffer paOffer3 = new PaOffer(Lender.CITI, "offer456", 75000L);

        // Then
        assertEquals("PaOffers with same data should be equal", paOffer1, paOffer2);
        assertEquals("PaOffers with same data should have same hashCode", paOffer1.hashCode(), paOffer2.hashCode());
        assertNotEquals("PaOffers with different data should not be equal", paOffer1, paOffer3);
    }

    @Test
    public void testPaOfferToString() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "offer123", 50000L);

        // When
        String toString = paOffer.toString();

        // Then
        assertNotNull("toString should not be null", toString);
        assertTrue("toString should contain lender", toString.contains("KISSHT"));
        assertTrue("toString should contain id", toString.contains("offer123"));
        assertTrue("toString should contain amount", toString.contains("50000"));
    }

    @Test
    public void testPaOfferWithSpecialCharactersInId() {
        // Given
        String specialId = "offer_123-ABC@#$%";
        PaOffer paOffer = new PaOffer(Lender.KISSHT, specialId, 50000L);

        // Then
        assertEquals(Lender.KISSHT, paOffer.getLender());
        assertEquals(specialId, paOffer.getId());
        assertEquals(Long.valueOf(50000L), paOffer.getAmount());
    }

    @Test
    public void testPaOfferWithAllNullValues() {
        // Given
        PaOffer paOffer = new PaOffer(null, null, null);

        // Then
        assertNull("Lender should be null", paOffer.getLender());
        assertNull("ID should be null", paOffer.getId());
        assertNull("Amount should be null", paOffer.getAmount());
    }

    @Test
    public void testPaOfferFieldModification() {
        // Given
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "original_id", 10000L);

        // When - modify fields
        paOffer.setLender(Lender.CITI);
        paOffer.setId("modified_id");
        paOffer.setAmount(20000L);

        // Then
        assertEquals(Lender.CITI, paOffer.getLender());
        assertEquals("modified_id", paOffer.getId());
        assertEquals(Long.valueOf(20000L), paOffer.getAmount());
    }

    // Comprehensive tests for Lombok-generated methods

    @Test
    public void testToString_WithAllFields_ShouldContainAllFieldValues() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.MONEYVIEW, "toString_offer_001", 750000L);

        // Act
        String toString = paOffer.toString();

        // Assert
        assertNotNull("toString should not be null", toString);
        assertTrue("toString should contain class name", toString.contains("PaOffer"));
        assertTrue("toString should contain lender", toString.contains("MONEYVIEW"));
        assertTrue("toString should contain id", toString.contains("toString_offer_001"));
        assertTrue("toString should contain amount", toString.contains("750000"));
    }

    @Test
    public void testToString_WithNullFields_ShouldHandleNullValues() {
        // Arrange
        PaOffer paOffer = new PaOffer(null, null, null);

        // Act
        String toString = paOffer.toString();

        // Assert
        assertNotNull("toString should not be null", toString);
        assertTrue("toString should contain class name", toString.contains("PaOffer"));
        assertTrue("toString should handle null lender", toString.contains("lender=null") || toString.contains("null"));
        assertTrue("toString should handle null id", toString.contains("id=null") || toString.contains("null"));
        assertTrue("toString should handle null amount", toString.contains("amount=null") || toString.contains("null"));
    }

    @Test
    public void testToString_WithMixedNullFields_ShouldHandleMixedValues() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.AXIS, null, 500000L);

        // Act
        String toString = paOffer.toString();

        // Assert
        assertNotNull("toString should not be null", toString);
        assertTrue("toString should contain lender", toString.contains("AXIS"));
        assertTrue("toString should handle null id", toString.contains("id=null") || toString.contains("null"));
        assertTrue("toString should contain amount", toString.contains("500000"));
    }

    @Test
    public void testEquals_WithSameData_ShouldBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.HDFC, "equals_test_001", 600000L);
        PaOffer paOffer2 = new PaOffer(Lender.HDFC, "equals_test_001", 600000L);

        // Act & Assert
        assertEquals("PaOffers with same data should be equal", paOffer1, paOffer2);
        assertEquals("Equal PaOffers should have same hashCode", paOffer1.hashCode(), paOffer2.hashCode());
        assertTrue("PaOffer should be equal to itself", paOffer1.equals(paOffer1));
    }

    @Test
    public void testEquals_WithDifferentLender_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "same_id", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.CITI, "same_id", 500000L);

        // Act & Assert
        assertNotEquals("PaOffers with different lender should not be equal", paOffer1, paOffer2);
    }

    @Test
    public void testEquals_WithDifferentId_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "id_one", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, "id_two", 500000L);

        // Act & Assert
        assertNotEquals("PaOffers with different id should not be equal", paOffer1, paOffer2);
    }

    @Test
    public void testEquals_WithDifferentAmount_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "same_id", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, "same_id", 600000L);

        // Act & Assert
        assertNotEquals("PaOffers with different amount should not be equal", paOffer1, paOffer2);
    }

    @Test
    public void testEquals_WithNull_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "test_id", 500000L);

        // Act & Assert
        assertNotEquals("PaOffer should not be equal to null", paOffer, null);
        assertNotEquals("Null should not be equal to PaOffer", null, paOffer);
    }

    @Test
    public void testEquals_WithDifferentClass_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "test_id", 500000L);

        // Act & Assert
        assertNotEquals("PaOffer should not be equal to different class", "String object", paOffer);
        assertNotEquals("Different class should not be equal to PaOffer", "String object", paOffer);
    }

    @Test
    public void testEquals_WithSelf_ShouldBeEqual() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "self_test", 500000L);

        // Act & Assert
        assertEquals("PaOffer should be equal to itself", paOffer, paOffer);
        assertEquals("PaOffer should be equal to itself using equals method", paOffer, paOffer);
    }

    @Test
    public void testEquals_WithNullFields_ShouldBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(null, null, null);
        PaOffer paOffer2 = new PaOffer(null, null, null);

        // Act & Assert
        assertEquals("PaOffers with same null fields should be equal", paOffer1, paOffer2);
        assertEquals("Equal PaOffers with null fields should have same hashCode", paOffer1.hashCode(), paOffer2.hashCode());
    }

    @Test
    public void testEquals_WithMixedNullFields_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, null, 500000L);
        PaOffer paOffer2 = new PaOffer(null, "test_id", 500000L);

        // Act & Assert
        assertNotEquals("PaOffers with different null field patterns should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffers with different null field patterns should not be equal (reversed)", paOffer2, paOffer1);
    }

    @Test
    public void testEquals_WithOneNullLender_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "test_id", 500000L);
        PaOffer paOffer2 = new PaOffer(null, "test_id", 500000L);

        // Act & Assert
        assertNotEquals("PaOffer with lender vs null lender should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffer with null lender vs lender should not be equal", paOffer2, paOffer1);
    }

    @Test
    public void testEquals_WithOneNullId_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "test_id", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, null, 500000L);

        // Act & Assert
        assertNotEquals("PaOffer with id vs null id should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffer with null id vs id should not be equal", paOffer2, paOffer1);
    }

    @Test
    public void testEquals_WithOneNullAmount_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "test_id", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, "test_id", null);

        // Act & Assert
        assertNotEquals("PaOffer with amount vs null amount should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffer with null amount vs amount should not be equal", paOffer2, paOffer1);
    }

    @Test
    public void testHashCode_WithSameData_ShouldHaveSameHashCode() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KOTAK, "hash_test_001", 1000000L);
        PaOffer paOffer2 = new PaOffer(Lender.KOTAK, "hash_test_001", 1000000L);

        // Act & Assert
        assertEquals("Equal objects should have same hashCode", paOffer1.hashCode(), paOffer2.hashCode());
    }

    @Test
    public void testHashCode_WithDifferentData_MayHaveDifferentHashCode() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "hash_test_001", 1000000L);
        PaOffer paOffer2 = new PaOffer(Lender.CITI, "hash_test_002", 2000000L);

        // Act
        int hashCode1 = paOffer1.hashCode();
        int hashCode2 = paOffer2.hashCode();

        // Assert
        // Note: Different objects may have same hashCode (hash collision), but it's unlikely
        // We just verify that hashCode method works without throwing exceptions
        assertNotNull("HashCode should be calculated", Integer.valueOf(hashCode1));
        assertNotNull("HashCode should be calculated", Integer.valueOf(hashCode2));
    }

    @Test
    public void testHashCode_WithNullFields_ShouldNotThrowException() {
        // Arrange
        PaOffer paOffer = new PaOffer(null, null, null);

        // Act & Assert
        // Should not throw NullPointerException
        int hashCode = paOffer.hashCode();
        assertNotNull("HashCode should be calculated even with null fields", Integer.valueOf(hashCode));
    }

    // Note: canEqual method has protected access, so we cannot test it directly from this package
    // The equals method functionality is already thoroughly tested above

    @Test
    public void testSetLender_ShouldUpdateLender() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "setter_test_001", 500000L);

        // Act
        paOffer.setLender(Lender.HDFC);

        // Assert
        assertEquals("Lender should be updated", Lender.HDFC, paOffer.getLender());
    }

    @Test
    public void testSetLender_WithNull_ShouldSetNull() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "setter_test_002", 500000L);

        // Act
        paOffer.setLender(null);

        // Assert
        assertNull("Lender should be set to null", paOffer.getLender());
    }

    @Test
    public void testSetId_ShouldUpdateId() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "initial_id", 500000L);

        // Act
        paOffer.setId("updated_id");

        // Assert
        assertEquals("Id should be updated", "updated_id", paOffer.getId());
    }

    @Test
    public void testSetId_WithNull_ShouldSetNull() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "initial_id", 500000L);

        // Act
        paOffer.setId(null);

        // Assert
        assertNull("Id should be set to null", paOffer.getId());
    }

    @Test
    public void testSetId_WithEmptyString_ShouldSetEmptyString() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "initial_id", 500000L);

        // Act
        paOffer.setId("");

        // Assert
        assertEquals("Id should be set to empty string", "", paOffer.getId());
    }

    @Test
    public void testSetId_WithWhitespace_ShouldSetWhitespace() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "initial_id", 500000L);

        // Act
        paOffer.setId("   ");

        // Assert
        assertEquals("Id should be set to whitespace", "   ", paOffer.getId());
    }

    @Test
    public void testSetAmount_ShouldUpdateAmount() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "amount_test", 500000L);

        // Act
        paOffer.setAmount(750000L);

        // Assert
        assertEquals("Amount should be updated", Long.valueOf(750000L), paOffer.getAmount());
    }

    @Test
    public void testSetAmount_WithNull_ShouldSetNull() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "amount_test", 500000L);

        // Act
        paOffer.setAmount(null);

        // Assert
        assertNull("Amount should be set to null", paOffer.getAmount());
    }

    @Test
    public void testSetAmount_WithZero_ShouldSetZero() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "amount_test", 500000L);

        // Act
        paOffer.setAmount(0L);

        // Assert
        assertEquals("Amount should be set to zero", Long.valueOf(0L), paOffer.getAmount());
    }

    @Test
    public void testSetAmount_WithNegative_ShouldSetNegative() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "amount_test", 500000L);

        // Act
        paOffer.setAmount(-1000L);

        // Assert
        assertEquals("Amount should be set to negative value", Long.valueOf(-1000L), paOffer.getAmount());
    }

    @Test
    public void testSettersAndGetters_Integration_ShouldWorkTogether() {
        // Arrange
        PaOffer paOffer = new PaOffer();

        // Act
        paOffer.setLender(Lender.MONEYVIEW);
        paOffer.setId("integration_test_offer");
        paOffer.setAmount(1000000L);

        // Assert
        assertEquals("Lender should be set correctly", Lender.MONEYVIEW, paOffer.getLender());
        assertEquals("Id should be set correctly", "integration_test_offer", paOffer.getId());
        assertEquals("Amount should be set correctly", Long.valueOf(1000000L), paOffer.getAmount());
    }

    @Test
    public void testSettersAndGetters_WithNullValues_ShouldWorkCorrectly() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "initial_id", 500000L);

        // Act
        paOffer.setLender(null);
        paOffer.setId(null);
        paOffer.setAmount(null);

        // Assert
        assertNull("Lender should be null", paOffer.getLender());
        assertNull("Id should be null", paOffer.getId());
        assertNull("Amount should be null", paOffer.getAmount());
    }

    // Comprehensive equals and hashCode tests

    @Test
    public void testEquals_WithPartiallyNullFields_ShouldNotBeEqual() {
        // Test case 1: One has null lender, other has null id
        PaOffer paOffer1 = new PaOffer(null, "test_id", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, null, 500000L);
        assertNotEquals("PaOffers with different null patterns should not be equal", paOffer1, paOffer2);

        // Test case 2: One has null lender, other has null amount
        PaOffer paOffer3 = new PaOffer(null, "test_id", 500000L);
        PaOffer paOffer4 = new PaOffer(Lender.KISSHT, "test_id", null);
        assertNotEquals("PaOffers with different null patterns should not be equal", paOffer3, paOffer4);

        // Test case 3: One has null id, other has null amount
        PaOffer paOffer5 = new PaOffer(Lender.KISSHT, null, 500000L);
        PaOffer paOffer6 = new PaOffer(Lender.KISSHT, "test_id", null);
        assertNotEquals("PaOffers with different null patterns should not be equal", paOffer5, paOffer6);
    }

    @Test
    public void testEquals_WithTwoNullFieldsVsOneNullField_ShouldNotBeEqual() {
        // Test case 1: Two nulls vs one null
        PaOffer paOffer1 = new PaOffer(null, null, 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, null, 500000L);
        assertNotEquals("PaOffers with different null counts should not be equal", paOffer1, paOffer2);

        // Test case 2: Two nulls vs one null (different combination)
        PaOffer paOffer3 = new PaOffer(null, "test_id", null);
        PaOffer paOffer4 = new PaOffer(null, "test_id", 500000L);
        assertNotEquals("PaOffers with different null counts should not be equal", paOffer3, paOffer4);

        // Test case 3: Two nulls vs one null (third combination)
        PaOffer paOffer5 = new PaOffer(Lender.KISSHT, null, null);
        PaOffer paOffer6 = new PaOffer(Lender.KISSHT, "test_id", null);
        assertNotEquals("PaOffers with different null counts should not be equal", paOffer5, paOffer6);
    }

    @Test
    public void testEquals_WithEmptyStringVsNull_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, null, 500000L);

        // Act & Assert
        assertNotEquals("PaOffer with empty string vs null should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffer with null vs empty string should not be equal", paOffer2, paOffer1);
    }

    @Test
    public void testEquals_WithWhitespaceVsNull_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "   ", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, null, 500000L);

        // Act & Assert
        assertNotEquals("PaOffer with whitespace vs null should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffer with null vs whitespace should not be equal", paOffer2, paOffer1);
    }

    @Test
    public void testEquals_WithZeroVsNull_ShouldNotBeEqual() {
        // Arrange
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "test_id", 0L);
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, "test_id", null);

        // Act & Assert
        assertNotEquals("PaOffer with zero amount vs null amount should not be equal", paOffer1, paOffer2);
        assertNotEquals("PaOffer with null amount vs zero amount should not be equal", paOffer2, paOffer1);
    }

    @Test
    public void testEquals_WithDifferentLenderEnums_ShouldNotBeEqual() {
        // Test key combinations of different lenders
        PaOffer paOffer1 = new PaOffer(Lender.KISSHT, "same_id", 500000L);
        PaOffer paOffer2 = new PaOffer(Lender.CITI, "same_id", 500000L);
        assertNotEquals("PaOffers with different lenders (KISSHT vs CITI) should not be equal", paOffer1, paOffer2);

        PaOffer paOffer3 = new PaOffer(Lender.HDFC, "same_id", 500000L);
        PaOffer paOffer4 = new PaOffer(Lender.AXIS, "same_id", 500000L);
        assertNotEquals("PaOffers with different lenders (HDFC vs AXIS) should not be equal", paOffer3, paOffer4);

        PaOffer paOffer5 = new PaOffer(Lender.KOTAK, "same_id", 500000L);
        PaOffer paOffer6 = new PaOffer(Lender.MONEYVIEW, "same_id", 500000L);
        assertNotEquals("PaOffers with different lenders (KOTAK vs MONEYVIEW) should not be equal", paOffer5, paOffer6);
    }

    @Test
    public void testHashCode_ConsistencyCheck_ShouldBeConsistent() {
        // Arrange
        PaOffer paOffer = new PaOffer(Lender.KISSHT, "consistency_test", 500000L);

        // Act - call hashCode multiple times
        int hashCode1 = paOffer.hashCode();
        int hashCode2 = paOffer.hashCode();
        int hashCode3 = paOffer.hashCode();

        // Assert
        assertEquals("HashCode should be consistent across multiple calls", hashCode1, hashCode2);
        assertEquals("HashCode should be consistent across multiple calls", hashCode2, hashCode3);
    }

    @Test
    public void testHashCode_WithPartiallyNullFields_ShouldNotThrowException() {
        // Test case 1: Only lender is null
        PaOffer paOffer1 = new PaOffer(null, "test_id", 500000L);
        int hashCode1 = paOffer1.hashCode();
        assertNotNull("HashCode should be calculated with null lender", Integer.valueOf(hashCode1));

        // Test case 2: Only id is null
        PaOffer paOffer2 = new PaOffer(Lender.KISSHT, null, 500000L);
        int hashCode2 = paOffer2.hashCode();
        assertNotNull("HashCode should be calculated with null id", Integer.valueOf(hashCode2));

        // Test case 3: Only amount is null
        PaOffer paOffer3 = new PaOffer(Lender.KISSHT, "test_id", null);
        int hashCode3 = paOffer3.hashCode();
        assertNotNull("HashCode should be calculated with null amount", Integer.valueOf(hashCode3));
    }

    @Test
    public void testHashCode_WithTwoNullFields_ShouldNotThrowException() {
        // Test case 1: Lender and id are null
        PaOffer paOffer1 = new PaOffer(null, null, 500000L);
        int hashCode1 = paOffer1.hashCode();
        assertNotNull("HashCode should be calculated with null lender and id", Integer.valueOf(hashCode1));

        // Test case 2: Lender and amount are null
        PaOffer paOffer2 = new PaOffer(null, "test_id", null);
        int hashCode2 = paOffer2.hashCode();
        assertNotNull("HashCode should be calculated with null lender and amount", Integer.valueOf(hashCode2));

        // Test case 3: Id and amount are null
        PaOffer paOffer3 = new PaOffer(Lender.KISSHT, null, null);
        int hashCode3 = paOffer3.hashCode();
        assertNotNull("HashCode should be calculated with null id and amount", Integer.valueOf(hashCode3));
    }

    @Test
    public void testHashCode_EqualObjectsContract_ShouldHaveSameHashCode() {
        // Test with all non-null fields
        PaOffer paOffer1 = new PaOffer(Lender.MONEYVIEW, "contract_test", 750000L);
        PaOffer paOffer2 = new PaOffer(Lender.MONEYVIEW, "contract_test", 750000L);

        assertTrue("Objects should be equal", paOffer1.equals(paOffer2));
        assertEquals("Equal objects must have same hashCode", paOffer1.hashCode(), paOffer2.hashCode());

        // Test with all null fields
        PaOffer paOffer3 = new PaOffer(null, null, null);
        PaOffer paOffer4 = new PaOffer(null, null, null);

        assertTrue("Objects with all null fields should be equal", paOffer3.equals(paOffer4));
        assertEquals("Equal objects with null fields must have same hashCode", paOffer3.hashCode(), paOffer4.hashCode());

        // Test with mixed null fields
        PaOffer paOffer5 = new PaOffer(Lender.KISSHT, null, 500000L);
        PaOffer paOffer6 = new PaOffer(Lender.KISSHT, null, 500000L);

        assertTrue("Objects with same mixed null fields should be equal", paOffer5.equals(paOffer6));
        assertEquals("Equal objects with mixed null fields must have same hashCode", paOffer5.hashCode(), paOffer6.hashCode());
    }
}
