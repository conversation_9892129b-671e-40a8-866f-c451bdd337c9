package com.flipkart.fintech.lending.orchestrator.service;

import com.flipkart.fintech.pinaka.api.request.v6.LandingPageRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;

import javax.validation.Valid;

public interface LendingGatewayService {

    PageActionResponse getPage(String merchantId, @Valid LandingPageRequest landingPageRequest);
    PageActionResponse submit(UserActionRequest submitRequest);

}
