package com.flipkart.fintech.lending.orchestrator.service;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.PERSONAL_LOAN;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.OFFER_ORCHESTRATOR_MODE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.OFFER_ORCHESTRATOR_MODE_SHADOW;

import com.codahale.metrics.Timer;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.de.entity.decision.recommendation.business.PlAction;
import com.flipkart.fintech.lending.orchestrator.client.OfferServiceClient;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.PreApprovedOfferDetails;
import com.flipkart.fintech.pandora.client.UserClient;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.common.utils.UserProfileInsightsUtils;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.LmsService;
import com.flipkart.fintech.pinaka.service.core.v6.impl.LoanHandlerImpl;
import com.flipkart.fintech.pinaka.service.core.v7.CreateApplicationRequestFactory;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.utils.CreateApplicationUtils;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.kloud.config.DynamicBucket;
import com.codahale.metrics.MetricRegistry;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.inject.Inject;

import liquibase.util.StringUtils;
import lombok.CustomLog;

@CustomLog
public class BasicOfferOrchestrator implements OfferService {

  private final CreateApplicationRequestFactory applicationRequestFactory;


  private final DynamicBucket dynamicBucket;


  private final UserClient userClient;
  private final UserProfileScores userProfileScores;
  private final OfferComparator offerComparator;
  private final OfferServiceClient offerServiceClient;
  private final LmsService lmsService;


  @Inject
  public BasicOfferOrchestrator(CreateApplicationRequestFactory applicationRequestFactory,
                                DynamicBucket dynamicBucket, UserClient userClient,
                                UserProfileScores userProfileScores,
                                OfferComparator offerComparator, OfferServiceClient offerServiceClient, LmsService lmsService) {
    this.applicationRequestFactory = applicationRequestFactory;
    this.dynamicBucket = dynamicBucket;
    this.userClient = userClient;
    this.userProfileScores = userProfileScores;
    this.offerComparator = offerComparator;
    this.offerServiceClient = offerServiceClient;
    this.lmsService = lmsService;
  }

  @Override
  public CreateApplicationRequest getLenderApplication(LeadDetails leadDetails, MerchantUser merchantUser,String requestId,
      PlAction.Value tnsScore, Map<String, Object> applicationData) throws PinakaException {
    try {
      Integer shippingPincode = getPincodeForUser(merchantUser, requestId);
      updateShippingPincode(shippingPincode, leadDetails);
    } catch (Exception e) {
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(BasicOfferOrchestrator.class, "shipping_pincode", "missed")).mark();
      log.error("Got Exception while fetching pincode for userId : {}", merchantUser.getMerchantUserId(), e);
    }
    applicationData.put("leadId", leadDetails.getLeadId());
    WhitelistEntity whitelistEntity;
    String offerId = "";
    String offerOrchestratorMode = dynamicBucket.getString(OFFER_ORCHESTRATOR_MODE);
    offerOrchestratorMode = offerOrchestratorMode != null ? offerOrchestratorMode : OFFER_ORCHESTRATOR_MODE_SHADOW;
    Optional<LenderOfferEntity> lenderOffer;
    Optional<BorrowerEntity> finalEntity;
    String offerLog = (String.format("OFFER_ORCH_COMPARISION_LEAD_ID:%s,MODE:%s,MERCHANT:%s", leadDetails.getLeadId(), offerOrchestratorMode, merchantUser.getMerchantKey()));

    try(Timer.Context timer = PinakaMetricRegistry.getMetricRegistry().timer(MetricRegistry.name(BasicOfferOrchestrator.class, "breLatency")).time()) {
      List<Lender> lendersToFilterOut = getDisbursedLenders(lmsService.getDisbursedLenders(merchantUser.getSmUserId()));
      lenderOffer = offerComparator.callOfferService(leadDetails, merchantUser, lendersToFilterOut);
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(BasicOfferOrchestrator.class, "executor_service", "success")).mark();
      offerLog = offerLog + String.format(",LENDER_O:%s", lenderOffer.<Object>map(LenderOfferEntity::getLender).orElse(null));
    } catch (Exception e) {
      log.error("Error while working on external offer : {} for merchant user {}", e.getMessage(), merchantUser);
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(BasicOfferOrchestrator.class, "executor_service", "failure")).mark();
      lenderOffer = Optional.empty();
    }

    log.info(offerLog);
    if (!lenderOffer.isPresent()) {
      finalEntity = Optional.empty();
    } else {
      BorrowerEntity entity = new BorrowerEntity();
      LenderOfferEntity offer = lenderOffer.get();
      log.info("Got offer from orchestrator : {}", offer);
      WhitelistEntity we = new WhitelistEntity();
      we.setLender(String.valueOf(offer.getLender()));
      we.setProductType(PERSONAL_LOAN);
      entity.setWhitelist(we);
      entity.setMetadata(offer.getMetadata());
      finalEntity = Optional.of(entity);
    }

    if (!LoanHandlerImpl.isWhitelisted(finalEntity)) {
      return null;
    }
    whitelistEntity = finalEntity.get().getWhitelist();
    offerId = CreateApplicationUtils.getOfferId(finalEntity);
    return createPageResponse(merchantUser, requestId, whitelistEntity, offerId, applicationData);
  }

  private List<Lender> getDisbursedLenders(List<Lender> disbursedLenders) {
    if(disbursedLenders.stream().anyMatch(lender -> lender.equals(Lender.MONEYVIEW))){
      disbursedLenders.add(Lender.MONEYVIEWOPENMKT);
      disbursedLenders.add(Lender.MONEYVIEWMFI);
    }
    return disbursedLenders;
  }

  @Override
  public Optional<PreApprovedOfferDetails> getActivePreApprovedOffersForLead(
      MerchantUser merchantUser) {
    return offerServiceClient.getPreApprovedOffer(merchantUser, Optional.empty()).map(lenderOfferEntity -> new PreApprovedOfferDetails(lenderOfferEntity.getAmount(), lenderOfferEntity.getRoi()));
  }

  private void updateShippingPincode(Integer shippingPincode, LeadDetails leadDetails) {
    leadDetails.getUserProfile().setShippingPincode(shippingPincode);
  }

  private Integer getPincodeForUser(MerchantUser merchantUser, String requestId) throws DataEnrichmentException {
    UserProfileResponseV3 userProfileResponse = new UserProfileResponseV3();
    FetchUserProfileResponse fetchUserProfileResponse = new FetchUserProfileResponse();

    Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
    if(dexterFlag){
      fetchUserProfileResponse = userProfileScores.getUserProfileByDexter(requestId, merchantUser);
    } else{
      userProfileResponse = userProfileScores.getUserProfile(merchantUser);
    }
    String shippingAddressId = UserProfileInsightsUtils.getShippingAddressId(userProfileResponse, fetchUserProfileResponse, dynamicBucket);
    if(StringUtils.isEmpty(shippingAddressId)){
      log.info("shippingAddressId is null for userId : {}", merchantUser.getMerchantUserId());
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(BasicOfferOrchestrator.class, "shipping_pincode", "unavailable")).mark();
      return null;
    }
    log.info("Got shippingAddressId : {} for userId : {}", shippingAddressId, merchantUser.getMerchantUserId());
    return Integer.parseInt(getPincode(merchantUser, shippingAddressId));
  }

  private String getPincode(MerchantUser merchantUser, String shippingAddressId) {
    AddressDetailResponse addressDetails = userClient.getAddressDetailResponse(shippingAddressId,
        merchantUser.getMerchantUserId(), merchantUser.getSmUserId(), merchantUser.getMerchantKey());
    PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(BasicOfferOrchestrator.class, "shipping_pincode", "available")).mark();
    return addressDetails.getPincode();
  }

  private CreateApplicationRequest createPageResponse(MerchantUser merchantUser, String  requestId, WhitelistEntity whitelist,
                                                      String offer_id, Map<String, Object> applicationData) throws PinakaException {
    return applicationRequestFactory.create(merchantUser, requestId, whitelist, offer_id, applicationData);
  }

}