package com.flipkart.fintech.lending.orchestrator.service;

import com.codahale.metrics.Timer;
import com.flipkart.fintech.lending.orchestrator.client.OfferServiceClient;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.LeadObject;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.model.UserProfileObject;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.codahale.metrics.MetricRegistry;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.inject.Inject;
import lombok.CustomLog;

@CustomLog
public class OfferComparator {

  private final OfferServiceClient offerServiceClient;

  @Inject
  public OfferComparator(OfferServiceClient offerServiceClient) {
    this.offerServiceClient = offerServiceClient;
  }

  public Optional<LenderOfferEntity> callOfferService(LeadDetails leadDetails, MerchantUser merchantUser, List<Lender> lendersToFilterOut) {
    LenderOfferEntity offerFromExternal = null;
    try {
      //log.info("Started evaluation for merchantUser : {} leadDetails : {}", merchantUser, leadDetails);
      LeadObject leadObject = convertToLeadObject(leadDetails, lendersToFilterOut);
      offerFromExternal = offerServiceClient.getOfferFromOfferService(leadObject, merchantUser
      );
      if(Objects.isNull(offerFromExternal.getLender())) {
        log.error("getting null lender for merchant user {}", merchantUser);
        return Optional.empty();
      }
      log.info("{}_external_offer : {}", merchantUser.getMerchantUserId(), offerFromExternal);
    } catch(Exception e) {
      log.error("Exception while running external offer service for user : {}", merchantUser.getMerchantUserId(), e);
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(OfferComparator.class, "ext_offer_service", "failure")).mark();
    }
    return Optional.ofNullable(offerFromExternal);
  }

  // Remove this code during clean up. Currently, Profile object uses snake case and new service is not supporting it for some reason. Hence, this workaround

  private LeadObject convertToLeadObject(LeadDetails leadDetails, List<Lender> lendersToFilterOut) {
    ProfileDetailedResponse userProfile = leadDetails.getUserProfile();
    UserProfileObject userProfileObject = UserProfileObject.builder()
            .userEnteredPincode(userProfile.getUserEnteredPincode())
            .merchantUserId(userProfile.getMerchantUserId())
            .smUserId(userProfile.getSmUserId())
            .profileId(userProfile.getProfileId())
            .firstName(userProfile.getFirstName())
            .lastName(userProfile.getLastName())
            .gender(userProfile.getGender())
            .dob(userProfile.getDob())
            .employmentType(userProfile.getEmploymentType())
            .phoneNo(userProfile.getPhoneNo())
            .pan(userProfile.getPan())
            .email(userProfile.getEmail())
            .shippingPincode(userProfile.getShippingPincode())
            .incomeSource(userProfile.getIncomeSource())
            .build();
    return LeadObject.builder()
            .stateOfLead(leadDetails.getStateOfLead())
            .leadId(leadDetails.getLeadId())
            .applicationData(leadDetails.getApplicationData())
            .applicationUserData(leadDetails.getApplicationUserData())
            .userProfile(userProfileObject)
            .experianData(leadDetails.getExperianData())
            .experianReport(leadDetails.getExperianReport())
            .binScore(leadDetails.getUserScores().getBinScore())
            .losV2CremoBand(leadDetails.getUserScores().getLosV2CremoBand())
            .idfcCremoBand(leadDetails.getUserScores().getIdfcCremoBand())
            .granularCremoBand(leadDetails.getUserScores().getGranularCremoBand())
            .monthlyIncome(leadDetails.getMonthlyIncome())
            .lendersToFilterOut(lendersToFilterOut)
            .build();
  }

  private void compareOffers(LenderOfferEntity serviceOffer, Optional<LenderOfferEntity> orchestratorOffer,
      MerchantUser merchantUser) {
    if(serviceOffer == null && orchestratorOffer.isPresent() && orchestratorOffer.get().getLender() != null) {
      log.info("EXT_OFFER_SVC returned null lender: {}, {}", merchantUser.getMerchantUserId(), merchantUser.getMerchantKey());
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(OfferComparator.class, "ext_offer_service", "empty")).mark();
    }
    if(!orchestratorOffer.isPresent() && serviceOffer != null && serviceOffer.getLender() != null) {
      log.info("EXT_OFFER_SVC returned lender when it should not: {}, {}", merchantUser.getMerchantUserId(), merchantUser.getMerchantKey());
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(OfferComparator.class, "ext_offer_service", "present")).mark();
    }

    if(!serviceOffer.getLender().equals(orchestratorOffer.get().getLender())) {
      log.info("EXT_OFFER_SVC returned wrong lender: {}, {}", merchantUser.getMerchantUserId(), merchantUser.getMerchantKey());
      PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(OfferComparator.class, "ext_offer_service", "mismatch")).mark();
    }

  }

}
