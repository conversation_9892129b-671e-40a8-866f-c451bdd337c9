package com.flipkart.fintech.lead.model;

import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.security.aes.AESService;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class Name {

    private String firstName;

    private String lastName;

    private boolean blank;

    public Name(String fullName, boolean isEncrypted) {

        String[] parts = fullName.trim().split("\\s+");
        if (parts.length < 2) {
            blank = true;
            return;
        }

        String fName = String.join(" ", java.util.Arrays.copyOfRange(parts, 0, parts.length - 1));
        String lName = parts[parts.length - 1];

        if (isEncrypted) {
            this.firstName = fName;
            this.lastName = lName;
        } else {
            this.firstName = new String(Base64.getEncoder().encode(AESService.encrypt(PinakaConstants.PLConstants.SMONEY_PL_DATA_ENCRYPTION_KEY, fName.getBytes(StandardCharsets.UTF_8))), StandardCharsets.UTF_8);
            this.lastName = new String(Base64.getEncoder().encode(AESService.encrypt(PinakaConstants.PLConstants.SMONEY_PL_DATA_ENCRYPTION_KEY, lName.getBytes(StandardCharsets.UTF_8))), StandardCharsets.UTF_8);
        }
        this.blank = false;
    }

    /**
     * Returns the full name as a formatted string in uppercase.
     * Returns null if the name is blank or both first and last names are empty.
     */
    public String getFormattedName() {
        if (blank || (StringUtils.isBlank(firstName) && StringUtils.isBlank(lastName))) {
            return null;
        }

        StringBuilder fullName = new StringBuilder();
        if (StringUtils.isNotBlank(firstName)) {
            fullName.append(firstName.trim());
        }
        if (StringUtils.isNotBlank(lastName)) {
            if (fullName.length() > 0) {
                fullName.append(" ");
            }
            fullName.append(lastName.trim());
        }

        return fullName.toString().toUpperCase();
    }

    /**
     * Checks if this Name object has valid content.
     * A name is considered to have valid content if it's not marked as blank
     * AND has at least one non-blank name component (firstName or lastName).
     *
     * @return true if the name has valid content, false otherwise
     */
    public boolean hasValidContent() {
        return !blank && (StringUtils.isNotBlank(firstName) || StringUtils.isNotBlank(lastName));
    }

}
