package com.flipkart.fintech.lead.model;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@Builder
public class LeadV4DataGatheringResponse {
    private Name name;
    private String phoneNumber;
    private LenderOfferEntity paOffer;

    // Cached USER_DATA map from FormWidgetDataFetcher
    private Map<String, Object> cachedUserData;
    
    public ContentScenario getContentScenario() {
        boolean hasOffer = paOffer != null;
        
        if (hasName() && hasOffer) {
            return ContentScenario.PERSONALIZED_WITH_PA_OFFER;
        } else if (hasName()) {
            return ContentScenario.PERSONALIZED_GENERIC_OFFER;
        } else if (hasOffer) {
            return ContentScenario.GENERIC_USER_WITH_PA_OFFER;
        } else {
            return ContentScenario.GENERIC_ALL;
        }
    }

    public boolean hasName() {
        return name != null && name.hasValidContent();
    }

    public boolean hasPhoneNumber() {
        return StringUtils.isNotBlank(phoneNumber);
    }

    public boolean hasCachedUserData() {
        return cachedUserData != null && !cachedUserData.isEmpty();
    }

    public enum ContentScenario {
        PERSONALIZED_WITH_PA_OFFER,
        PERSONALIZED_GENERIC_OFFER,
        GENERIC_USER_WITH_PA_OFFER,
        GENERIC_ALL
    }
}
