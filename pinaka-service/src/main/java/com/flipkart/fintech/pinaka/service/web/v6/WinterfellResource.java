package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.request.v6.EligibleOfferRequest;
import com.flipkart.fintech.pinaka.api.request.v6.IdentifyCustomerRequest;
import com.flipkart.fintech.pinaka.api.request.v6.RejectApplicationRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SubmitOfferRequest;
import com.flipkart.fintech.pinaka.service.core.v6.WinterfellResourceHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.http.WinterfellNodeResponse;
import com.google.inject.Inject;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl")
public class WinterfellResource {
    private final WinterfellResourceHandler winterfellResourceHandler;

    @Inject
    public WinterfellResource(WinterfellResourceHandler winterfellResourceHandler) {
        this.winterfellResourceHandler = winterfellResourceHandler;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Customer Identification")
    @Path("/customer-identification")
    public WinterfellNodeResponse identifyCustomer(@Valid IdentifyCustomerRequest identifyCustomerRequest,
                                                   @HeaderParam("X-Merchant-Id") String merchantId) throws PinakaException {
        log.info("Request received for customer identification accountId: {}, applicationId: {}",
                identifyCustomerRequest.getAccountId(), identifyCustomerRequest.getApplicationId());
        return winterfellResourceHandler.identifyCustomer(identifyCustomerRequest, merchantId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Eligible Offer")
    @Path("/eligible-offer")
    public WinterfellNodeResponse eligibleOffer(@Valid EligibleOfferRequest eligibleOfferRequest,
                                                @HeaderParam("X-Merchant-Id") String merchantId,
                                                @HeaderParam("X-Request-Id") String requestId) throws PinakaException {
        log.info("Request received for eligible Offer accountId: {}, applicationId: {}",
                eligibleOfferRequest.getAccountId(), eligibleOfferRequest.getApplicationId());

        return winterfellResourceHandler.eligibleOffer(eligibleOfferRequest, merchantId, requestId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Submit Offer")
    @Path("/submit-offer")
    public WinterfellNodeResponse submitOffer(@Valid SubmitOfferRequest submitOfferRequest,
                                              @HeaderParam("X-Merchant-Id") String merchantId) throws PinakaException {
        log.info("Request received for submit offer accountId: {}, applicationId: {}",
                submitOfferRequest.getAccountId(), submitOfferRequest.getApplicationId());


        return winterfellResourceHandler.submitOffer(submitOfferRequest, merchantId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Reject Application")
    @Path("/reject")
    public WinterfellNodeResponse rejectApplication(@Valid RejectApplicationRequest rejectApplicationRequest,
                                                    @HeaderParam("X-Merchant-Id") String merchantId) {
        log.info("Request received for reject application accountId: {}, applicationId: {}");

        return winterfellResourceHandler.rejectApplication(rejectApplicationRequest, merchantId);
    }


}
