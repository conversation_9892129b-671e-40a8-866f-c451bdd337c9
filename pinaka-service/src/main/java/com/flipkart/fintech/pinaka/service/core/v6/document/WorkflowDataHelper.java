package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.flipkart.fintech.pinaka.api.request.v4.kyc.UploadDocumentRequest;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import com.flipkart.fintech.stratum.api.models.kyc.xmlKyc.KycDocumentDetails;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import java.util.HashMap;
import java.util.Map;

public class WorkflowDataHelper {

  static Map<String, VariableData> createWorkflowData(String applicationId,
      UploadDocumentRequest uploadDocumentRequest) {
    KycDocumentDetails kycDocumentDetails = uploadDocumentRequest.getKycDocumentDetails();
    EncryptionKeyData docEncryptionKeyData = uploadDocumentRequest.getEncryptionKeyData();
    Map<String, VariableData> workflowData = new HashMap<>();
    setDocumentKey(applicationId, kycDocumentDetails);
    setKycDocumentDetails(workflowData, kycDocumentDetails);
    setDocumentEncryptionKeyData(workflowData, docEncryptionKeyData);
    return workflowData;
  }

  private static void setDocumentKey(String applicationId, KycDocumentDetails kycDocumentDetails) {
    kycDocumentDetails.setBase64EncodedData(null);
    kycDocumentDetails.setDocStoreReferenceKey(applicationId);
  }

  private static void setKycDocumentDetails(Map<String, VariableData> workflowData, KycDocumentDetails kycDocumentDetails) {
    workflowData.put("kyc_document_details", new VariableData(true, kycDocumentDetails));
  }

  private static void setDocumentEncryptionKeyData(Map<String, VariableData> workflowData, EncryptionKeyData docEncryptionKeyData) {
    workflowData.put("doc_encryption_key_data", new VariableData(true, docEncryptionKeyData));
  }

}