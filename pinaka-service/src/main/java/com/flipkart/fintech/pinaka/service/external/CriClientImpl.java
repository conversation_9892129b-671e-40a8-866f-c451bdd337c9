package com.flipkart.fintech.pinaka.service.external;

import com.flipkart.de.entity.client.request.pl.PlDiscoveryRequest;
import com.flipkart.de.entity.decision.response.DecisionResponseEntity;
import com.flipkart.fintech.pinaka.service.application.CriClientConfig;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.kloud.config.DynamicBucket;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import de.client.shade.com.flipkart.de.entity.client.request.CfOnboardRequestEntity;
import de.client.shade.com.flipkart.de.entity.decision.usecase.CfOnboardDecisionEntity;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.util.UUID;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.PERSONAL_LOAN;

@CustomLog
public class CriClientImpl implements CriClient {

    public static final String TNS_PATH = "/de/pl/discovery";
    public static final String X_CLIENT_ID = "X-CLIENT-ID";
    public static final String X_REQUEST_ID = "X-Request-Id";
    public static final String X_DOMAIN = "X-Domain";
    public static final String X_MARKETPLACE_CONTEXT = "X-MARKETPLACE-CONTEXT";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String FLIPKART = "FLIPKART";
    public static final String SUPERMONEY = "SUPERMONEY";
    private static final String APPLICATION_JSON = "application/json";

    private final WebTarget webTarget;

    @Inject
    public CriClientImpl(Client client, CriClientConfig criClientConfig) {
        this.webTarget = client.target(criClientConfig.getHost());
    }

    @HystrixCommand(
            groupKey = "CRI",
            commandKey = "GET_TNS_DETAILS"
    )
    @Override
    public DecisionResponseEntity getTNSDetails(PlDiscoveryRequest plDiscoveryRequest) {
        Response response = null;
        DecisionResponseEntity decisionResponseEntity;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(TNS_PATH)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(X_CLIENT_ID, PERSONAL_LOAN);
            invocationBuilder.header(X_DOMAIN, FLIPKART);
            invocationBuilder.header(X_MARKETPLACE_CONTEXT, SUPERMONEY);
            invocationBuilder.header(CONTENT_TYPE, APPLICATION_JSON);
            invocationBuilder.header(X_REQUEST_ID, UUID.randomUUID().toString());
            response = invocationBuilder.post(Entity.json(plDiscoveryRequest));
            if (response.getStatus() != 200) {
                log.error("TNS api error response for account id {} is {}", plDiscoveryRequest.getAccountDetails().getAccountId(),
                        response.readEntity(String.class));
                throw new PinakaException(String.format("TNS call failed for account id %s",
                        plDiscoveryRequest.getAccountDetails().getAccountId()));
            }
            decisionResponseEntity = response.readEntity(DecisionResponseEntity.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return decisionResponseEntity;
    }
}
