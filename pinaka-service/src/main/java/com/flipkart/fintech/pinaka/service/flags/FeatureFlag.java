package com.flipkart.fintech.pinaka.service.flags;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
public class FeatureFlag {
    private final Boolean isActive;

    private final Integer activePercentage;

    private final Set<String> enabledUsers;

    public FeatureFlag(boolean isActive, int activePercentage, List<String> enabledUsers) {
        this.isActive = isActive;
        this.activePercentage = Math.max(0, Math.min(activePercentage, 100));
        this.enabledUsers = new HashSet<>();
        if(enabledUsers != null) {
            this.enabledUsers.addAll(enabledUsers);
        }
    }

    public boolean shouldExecute(String userId) {
        if(enabledUsers.contains(userId)) {
            return true;
        }
        boolean withinRange = Math.abs(userId.hashCode() % 100) < activePercentage;
        return isActive && withinRange;
    }
}
