package com.flipkart.fintech.pinaka.service.arsenal;

import com.flipkart.mobile.parser.reponseobject.UserAgentResponse;
import java.util.Objects;

/* Copied from AAPI code: com.flipkart.dataprovider.helpers.UserAgentHelper
* */
public class UserAgentHelper {
  public static final String IOS_PLATFORM_STRING = "ios";
  public static final String MSITE_APP_STRING = "msite";
  public static final String UCWEB_APP_STRING = "ucweb";
  public static final String ANDROID_PLATFORM_STRING = "android";

  public static boolean isAndroidPlatform(UserAgentResponse userAgentResponse) {
    return isPlatformEquals(userAgentResponse, ANDROID_PLATFORM_STRING);
  }

  public static boolean isMsite(UserAgentResponse userAgentResponse) {
    return checkAppType(userAgentResponse, MSITE_APP_STRING) || checkAppType(userAgentResponse, UCWEB_APP_STRING);
  }

  private static boolean isPlatformEquals(UserAgentResponse userAgentResponse, String platform) {
    if (userAgentResponse == null || userAgentResponse.fkApp == null ||
        isNotNullOrEmpty(userAgentResponse.fkApp.platform)) {
      return false;
    }
    return userAgentResponse.fkApp.platform.equalsIgnoreCase(platform);
  }

  private static boolean checkAppType(UserAgentResponse userAgentResponse, String type) {
    return userAgentResponse != null && userAgentResponse.fkApp != null && userAgentResponse.fkApp.type != null && userAgentResponse.fkApp.type.equalsIgnoreCase(type);
  }

  private static boolean isNotNullOrEmpty(String str) {
    return str == null|| str.trim().isEmpty() || str.trim().equalsIgnoreCase("null");
  }
}
