package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.amazonaws.services.s3.model.GetObjectRequest;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.api.request.v4.kyc.UploadDocumentRequest;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentUploadResponse;
import com.flipkart.fintech.stratum.api.models.kyc.xmlKyc.KycDocumentDetails;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.google.inject.Inject;
import java.util.Map;
import lombok.CustomLog;

@CustomLog
public class D42DocumentHelper implements DocumentHelper {

  private final ObjectStore objectStore;

  @Inject
  public D42DocumentHelper(ObjectStore objectStore) {
    this.objectStore = objectStore;
  }

  @Override
  public Map<String, VariableData> setKycDocumentUploadRequestData(String applicationId,
      UploadDocumentRequest uploadDocumentRequest) throws Exception {
    uploadDocument(applicationId, uploadDocumentRequest);
    return WorkflowDataHelper.createWorkflowData(applicationId, uploadDocumentRequest);
  }


  @Override
  public void uploadDocument(String applicationId, UploadDocumentRequest uploadDocumentRequest)
      throws Exception {
    Tenant tenant = RequestContextThreadLocal.get().getTenantId();
    Map<String, String> d42MetaData = uploadDocumentRequest.createD42MetaData();
    String documentKey = DocumentUtils.getDocumentKey(applicationId);
    String bucketName = DocumentUtils.getBucketName(tenant);
    KycDocumentDetails document = uploadDocumentRequest.getKycDocumentDetails();
    byte[] content = document.getBase64EncodedData();
    objectStore.upload(bucketName, documentKey, content, d42MetaData);
  }

  @Override
  public DocumentUploadResponse uploadDocument(String applicationId, UploadDocumentRequestV6 uploadDocumentRequest)
      throws Exception {
    Tenant tenant = Tenant.CALM;
    Map<String, String> d42MetaData = uploadDocumentRequest.createD42MetaData();
    Document document = uploadDocumentRequest.getDocument();
    String documentKey = DocumentUtils.getDocumentKey(applicationId);
    String bucketName = DocumentUtils.getBucketName(tenant, document);
    byte[] content = document.getBase64EncodedData();
    objectStore.upload(bucketName, documentKey, content, d42MetaData);
    return new DocumentUploadResponse(bucketName + "/" + documentKey);
  }

    @Override
    public DocumentDownloadResponse downloadDocument(String documentId) throws Exception {
        String[] split = documentId.split("/");
        GetObjectRequest getObjectRequest = new GetObjectRequest(split[0], split[1]);
        return objectStore.getDocument(getObjectRequest);
    }

}
