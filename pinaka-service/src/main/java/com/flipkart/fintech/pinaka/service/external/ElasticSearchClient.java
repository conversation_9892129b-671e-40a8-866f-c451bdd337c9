package com.flipkart.fintech.pinaka.service.external;

import com.google.inject.ImplementedBy;
import org.elasticsearch.search.SearchHits;

import java.util.List;
import java.util.Map;

@ImplementedBy(ElasticSearchClientImpl.class)
public interface ElasticSearchClient {
    SearchHits prefixSearch(String prefix, String index, String field, int size, String sortField);

    SearchHits prefixSearch(String prefix, String index, String field, int size);

    boolean matchString(String input, String index, String field);

    Map<String, Object> fetchDocument(String input, String index, String field);

    Map<String, List<String>> getUniqueValuesForFields(List<String> fields, String index);
}
