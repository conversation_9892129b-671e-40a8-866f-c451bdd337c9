package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ReviewUserDataSourceResponse {
    private EncryptionData encryptionData;
    private Map<String, Object> queryParams;
    private InitialUserDataResponse initialUserDataResponse;
    private ProfileDetailedResponse profile;
}
