package com.flipkart.fintech.pinaka.service.data.impl;

import com.flipkart.fintech.pinaka.service.data.MerchantEntityDao;
import com.flipkart.fintech.pinaka.service.data.model.MerchantEntity;
import com.google.inject.Inject;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/08/17.
 */
public class MerchantEntityEntityDaoImpl extends AbstractDAO<MerchantEntity> implements MerchantEntityDao {

    @Inject
    public MerchantEntityEntityDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    @Override
    public MerchantEntity getByKey(String merchantKey) {
        Criteria criteria = criteria().add(Restrictions.eq("merchantKey", merchantKey));
        return uniqueResult(criteria);
    }

    @Override
    public MerchantEntity getById(Long id) {
        return getById(id);
    }
}
