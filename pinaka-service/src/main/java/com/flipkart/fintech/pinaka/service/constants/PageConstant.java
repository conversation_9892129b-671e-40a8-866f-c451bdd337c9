package com.flipkart.fintech.pinaka.service.constants;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.DataEnum;
import com.flipkart.fintech.pinaka.api.response.v6.HelpDataEnumResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Option;
import com.flipkart.fintech.pinaka.service.enums.PLUserCohort;
import com.flipkart.fintech.pinaka.service.widgettransformer.ConsentFor;
import com.flipkart.fintech.pinaka.service.widgettransformer.ConsentObject;

import java.math.BigDecimal;
import java.util.*;

public class PageConstant {

    public static final String SearchUrl = "/1/fintech/calm/search";
    public static final String KfsUrl = "/1/fintech/calm/kfs";
    public static final String PincodeExistenceUrl = "/6/pincode/existence";
    public static final List<String> CONTACT_DETAILS = Arrays.asList("1860-419-5555", "1860-500-5555");

    public static class PL_CUSTOMER_IDENTIFICATION_FORM {
        public static final Map<PLUserCohort, String> headerBannerUrls = new HashMap<PLUserCohort, String >() { {
            put(PLUserCohort.NTB, "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/03/2025/615a1cdf-5c5d-49e0-8c7f-51ac9768d885.png?q={@quality}");
            put(PLUserCohort.ETB, "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/07/03/2025/615a1cdf-5c5d-49e0-8c7f-51ac9768d885.png?q={@quality}");
        }
        };
        public static final String headerImageURL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/04/2023/fd04f64d-5617-48b0-b0b6-e9be86b6533e.png?q={@quality}";
        public static final List<Option> genderOptions = new ArrayList<Option>() {{
            add(new Option("Male", "M", null));
            add(new Option("Female", "F", null));
            add(new Option("Others", "T", null));
        }};
        public static final String panRegex = "[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}";
        public static final String panPlaceholder = "EX: ANBLHVH";
        public static final String housePlaceholder = "Eg. 97, Pramila Nilaya";
        public static final String areaPlaceholder = "Eg. 3rd Block Koramangala";
        public static final String cityPlaceholder = "Eg. Bengaluru";
        public static final String statePlaceholder = "Eg. Karnataka";
        public static final String pincodeRegex = "[1-9]{1}[0-9]{5}";
        public static final String pincodePlaceholder = "Eg. 560034";
        public static final String announcementImageURL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/04/2023/076b4c7e-08fd-49df-baf7-ff094987f5d7.png?q={@quality}";
        public static final String announcementImageAspectRatio = "201:32";
        public static final String consentText = "I hereby expressly consent and authorize Scapic Innovations Pvt. Ltd. \"(a Flipkart group company)\" to obtain, collect, process, record my personal data including personal information and sensitive personal information (“Data”) during Loan Journey on its Platform/Application and share my Data with [Lending Partners](https://www.flipkart.com/pages/scapic-lp-tnc) for the purpose of processing my loan application. I further declare and confirm that the [Lending Partners](https://www.flipkart.com/pages/scapic-lp-tnc) are authorized to share and use my Data with their service providers, and/or third parties, as required, for any purpose(s) specifically specified by the [Lending Partners](https://www.flipkart.com/pages/scapic-lp-tnc) to Scapic Innovations Pvt. Ltd." +
                "\n\n" +
                "I hereby confirm and accept that I have read the [terms and conditions](https://www.flipkart.com/pages/scapic-tnc) and [privacy policy](https://www.flipkart.com/pages/scapic-privacy-policy) of Scapic Innovations Pvt. Ltd. I hereby also confirm that I'm an Indian citizen and I authorize Scapic Innovations Pvt. Ltd. and its [Lending Partners](https://www.flipkart.com/pages/scapic-lp-tnc) to fetch my credit report from the Credit Bureau basis Data shared by Scapic Innovations Pvt. Ltd.";

        public static final String buttonText = "Next";
    }

    public static HelpDataEnumResponse constructHelpWidget() {
        HelpDataEnumResponse helpDataEnumResponse = HelpDataEnumResponse.builder()
                .url("https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/04/2023/199c1bc1-f9a1-49fc-90ef-626f40816f76.png?q={@quality}")
                .dataEnum(DataEnum.HELP)
                .title("Help")
                .build();

        return helpDataEnumResponse;
    }

    public static class PL_ADDITIONAL_DETAILS_FORM {
        public static final String headerImageURL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/04/2023/bee9d3bc-69bf-4cc1-90cf-2c88bbc921cf.png?q={@quality}";

        public static final List<Option> employmentTypeOptions = new ArrayList<Option>() {{
            add(new Option("Salaried", "salaried", "Select this, if you work for a company and receive monthly salary"));
            add(new Option("Self-employed", "self_employed", "Select this, if you run your own business"));
        }};

        public static final String minMonthlyIncome = "20000";
        public static final String minAnnualTurnOver = "240000";

        public static final List<Option> industryTypeOptions = new ArrayList<Option>() {{
            add(new Option("ACTIVITIES OF TRADE UNIONS","1",null));
            add(new Option("AIR TRAVEL AGENTS","2",null));
            add(new Option("AVIATION","3",null));
            add(new Option("BUILDERS","4",null));
            add(new Option("CABLE OPERATORS / VIDEO LIBRARY OWNERS / VIDEO PARLOURS","5",null));
            add(new Option("CHARITABLE TRUST","6",null));
            add(new Option("CYBERCAFES/  INTERNET COMPANIES","7",null));
            add(new Option("DSA'S / VERIFICATION AGENCIES / COLLECTION AGENCIES / REPOSSESSION AGENCIES","8",null));
            add(new Option("EDUCATION AND TRAINING","9",null));
            add(new Option("SALARIED - OTHERS","10",null));
            add(new Option("SHIPPING","11",null));
            add(new Option("STANDALONE BAR","12",null));
            add(new Option("STD / PCO OUTLETS","13",null));
            add(new Option("STOCK BROKERS","14",null));
            add(new Option("TAX CONSULTANTS (NON QUALIFIED)","15",null));
            add(new Option("TAX CONSULTANTS (QUALIFIED)","16",null));
            add(new Option("TELECOMMUNICATION AND TELECOM SERVICES","17",null));
            add(new Option("TRANSPORT OPERATORS","18",null));
            add(new Option("FINANCE COMPANIES/ PRIVATE MONEY LENDERS","19",null));
            add(new Option("Practicing Doctor - Homeopathic/Ayurvedic/Unani","20",null));
            add(new Option("ENTERTAINMENT AND MEDIA","21",null));
            add(new Option("FILM PERSONALITIES - RADIO / TV AND FILM INDUSTRY (ACTORS / ARTISTS / PRODUCERS / DIRECTORS) / FILM DISTRIBUTORS.","22",null));
            add(new Option("Finance companies / Financial Intermediaries","23",null));
            add(new Option("Firms/Companies Dealing In Plantation / Aquaculture / Chit Funds / Nidhis / money lending /Money Exchangers","24",null));
            add(new Option("HOTELS","25",null));
            add(new Option("Builders / Infrastructure Construction / Real Estate Developer","26",null));
            add(new Option("IT AND ITES","27",null));
            add(new Option("JOURNALISTS","28",null));
            add(new Option("LAWYER/TAX CONSULTANTS","29",null));
            add(new Option("LOTTERY BUSINESS / ANY GAMBLING BUSINESS","30",null));
            add(new Option("MANAGEMENT CONSULTANTS -NON PROFESSIONAL CONSULTANTS","31",null));
            add(new Option("MANPOWER CONSULTANTS / PLACEMENT AGENCIES / LABOUR CONTRACTORS/ SECURITY AGENCY","32",null));
            add(new Option("MEDICAL AND HEALTH SERVICES","33",null));
            add(new Option("MOTOR TRAINING SCHOOLS","34",null));
            add(new Option("MULTI- LEVEL / NETWORK MARKETING BUSINESS","35",null));
            add(new Option("NBFC","36",null));
            add(new Option("NON - MNC COURIER COMPANIES","37",null));
            add(new Option("OIL AND GAS RELATED SERVICES","38",null));
            add(new Option("Politicians / linked to Politics","39",null));
            add(new Option("REAL ESTATE AGENTS","40",null));
            add(new Option("ENGINEERING","41",null));
            add(new Option("POWER - TRANSMISSION","42",null));
            add(new Option("AIR TRAVEL AGENTS","43",null));
            add(new Option("CABLE OPERATORS / VIDEO LIBRARY OWNERS / VIDEO PARLOURS","44",null));
            add(new Option("DSA'S / VERIFICATION AGENCIES / COLLECTION AGENCIES / REPOSSESSION AGENCIES","45",null));
            add(new Option("EDUCATION AND TRAINING","46",null));
            add(new Option("ENGINEERING","47",null));
            add(new Option("ENTERTAINMENT AND MEDIA","48",null));
            add(new Option("FILM PERSONALITIES - RADIO / TV AND FILM INDUSTRY (ACTORS / ARTISTS / PRODUCERS / DIRECTORS) / FILM DISTRIBUTORS.","49",null));
            add(new Option("Finance companies / Financial Intermediaries","50",null));
            add(new Option("Firms/Companies Dealing In Plantation / Aquaculture / Chit Funds / Nidhis / money lending /Money Exchangers","51",null));
            add(new Option("HOTELS","52",null));
            add(new Option("Builders / Infrastructure Construction / Real Estate Developer","53",null));
            add(new Option("IT AND ITES","54",null));
            add(new Option("JOURNALISTS","55",null));
            add(new Option("LAWYER/TAX CONSULTANTS","56",null));
            add(new Option("LOTTERY BUSINESS / ANY GAMBLING BUSINESS","57",null));
            add(new Option("MANAGEMENT CONSULTANTS -NON PROFESSIONAL CONSULTANTS","58",null));
            add(new Option("MANAGEMENT CONSULTANTS -PROFESSIONAL CONSULTANTS","59",null));
            add(new Option("MANPOWER CONSULTANTS / PLACEMENT AGENCIES / LABOUR CONTRACTORS/ SECURITY AGENCY","60",null));
            add(new Option("MEDICAL AND HEALTH SERVICES","61",null));
            add(new Option("MNC COURIER COMPANIES","62",null));
            add(new Option("MOTOR TRAINING SCHOOLS","63",null));
            add(new Option("MULTI- LEVEL / NETWORK MARKETING BUSINESS","64",null));
            add(new Option("NBFC","65",null));
            add(new Option("NON - MNC COURIER COMPANIES","66",null));
            add(new Option("OIL AND GAS RELATED SERVICES","67",null));
            add(new Option("OTHER SERVICES","68",null));
            add(new Option("Politicians / linked to Politics","69",null));
            add(new Option("POWER - TRANSMISSION","70",null));
            add(new Option("REAL ESTATE AGENTS","71",null));
            add(new Option("Salaried","72",null));
            add(new Option("STOCK/COMMODITY BROKERS","73",null));
            add(new Option("TELECOMMUNICATION AND TELECOM SERVICES","74",null));
            add(new Option("TRANSPORT OPERATORS","75",null));
            add(new Option("PRIVATE MONEY LENDERS /DEALER OF VIRTUAL CURRENCY","76",null));
            add(new Option("Practicing Doctor - Homeopathic/Ayurvedic/Unani","77",null));
            add(new Option("TOBACCO AND TOBACCO PRODUCTS","78",null));
            add(new Option("EDIBLE OILS AND VANASPATI","79",null));
            add(new Option("FOOD PROCESSING","80",null));
            add(new Option("BEVERAGES (EXCLUDING TEA AND COFFEE)","81",null));
            add(new Option("TEA","82",null));
            add(new Option("AUTO ANCILLARIES","83",null));
            add(new Option("AUTOMOBILES-MANUFACTURING","84",null));
            add(new Option("CEMENT AND CEMENT PRODUCTS","85",null));
            add(new Option("CHEMICALS","86",null));
            add(new Option("PAINTS AND VARNISHES","87",null));
            add(new Option("DYES AND PIGMENTS","88",null));
            add(new Option("DRUGS AND PHARMACEUTICALS","89",null));
            add(new Option("ENGINEERING","90",null));
            add(new Option("ELECTRONICS","91",null));
            add(new Option("FERTILIZERS","92",null));
            add(new Option("GEMS AND JEWELLERY","93",null));
            add(new Option("POWER GENERATION","94",null));
            add(new Option("POWER - TRANSMISSION","95",null));
            add(new Option("IRON AND STEEL - MANUFACTURING","96",null));
            add(new Option("OTHER METAL AND METAL PRODUCTS","97",null));
            add(new Option("PETROCHEMICALS","98",null));
            add(new Option("TEXTILES","99",null));
            add(new Option("PAPER AND PAPER PRODUCTS","100",null));
            add(new Option("WOOD AND WOOD PRODUCTS","101",null));
            add(new Option("LEATHER AND LEATHER PRODUCTS","102",null));
            add(new Option("RUBBER AND RUBBER PRODUCTS","103",null));
            add(new Option("ENVIRONMENTALLY UNFRIENDLY PRODUCTS","104",null));
            add(new Option("HATCHERIES","105",null));
            add(new Option("COAL PRODUCT","106",null));
            add(new Option("COFFEE","107",null));
            add(new Option("CONSUMER DURABLES","108",null));
            add(new Option("GLASS AND GLASSWARE","109",null));
            add(new Option("MINING OF IRON ORES","110",null));
            add(new Option("Other Manufacturing","111",null));
            add(new Option("SMALL / MEDIUM SCALE UNITS ENGAGED IN THE MANUFACTURE OF AEROSAL UNITS USING CFC (CHLOROFLUOROCARBONS)","112",null));
            add(new Option("SUGAR MILL","113",null));
            add(new Option("PLASTIC AND PLASTIC PRODUCTS","114",null));
            add(new Option("SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP)","115",null));
            add(new Option("AUTO ANCILLARIES","116",null));
            add(new Option("AUTOMOBILES","117",null));
            add(new Option("BEVERAGES (EXCLUDING TEA AND COFFEE)","118",null));
            add(new Option("CEMENT AND CEMENT PRODUCTS","119",null));
            add(new Option("CHEMICALS","120",null));
            add(new Option("COAL PRODUCT","121",null));
            add(new Option("COFFEE","122",null));
            add(new Option("CONSUMER DURABLES","123",null));
            add(new Option("DRUGS AND PHARMACEUTICALS","124",null));
            add(new Option("DYES AND PIGMENTS","125",null));
            add(new Option("EDIBLE OILS AND VANASPATI","126",null));
            add(new Option("ELECTRONICS","127",null));
            add(new Option("ENGINEERING","128",null));
            add(new Option("ENVIRONMENTALLY UNFRIENDLY PRODUCTS/OFFENSIVE/WARFARE PRODUCTS","129",null));
            add(new Option("FERTILIZERS","130",null));
            add(new Option("FOOD PROCESSING","131",null));
            add(new Option("GEMS AND JEWELLERY","132",null));
            add(new Option("GLASS AND GLASSWARE","133",null));
            add(new Option("HATCHERIES","134",null));
            add(new Option("IRON AND STEEL - MANUFACTURING","135",null));
            add(new Option("LEATHER AND LEATHER PRODUCTS","136",null));
            add(new Option("MINING OF IRON ORES","137",null));
            add(new Option("OTHER METAL AND METAL PRODUCTS","138",null));
            add(new Option("Other Manufacturing","139",null));
            add(new Option("PAINTS AND VARNISHES","140",null));
            add(new Option("PAPER AND PAPER PRODUCTS","141",null));
            add(new Option("PETROCHEMICALS","142",null));
            add(new Option("PLASTIC AND PLASTIC PRODUCTS","143",null));
            add(new Option("POWER GENERATION","144",null));
            add(new Option("RUBBER AND RUBBER PRODUCTS","145",null));
            add(new Option("SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP","146",null));
            add(new Option("SMALL / MEDIUM SCALE UNITS ENGAGED IN THE MANUFACTURE OF AEROSAL UNITS USING CFC (CHLOROFLUOROCARBONS)","147",null));
            add(new Option("SUGAR MILL","148",null));
            add(new Option("TEA","149",null));
            add(new Option("TEXTILES","150",null));
            add(new Option("TOBACCO AND TOBACCO PRODUCTS","151",null));
            add(new Option("WOOD AND WOOD PRODUCTS","152",null));
            add(new Option("PETROCHEMICALS","153",null));
            add(new Option("PLASTIC AND PLASTIC PRODUCTS","154",null));
            add(new Option("RUBBER AND RUBBER PRODUCTS","155",null));
            add(new Option("SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP, ETC).","156",null));
            add(new Option("SUGAR","157",null));
            add(new Option("TOBACCO AND TOBACCO PRODUCTS","158",null));
            add(new Option("TRADE-RETAIL","159",null));
            add(new Option("TRADE-WHOLESALE","160",null));
            add(new Option("WOOD AND WOOD PRODUCTS","161",null));
            add(new Option("ENVIRONMENTALLY UNFRIENDLY PRODUCTS","162",null));
            add(new Option("WASTE/SCRAP MERCHANTS","163",null));
            add(new Option("Silver Bullion / Precious stones/ precious metal dealer / Importer of Gold","164",null));
            add(new Option("ANTIQUE DEALERS","165",null));
            add(new Option("AUTOMOBILES","166",null));
            add(new Option("COAL PRODUCT","167",null));
            add(new Option("COFFEE","168",null));
            add(new Option("CONSUMER DURABLES","169",null));
            add(new Option("GEMS AND JEWELLERY-TRADE","170",null));
            add(new Option("GLASS AND GLASSWARE","171",null));
            add(new Option("IRON AND STEEL-TRADE","172",null));
            add(new Option("LIQUOR/WINE STORES/Standalone Bar","173",null));
            add(new Option("MOBILE PRE-PAID CARD DEALERS","174",null));
            add(new Option("AUTO ANCILLARIES","175",null));
            add(new Option("BEVERAGES (EXCLUDING TEA AND COFFEE)","176",null));
            add(new Option("CEMENT AND CEMENT PRODUCTS","177",null));
            add(new Option("CHEMICALS","178",null));
            add(new Option("DRUGS AND PHARMACEUTICALS","179",null));
            add(new Option("DYES AND PIGMENTS","180",null));
            add(new Option("EDIBLE OILS AND VANASPATI","181",null));
            add(new Option("ELECTRONICS","182",null));
            add(new Option("FERTILIZERS","183",null));
            add(new Option("LEATHER AND LEATHER PRODUCTS","184",null));
            add(new Option("OTHER METAL AND METAL PRODUCTS","185",null));
            add(new Option("PAINTS AND VARNISHES","186",null));
            add(new Option("PAPER AND PAPER PRODUCTS","187",null));
            add(new Option("TEA","188",null));
            add(new Option("TEXTILES","189",null));
            add(new Option("ANTIQUE DEALERS","190",null));
            add(new Option("AUTO ANCILLARIES","191",null));
            add(new Option("AUTOMOBILES","192",null));
            add(new Option("BEVERAGES (EXCLUDING TEA AND COFFEE)","193",null));
            add(new Option("CEMENT AND CEMENT PRODUCTS","194",null));
            add(new Option("CHEMICALS","195",null));
            add(new Option("COAL PRODUCT","196",null));
            add(new Option("COFFEE","197",null));
            add(new Option("CONSUMER DURABLES","198",null));
            add(new Option("DRUGS AND PHARMACEUTICALS","199",null));
            add(new Option("DYES AND PIGMENTS","200",null));
            add(new Option("EDIBLE OILS AND VANASPATI","201",null));
            add(new Option("ELECTRONICS","202",null));
            add(new Option("ENVIRONMENTALLY UNFRIENDLY PRODUCTS/OFFENSIVE/WARFARE PRODUCTS","203",null));
            add(new Option("FERTILIZERS","204",null));
            add(new Option("GEMS AND JEWELLERY-TRADE","205",null));
            add(new Option("GLASS AND GLASSWARE","206",null));
            add(new Option("IRON AND STEEL-TRADE","207",null));
            add(new Option("LEATHER AND LEATHER PRODUCTS","208",null));
            add(new Option("LIQUOR/WINE STORES/Standalone Bar","209",null));
            add(new Option("MOBILE PRE-PAID CARD DEALERS","210",null));
            add(new Option("OTHER METAL AND METAL PRODUCTS","211",null));
            add(new Option("PAINTS AND VARNISHES","212",null));
            add(new Option("PAPER AND PAPER PRODUCTS","213",null));
            add(new Option("PETROCHEMICALS","214",null));
            add(new Option("PLASTIC AND PLASTIC PRODUCTS","215",null));
            add(new Option("RUBBER AND RUBBER PRODUCTS","216",null));
            add(new Option("SEASONAL BUSINESSES (E.G. FIRE CRACKER SHOP","217",null));
            add(new Option("SUGAR","218",null));
            add(new Option("TEA","219",null));
            add(new Option("TEXTILES","220",null));
            add(new Option("TOBACCO AND TOBACCO PRODUCTS","221",null));
            add(new Option("TRADE-RETAIL","222",null));
            add(new Option("TRADE-WHOLESALE","223",null));
            add(new Option("WASTE/SCRAP MERCHANTS","224",null));
            add(new Option("WOOD AND WOOD PRODUCTS","225",null));
            add(new Option("Silver Bullion / Precious stones/ precious metal dealer / Importer of Gold","226",null));
            }};

        public static final String announcementImageURL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/04/2023/076b4c7e-08fd-49df-baf7-ff094987f5d7.png?q={@quality}";
        public static final String announcementImageAspectRatio = "201:32";
        public static final String noteTitle = "Sharing work Details will give you higher loan limits";
        public static final String buttonText = "See your loan offer";
        public static final String monthlyIncomeSubtext = "Enter your monthly salary after the tax deduction";
    }

    public static class PL_OFFER_SCREEN_FORM {
        public static final String announcementImageURL = "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/19/04/2023/d8c2cc3d-d68e-48a1-b8e6-1da00e5f1c20.png?q={@quality}";
        public static final String announcementImageAspectRatio = "160:24";
        public static final String loanAmountTitle = "%sHow much do you need?";
        public static final String loanEmiTitle = "How would you like to repay?";
        public static final String loanEmiSubTitle = "%s of interest rate applicable on all tenures";
        public static final String loanEmiNote = "No additional charges are levied for processing this loan";
        public static final String kfsButtonText = "See loan summary";
        public static final String buttonText = "Confirm & Continue";
        public static final BigDecimal flipkartChargesValue = new BigDecimal("0.00");
        public static final BigDecimal flipkartChargesGST = new BigDecimal("0.00");
        public static final String OFFER_CONSENT = "consentSubmitOffer";
    }

    public static class PL_BASIC_DETAILS_SCREEN_CONSENTS {
        public static final Map<String, String> merchantSandBoxConsentMap = new HashMap<>();
        public static final Map<String, String> merchantIDFCConsentMap = new HashMap<>();
        public static final Map<String, Map<String, ConsentObject>> consentIdSandBoxMap = new HashMap<>();
        public static final Map<String, ConsentObject> consentIdFkMap = new HashMap<>();
        public static final Map<String, ConsentObject> consentIdSmMap = new HashMap<>();
        public static final Map<String, ConsentObject> consentIdIdfcMap = new HashMap<>();
        private static final String flipkartConsent = "I hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source (\\\"Personal Data\\\") during loan journey on the platform/application and share Personal Data with <a href='https://super.money/lending-partners'>SIPL's Partners</a> to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application (\\\"Purpose\\\"). I further consent to and authorize the <a href='https://super.money/lending-partners'>SIPL's Partners</a> to further share my Personal Data with their service providers and\\/or third parties for the Purpose including Tele-calling, SMS or WhatsApp. I also understand and authorize the <a href='https://super.money/lending-partners'>SIPL's Partners</a> to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies (Read Experian Specific terms <a href='https://super.money/scapic-compliance-tnc/scapic-tnc'>here</a>, to evaluate my credit worthiness\\/eligibility\\/identity for financial services and products such as personal loans, credit cards, buy now pay later \\/or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the <a href='https://super.money/scapic-compliance-tnc/scapic-tnc'>terms and conditions</a> and <a href='https://super.money/scapic-compliance-tnc/scapic-privacy-policy'>privacy policy</a> of Scapic Innovations Pvt. Ltd.";
        private static final String superMoneyConsent = "I hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source (\\\"Personal Data\\\") during loan journey on the platform\\/application and share Personal Data with [Lending Partners](https://super.money/sumo-3p/pl/static-pages/static-nl-scapic-lp-tnc) to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application (\\\"Purpose\\\"). I further consent to and authorize the [Lending Partners](https://super.money/sumo-3p/pl/static-pages/static-nl-scapic-lp-tnc) to further share my Personal Data with their service providers and\\/or third parties for the Purpose. I also understand and authorize the [Lending Partners](https://super.money/sumo-3p/pl/static-pages/static-nl-scapic-lp-tnc) to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies [(Read Experian Specific terms here)](https://super.money/sumo-3p/pl/static-pages/static-nl-experian-new-tnc) to evaluate my credit worthiness\\/eligibility\\/identity for financial services and products such as personal loans, credit cards, buy now pay later \\/or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the [terms and conditions](https://super.money/sumo-3p/pl/static-pages/static-nl-scapic-tnc) and [privacy policy](https://super.money/sumo-3p/pl/static-pages/static-nl-scapic-privacy-policy) of Scapic Innovations Pvt. Ltd.";
        private static final String idfcConsent = "By continuing I consent to initiate KYC and NSDL verification, and sharing my credit bureau details with our lending partner";
        private static final String nonPEPConsent = "I confirm that I am a major (above 18 years of age), resident of India and am currently residing in India. I also confirm that I am not a tax resident of any other country and am not a politically exposed person";

        private static final String moneyviewCKYCConsent = "I authorise <a href='https://www.flipkart.com/pages/scapic-lp-tnc'>SIPL’s Partners</a> to fetch my records from CKYCR (CERSAI) for processing of my loan application.";
        private static final String mfiConsent = "I confirm that my household income is above INR 3,00,000 per annum.";

        static {
            consentIdFkMap.put("001", new ConsentObject("001", ConsentFor.BUREAU_PULL, flipkartConsent));
            consentIdSmMap.put("001", new ConsentObject("001", ConsentFor.BUREAU_PULL, superMoneyConsent));
            consentIdIdfcMap.put("001", new ConsentObject("001", ConsentFor.BUREAU_PULL, idfcConsent));
            consentIdFkMap.put("002", new ConsentObject("002", ConsentFor.NON_PEP, nonPEPConsent));
            consentIdFkMap.put("003", new ConsentObject("003", ConsentFor.MFI, mfiConsent));
            consentIdFkMap.put("004", new ConsentObject("004", ConsentFor.CKYC, moneyviewCKYCConsent));
            consentIdSandBoxMap.put(MerchantUser.MerchantKeys.FLIPKART_MERCHANT_KEY, consentIdFkMap);
            consentIdSandBoxMap.put(MerchantUser.MerchantKeys.SHOPSY_MERCHANT_KEY, consentIdFkMap);
            consentIdSandBoxMap.put(MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY, consentIdFkMap);
            merchantSandBoxConsentMap.put(MerchantUser.MerchantKeys.FLIPKART_MERCHANT_KEY, flipkartConsent);
            merchantSandBoxConsentMap.put(MerchantUser.MerchantKeys.SHOPSY_MERCHANT_KEY, flipkartConsent);
            merchantSandBoxConsentMap.put(MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY, superMoneyConsent);
            merchantIDFCConsentMap.put(MerchantUser.MerchantKeys.FLIPKART_MERCHANT_KEY, idfcConsent);
            merchantIDFCConsentMap.put(MerchantUser.MerchantKeys.SHOPSY_MERCHANT_KEY, idfcConsent);
            merchantIDFCConsentMap.put(MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY, idfcConsent);
        }
    }

    public static class ActionHandlerV2Constants{
        public static final String CHECK_SCORE_DISPLAY = "/credit-score/display";
        public static final String CHECK_SCORE_INSIGHTS ="/credit-score/insights";
        public static final String NO_CHECK_SCORE_DISPLAY = "/credit-score/no-score-display";
        public static final String SHOW_CHECK_SCORE_INSIGHTS = "showCheckScoreInsights";
        public static final String NO_LENDER_ALLOCATED = "/ams/v1/unallocated";
    }

}
