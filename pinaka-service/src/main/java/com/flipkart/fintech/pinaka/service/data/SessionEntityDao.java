package com.flipkart.fintech.pinaka.service.data;

import com.flipkart.fintech.pinaka.api.enums.SessionType;
import com.flipkart.fintech.pinaka.service.data.model.SessionEntity;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 07/11/17.
 */
public interface SessionEntityDao {
    SessionEntity saveOrUpdate(SessionEntity entity);
    List<SessionEntity> getByTypeAndRefId(SessionType type, String id);
}
