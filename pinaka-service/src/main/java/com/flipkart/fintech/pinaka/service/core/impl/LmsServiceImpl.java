package com.flipkart.fintech.pinaka.service.core.impl;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.service.core.LmsService;
import com.flipkart.fintech.pinaka.service.data.impl.DisbursalsDao;
import com.flipkart.fintech.pinaka.service.data.model.DisbursalsEntity;
import com.google.inject.Inject;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


public class LmsServiceImpl implements LmsService {

    private final DisbursalsDao disbursalsDao;

    @Inject
    public LmsServiceImpl(DisbursalsDao disbursalsDao) {
        this.disbursalsDao = disbursalsDao;
    }

    @Override
    public int getDaysPastDisbursal(String applicationId) {
        Optional<DisbursalsEntity> disbursal = disbursalsDao.getByApplicationId(applicationId);
        return disbursal.map(disbursalsEntity -> (int) TimeUnit.DAYS.convert(new Date().getTime() -
                disbursalsEntity.getDisbursalDate().getTime(), TimeUnit.MILLISECONDS)).orElse(-1);
    }

    @Override
    public List<Lender> getDisbursedLenders(String smUserId){
        return disbursalsDao.getBySmUserId(smUserId).stream().map(entity -> Lender.valueOf(entity.getLender())).collect(Collectors.toList());
    }
}
