package com.flipkart.fintech.pinaka.service.utils.v6;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.fintech.pinaka.service.widgettransformer.ConsentObject;
import com.flipkart.kloud.config.DynamicBucket;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.PinakaConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 04/01/24
 */
@AllArgsConstructor
@Getter
public class FormConfig {
    private static final String CONSENT = "consent";
    private static final String CONSENT_ID = "consentId";

    private static final String CONSENT_MFI = "consentMFI";
    private static final String CONSENT_ID_MFI = "consentIdMFI";
    private static final String CONSENT_FOR_MFI = "consentForMFI";
    private static final String CONSENT_CKYC = "consentCKYC";
    private static final String CONSENT_ID_CKYC = "consentIdCKYC";
    private static final String CONSENT_FOR_CKYC = "consentForCKYC";
    private static final String CONSENT_NONPEP = "consentNonPEP";
    private static final String CONSENT_ID_NONPEP = "consentIdNonPEP";
    private static final String CONSENT_FOR_NONPEP = "consentForNonPEP";

    private static final String CONSENT_FOR = "consentFor";
    private final String minAge;
    private final String maxAge;
    @JsonIgnore
    private final Map<String, String> consentMap;
    @JsonIgnore
    private final Map<String, Map<String, ConsentObject>> consentIdMap;

    public Map<String, Object> getFormConfigMap(String merchant, DynamicBucket dynamicBucket) {
        Map<String, Object> formConfigMap = ObjectMapperUtil.get().convertValue(this, new TypeReference<Map<String, Object>>() {
        });

        formConfigMap.put(CONSENT, consentMap.get(merchant));
        formConfigMap.put(CONSENT_ID, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_PAN_PAGE)).getConsentId());
        formConfigMap.put(CONSENT_FOR, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_PAN_PAGE)).getConsentFor());
        return formConfigMap;
    }

    public Map<String, Object> getFormConfigMapForPage3(String merchant, DynamicBucket dynamicBucket) {
        Map<String, Object> formConfigMap = ObjectMapperUtil.get().convertValue(this, new TypeReference<Map<String, Object>>() {
        });

        formConfigMap.put(CONSENT_MFI, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_MFI)).getConsentText());
        formConfigMap.put(CONSENT_ID_MFI, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_MFI)).getConsentId());
        formConfigMap.put(CONSENT_FOR_MFI, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_MFI)).getConsentFor());

        formConfigMap.put(CONSENT_CKYC, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_CKCY)).getConsentText());
        formConfigMap.put(CONSENT_ID_CKYC, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_CKCY)).getConsentId());
        formConfigMap.put(CONSENT_FOR_CKYC, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_CKCY)).getConsentFor());

        formConfigMap.put(CONSENT_NONPEP, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_NONPEP)).getConsentText());
        formConfigMap.put(CONSENT_ID_NONPEP, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_NONPEP)).getConsentId());
        formConfigMap.put(CONSENT_FOR_NONPEP, consentIdMap.get(merchant).get(dynamicBucket.getString(PinakaConstants.CONSENT_ID_EMP_NONPEP)).getConsentFor());


        return formConfigMap;
    }
}
