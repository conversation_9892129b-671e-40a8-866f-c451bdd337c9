package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class LiveSelfiePageDataSourceResponse {
    private String applicationId;
    private AuthTokenResponse authTokenResponse;
    private EncryptionData encryptionData;
    private Map<String, Object> queryParams;

}