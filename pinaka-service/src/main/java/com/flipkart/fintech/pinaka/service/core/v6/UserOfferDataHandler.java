package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.response.v6.UserOfferDataResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.UserOfferDataHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.InvalidOfferDataException;
import com.google.inject.ImplementedBy;

@ImplementedBy(UserOfferDataHandlerImpl.class)
public interface UserOfferDataHandler {
    UserOfferDataResponse getUserOfferData(String applicationId, String smUserId, String lender) throws InvalidOfferDataException;
}