package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.CustomLog;
import org.apache.http.HttpStatus;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/")
public class LocationResource {

    private final LocationRequestHandler locationRequestHandler;

    @Inject
    public LocationResource(LocationRequestHandler locationRequestHandler) {
        this.locationRequestHandler = locationRequestHandler;
    }

    @GET
    @ApiOperation(value = "Check pincode serviceability", response = PincodeDetailsResponse.class)
    @Timed
    @ExceptionMetered
    @Path("pincode/existence")
    public PincodeDetailsResponse checkPincodeExistence(@QueryParam("pincode") String pincode,
                                                        @QueryParam("value") String value) throws PinakaException {

        pincode = value == null ? pincode : value; //value param is a generic field.

        if (pincode.length() != 6) {
            throw new PinakaWebAppException(Response.Status.BAD_REQUEST, "Pincode length isn't 6 characters");
        }

        return locationRequestHandler.checkPincodeExistence(pincode);
    }


    @Path("/address/{account_id}")
    @GET
    @ApiOperation(value = "API to get most used shipping address")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_NO_CONTENT, message = "NO content")
    })
    @Timed
    @ExceptionMetered
    public Response getUserAddress(
            @HeaderParam("X-Merchant-Id") String merchantId,
            @HeaderParam("X-Request-Id") String requestId,
            @NotNull @PathParam("account_id") String accountId,
            @NotNull @HeaderParam("sm_user_id") String smUserId
    ) {
        try {
            return Response.status(Response.Status.OK).entity(locationRequestHandler.getUserAddress(accountId, merchantId, smUserId, requestId)).build();
        } catch (Exception e) {
            return Response.status(Response.Status.OK).entity(new AddressDetailResponse()).build();
        }
    }
}
