package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.BureauActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.page.RetryWithoutEditBehaviour;
import com.flipkart.fintech.pinaka.service.core.v6.ActionHandlerV2;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.util.Map;
import java.util.Objects;

@CustomLog
public class ActionHandlerV2Impl implements ActionHandlerV2 {
    private final ProfileClient profileClient;
    private final DynamicBucket dynamicBucket;

    @Inject
    public ActionHandlerV2Impl(ProfileClient profileClient, DynamicBucket dynamicBucket) {
        this.profileClient = profileClient;
        this.dynamicBucket = dynamicBucket;
    }

    @Override
    public PageActionResponse submit(FormSubmitRequestV2 submitRequest, String requestId, String userAgent) throws PinakaException {
        try {
            UserRequestActionType type = submitRequest.getType();
            if(UserRequestActionType.BUREAUFORM.equals(type)) {
                BureauDataRequest bureauDataRequest = buildBureauDataRequest((BureauActionRequest) submitRequest);
                BureauDataResponse bureauDataResponse = profileClient.getCreditScore(bureauDataRequest);

                if (!Objects.isNull(bureauDataResponse) && Objects.nonNull(bureauDataResponse.getError())) {
                    return PageActionResponse.builder().action(Action.builder().actionType(ActionType.NAVIGATION).url(PageConstant.ActionHandlerV2Constants.NO_CHECK_SCORE_DISPLAY).build()).actionSuccess(true).error(null).params(null).build();
                }
                String creditScoreUrl = PageConstant.ActionHandlerV2Constants.CHECK_SCORE_DISPLAY;
                if(dynamicBucket.getBoolean(PageConstant.ActionHandlerV2Constants.SHOW_CHECK_SCORE_INSIGHTS)){
                    creditScoreUrl = PageConstant.ActionHandlerV2Constants.CHECK_SCORE_INSIGHTS;
                }
                return PageActionResponse.builder().action(Action.builder().actionType(ActionType.NAVIGATION).url(creditScoreUrl).build()).actionSuccess(true).error(null).params(null).build();
            }
            else{
                log.error("Form Submit not handled for userAction type : {}",type);
                return RetryWithoutEditBehaviour.getPageActionResponse();
            }
        } catch (Exception e) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(ActionHandlerV2Impl.class, "getBureauData", "failed")).mark();
            log.error("Couldn't do submit for bureau score : {}", e.getMessage());
            throw new RuntimeException(e);
        }

    }


    private BureauDataRequest buildBureauDataRequest(BureauActionRequest submitRequest) {
        BureauDataRequest bureauDataRequest = new BureauDataRequest();

        Map<String, Object> formData = submitRequest.getFormData();
        bureauDataRequest.setMerchantUserId(submitRequest.getMerchantAccountId());
        bureauDataRequest.setAccountId(submitRequest.getMerchantAccountId());
        bureauDataRequest.setSmUserId(submitRequest.getSmAccountId());
        bureauDataRequest.setFirstName((String) formData.get("firstName"));
        bureauDataRequest.setLastName((String) formData.get("lastName"));
        bureauDataRequest.setMobileNum((String) formData.get("phoneNumber"));
        bureauDataRequest.setDob((String) formData.get("dob"));
        bureauDataRequest.setPan((String) formData.get("pan"));
        bureauDataRequest.setEmailId((String) formData.get("email"));

        Map<String, Object> consentData = submitRequest.getConsentData();
        Consent consent = new Consent();
        consent.setTs(Long.parseLong(consentData.get("currentTimeStamp").toString()));
        consent.setIp((String) consentData.get("userIP"));
        consent.setPurpose("CREDIT_SCORE");
        consent.setProvided(true);
        consent.setDeviceId((String) consentData.get("deviceId"));
        consent.setDeviceInfo((String) consentData.get("deviceInfo"));

        bureauDataRequest.setConsent(consent);
        return bureauDataRequest;
    }
}
