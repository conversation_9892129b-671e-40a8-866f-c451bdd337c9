package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
public class BasicDetailsPageDataSourceResponse extends Value {

    private Map<String, Object> queryParams;
    private EncryptionData encryptionData;
    private ProfileDetailedResponse profile;
}
