package com.flipkart.fintech.pinaka.service.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.configuration.requests.ApiRequest;

import com.sumo.bff.models.runtime_get_api.request.RuntimeGetApiResponse;

import com.google.inject.Inject;

import javax.inject.Named;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.CustomLog;

@CustomLog
public class LendingGatewayClient {

    private static final String GET_DATA = "/get-data-api";
    private static final String X_SM_USER_ID = "X-SM-User-Id";

    private final WebTarget webTarget;
    private final ObjectMapper objectMapper;

    @Inject
    public LendingGatewayClient(@Named("lendingGatewayTarget") WebTarget webTarget, ObjectMapper objectMapper) {
        this.webTarget = webTarget;
        this.objectMapper = objectMapper;
    }

    public RuntimeGetApiResponse<PageActionResponse> getData(ApiRequest apiRequest) {
        log.info("calling lending gateway client with request: {}, for user {}", apiRequest.getDataApiName(), apiRequest.getUserRequestContext().getAccountId());

        try {
            String requestStr = objectMapper.writeValueAsString(apiRequest);
            Response response = webTarget
                    .path(GET_DATA)
                    .request()
                    .header(X_SM_USER_ID, apiRequest.getUserRequestContext().getAccountId())
                    .post(Entity.entity(requestStr, MediaType.APPLICATION_JSON));
            if (response.getStatus() == 200) {
                String jsonResponse = response.readEntity(String.class);
                return objectMapper.readValue(jsonResponse,
                        new TypeReference<RuntimeGetApiResponse<PageActionResponse>>() {
                        });
            } else {
                throw new RuntimeException("Got Error In Lending Gateway client" + response.getStatus());
            }

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public RuntimeGetApiResponse<PageActionResponse> postSubmit(ApiRequest apiRequest) {
        try {
            String requestStr = objectMapper.writeValueAsString(apiRequest);
            Response response = webTarget
                    .path(GET_DATA)
                    .request()
                    .header(X_SM_USER_ID, apiRequest.getUserRequestContext().getAccountId())
                    .post(Entity.entity(requestStr, MediaType.APPLICATION_JSON));
            if (response.getStatus() == 200) {
                String jsonResponse = response.readEntity(String.class);
                return objectMapper.readValue(jsonResponse,
                        new TypeReference<RuntimeGetApiResponse<PageActionResponse>>() {
                        });
            } else {
                throw new RuntimeException("Got Error In Lending Gateway client" + response.getStatus());
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}