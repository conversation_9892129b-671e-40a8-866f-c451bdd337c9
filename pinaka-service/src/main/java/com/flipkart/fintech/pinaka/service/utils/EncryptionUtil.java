package com.flipkart.fintech.pinaka.service.utils;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import org.apache.commons.lang.StringUtils;
import com.flipkart.fintech.logger.core.flogger.FintechFlogger;
import com.flipkart.fintech.logger.core.FintechLogger;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class EncryptionUtil {
    private static final FintechFlogger log           = FintechLogger.flogger(EncryptionUtil.class);
    private EncryptionUtil() {

    }
    private static final String CS5_PADDING   = "AES/CBC/PKCS5PADDING";
    private static final String AES_ALGORITHM = "AES";

    public static String encryptWithAes(final String text, final String encryptionKey) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        byte[] key = encryptionKey.getBytes(StandardCharsets.UTF_8);
        try {
            byte[] cipherText = encrypt(key, key, text.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(cipherText);
        } catch (PinakaException e) {
            log.error("Failed to encrypt data");
        }

        return PinakaConstants.EMPTY_STRING;
    }

    public static String decryptAesCbc(final String text, final String encryptionKey) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        byte[] key = encryptionKey.getBytes(StandardCharsets.UTF_8);
        try {
            return new String(decrypt(key, key, Base64.getDecoder().decode(text)));
        } catch (PinakaException e) {
            log.error("Failed to decrypt data");
        }

        return PinakaConstants.EMPTY_STRING;
    }

    private static byte[] decrypt(final byte[] key, final byte[] ivParameterSpec, final byte[] message) throws PinakaException {
        return encryptDecrypt(Cipher.DECRYPT_MODE, key, ivParameterSpec, message);
    }

    private static byte[] encrypt(final byte[] key, final byte[] ivParameterSpec, final byte[] message) throws PinakaException {
        return encryptDecrypt(Cipher.ENCRYPT_MODE, key, ivParameterSpec, message);
    }

    private static byte[] encryptDecrypt(final int mode, final byte[] key, final byte[] ivParameterSpec, final byte[] message) throws PinakaException {
        try {
            final Cipher cipher = Cipher.getInstance(CS5_PADDING);
            final SecretKeySpec keySpec = new SecretKeySpec(key, AES_ALGORITHM);
            final IvParameterSpec ivSpec = new IvParameterSpec(ivParameterSpec);
            cipher.init(mode, keySpec, ivSpec);

            return cipher.doFinal(message);
        } catch (NoSuchAlgorithmException | InvalidKeyException | InvalidAlgorithmParameterException |
                NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException e) {
            throw new PinakaException("Failed to encrypt/decrypt data", e);
        }
    }



    @Timed
    @ExceptionMetered
    public static SecretKey decryptSymmetricKey(String encryptedSymmetricKey, String privateKeyString) throws PinakaException {
        PrivateKey privateKey = getPKCS8EncodedRSAPrivateKeyFromString(privateKeyString);
        SecretKey symmetricKey;
        byte[] symmetricKeyBytes;
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            symmetricKeyBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedSymmetricKey.getBytes()));
        } catch (Exception e) {
            throw new PinakaException(e);
        }
        symmetricKey = new SecretKeySpec(symmetricKeyBytes, "AES");
        log.info("Decryption successful");
        return symmetricKey;
    }

    private static PrivateKey getPKCS8EncodedRSAPrivateKeyFromString(String privateKey) throws PinakaException {
        PrivateKey prvKey = null;
        try {
            byte[] decodeByte = Base64.getDecoder().decode(privateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodeByte);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            prvKey = keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("Exception converting privateKey {}", e);
            throw new PinakaException(e);
        }
        return prvKey;
    }

}
