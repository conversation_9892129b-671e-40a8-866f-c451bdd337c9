package com.flipkart.fintech.pinaka.service.configuration.requests;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class Payload {
    private String userId;
    private String source;
    private String merchantId;
    private UserActionRequest userActionRequest;
}