package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.lead.model.Name;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.model.PaOffer;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;

import com.flipkart.sm.pages.dp.constants.Constants;
import lombok.CustomLog;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.ENABLE_LEAD_V4_FLOW;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_WHITELISTED_USERS;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_TRAFFIC_PERCENTAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_ENABLED_PLATFORMS;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_ANDROID_TRAFFIC_PERCENTAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_IOS_TRAFFIC_PERCENTAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V4_WEB_TRAFFIC_PERCENTAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.USER_NAME;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.USER_PHONE_NUMBER;
import static com.flipkart.fintech.pinaka.service.helper.UserActionSubmitRequestHelper.FULL_NAME;
import static com.flipkart.fintech.pinaka.service.utils.PlatformDetectionUtil.PLATFORM_ANDROID;
import static com.flipkart.fintech.pinaka.service.utils.PlatformDetectionUtil.PLATFORM_IOS;
import static com.flipkart.fintech.pinaka.service.utils.PlatformDetectionUtil.PLATFORM_WEB;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.NameValidationConstants.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.ADDRESSES_STRING;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.HOUSE_NUMBER_STRING;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.PINCODE_DETAILS_STRING;

import org.apache.commons.text.WordUtils;

@CustomLog
public class LV4Util {

    private final static Random RANDOM = new Random();

    private LV4Util() {
        throw new IllegalStateException("Utility class");
    }

    private static final long MIN_OFFER_AMOUNT = 1_00_000L; // Minimum offer amount to consider as valid
    private static final long MAX_OFFER_AMOUNT = 10_00_000L; // Maximum offer amount to consider as valid

    // @COMMENT: Add constants for better maintainability
    private static final String LEAD_V4_IDENTIFIER = "LEAD_V4";
    private static final String LEAD_V4_KEY_PREFIX = "leadV4";

    // Cached user data constants
    public static final String CACHED_USER_DATA_KEY = "cachedUserData";
    public static final String CACHED_REVIEW_DATA_KEY = "reviewUserDataSourceResponse";
    public static final String CACHED_LEAD_PAGE_DATA_KEY = "leadPageDataSourceResponse";
    public static final String CACHED_IS_ENCRYPTED_KEY = "isEncrypted";

    /**
     * Checks if LV4 is enabled for a user based on feature flag, whitelist, traffic percentage, and platform
     * @param dynamicBucket Dynamic configuration bucket
     * @param smUserId User ID to check
     * @param platform Platform string (android/ios/web)
     * @return true if LV4 is enabled for the user and platform, false otherwise
     */
    public static boolean isLv4Enabled(DynamicBucket dynamicBucket, String smUserId, String platform) {
        if (dynamicBucket == null || StringUtils.isBlank(smUserId)) {
            log.warn("Invalid input parameters for LV4 enablement check");
            return false;
        }

        if (Boolean.FALSE.equals(dynamicBucket.getBoolean(ENABLE_LEAD_V4_FLOW))) {
            return false;
        }

        // Check platform enablement first
        List<String> enabledPlatforms = dynamicBucket.getStringArray(LEAD_V4_ENABLED_PLATFORMS);
        if (enabledPlatforms != null && !enabledPlatforms.isEmpty() && !enabledPlatforms.contains(platform)) {
            log.debug("LV4 not enabled for platform: {} for userId: {}", platform, smUserId);
            return false;
        }

        // Check whitelist first
        List<String> whitelistedUsers = dynamicBucket.getStringArray(LEAD_V4_WHITELISTED_USERS);
        if (whitelistedUsers != null && whitelistedUsers.contains(smUserId)) {
            return true;
        }

        // Platform-specific percentage-based routing
        int percentage = getPlatformTrafficPercentage(dynamicBucket, platform);
        return RANDOM.nextInt(100) < percentage;
    }

    public static boolean isLv4Application(ApplicationDataResponse applicationDataResponse) {
        if(applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
            return false;
        }
        if (StringUtils.isNotBlank(applicationDataResponse.getApplicationState()) &&
            applicationDataResponse.getApplicationState().contains(LEAD_V4_IDENTIFIER)) {
            return true;
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        return isLv4Application(applicationData);
    }

    public static boolean isLv4Application(Map<String, Object> applicationData) {
        if (applicationData == null || applicationData.isEmpty()) {
            return false;
        }

        // @COMMENT: Use stream API for more efficient key searching
        return applicationData.keySet().stream()
                .anyMatch(key -> key.contains(LEAD_V4_KEY_PREFIX));
    }

    public static ApprovedAmount getOfferAmount(ApplicationDataResponse applicationDataResponse, PaOffer paOffer) {
        if (applicationDataResponse == null) {
            log.warn("ApplicationDataResponse is null, using default offer amount");
            return new ApprovedAmount(true, MAX_OFFER_AMOUNT);
        }

        boolean appendUpto = true;
        long amount;
        if (paOffer == null || paOffer.getAmount() == null) {
            log.debug("PA offer is null or amount is not set, using maximum offer amount for userId {}", applicationDataResponse.getSmUserId());
            amount = MAX_OFFER_AMOUNT;
        } else if (paOffer.getAmount() < MIN_OFFER_AMOUNT) {
            log.debug("PA offer amount {} is less than minimum threshold, setting to minimum offer amount for userId {}", paOffer.getAmount(), applicationDataResponse.getSmUserId());
            amount = MIN_OFFER_AMOUNT;
        } else {
            appendUpto = false;
            amount = paOffer.getAmount();
        }
        return new ApprovedAmount(appendUpto, amount);
    }

    static String format(String pattern, Object value) {
        return new DecimalFormat(pattern).format(value);
    }

    public static String formatNumber(double value) {
        String result;
        if (value < 1000) {
            result = format(Constants.UNDER_THOUSAND_INTEGER_PATTERN, value);
        } else {
            double hundreds = value % 1000;
            int other = (int) (value / 1000);
            result = format(",##", other) + ',' + format(Constants.OVER_THOUSAND_INTEGER_FLOW, hundreds);
        }
        return result;
    }

    /**
     * Gets the traffic percentage for a specific platform
     * @param dynamicBucket Dynamic configuration bucket
     * @param platform Platform string (android/ios/web)
     * @return Traffic percentage for the platform, falls back to general percentage if platform-specific not found
     */
    private static int getPlatformTrafficPercentage(DynamicBucket dynamicBucket, String platform) {
        if (platform == null) {
            return dynamicBucket.getInt(LEAD_V4_TRAFFIC_PERCENTAGE);
        }

        String platformSpecificKey;
        switch (platform) {
            case PLATFORM_ANDROID:
                platformSpecificKey = LEAD_V4_ANDROID_TRAFFIC_PERCENTAGE;
                break;
            case PLATFORM_IOS:
                platformSpecificKey = LEAD_V4_IOS_TRAFFIC_PERCENTAGE;
                break;
            case PLATFORM_WEB:
                platformSpecificKey = LEAD_V4_WEB_TRAFFIC_PERCENTAGE;
                break;
            default:
                log.debug("Unknown platform: {}, using general traffic percentage", platform);
                return dynamicBucket.getInt(LEAD_V4_TRAFFIC_PERCENTAGE);
        }

        // Try to get platform-specific percentage, fall back to general percentage if not configured
        Integer platformPercentage = dynamicBucket.getInt(platformSpecificKey);
        if (platformPercentage == null) {
            log.debug("Platform-specific traffic percentage not configured for {}, using general percentage", platform);
            return dynamicBucket.getInt(LEAD_V4_TRAFFIC_PERCENTAGE);
        }

        return platformPercentage;
    }

    // Name validation utility methods

    /**
     * Check if the given name is a title (Mr, Mrs, Dr, etc.)
     */
    public static boolean isTitle(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return TITLE_PREFIXES.contains(name.toLowerCase().trim());
    }

    /**
     * Check if the name contains special characters (only letters and spaces are allowed)
     */
    public static boolean hasSpecialCharacters(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return SPECIAL_CHARACTERS_PATTERN.matcher(name).find();
    }

    /**
     * Check if the name length is within valid range
     */
    public static boolean isValidNameLength(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        int length = name.trim().length();
        return length >= MIN_NAME_LENGTH && length <= MAX_NAME_LENGTH;
    }

    /**
     * Check if name should be considered empty based on validation rules:
     * 1. Is a title (Ms/Mr/Mrs/Dr/Er/Ar etc.)
     * 2. More than 26 chars
     * 3. Less than 3 chars
     * 4. Contains special characters
     */
    public static boolean isNameEmpty(String name) {
        if (name == null || name.trim().isEmpty()) {
            return true;
        }

        String trimmedName = name.trim();

        // Check if it's a title
        if (isTitle(trimmedName)) {
            return true;
        }

        // Check length constraints
        if (!isValidNameLength(trimmedName)) {
            return true;
        }

        // Check for special characters
        if (hasSpecialCharacters(trimmedName)) {
            return true;
        }

        return false;
    }

    /**
     * Removes titles from the beginning of a full name string.
     * This method only removes titles that appear at the start of the name.
     * Titles that appear in the middle or end are preserved as they might be part of the actual name.
     *
     * @param fullName The full name string to process
     * @return The full name with leading titles removed, properly capitalized
     */
    public static String removeTitlesFromFullName(String fullName) {
        if (StringUtils.isBlank(fullName)) {
            return "";
        }

        String normalized = StringUtils.normalizeSpace(fullName);
        String[] words = normalized.split("\\s+");

        if (words.length == 0) {
            return "";
        }

        // Find the first non-title word
        int startIndex = 0;
        for (int i = 0; i < words.length; i++) {
            if (!isTitle(words[i])) {
                startIndex = i;
                break;
            }
            // If we've gone through all words and they're all titles, return empty
            if (i == words.length - 1) {
                return "";
            }
        }

        // Build result from first non-title word onwards
        StringBuilder result = new StringBuilder();
        for (int i = startIndex; i < words.length; i++) {
            if (result.length() > 0) {
                result.append(" ");
            }
            result.append(WordUtils.capitalizeFully(words[i]));
        }

        return result.toString();
    }

    /**
     * Validates if a name is suitable for prefill according to PA-PL journey criteria.
     * Name should be prefilled ONLY if it meets ALL these criteria:
     * 1. No special characters
     * 2. No numbers
     * 3. No repetition of same character more than 2 times
     * 4. Min 3 characters
     * 5. Max 26 characters
     *
     * This method validates both the full name and individual name components.
     *
     * @param name The name to validate
     * @return true if name is valid for prefill, false otherwise
     */
    public static boolean isValidNameForPrefill(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }

        String trimmedName = name.trim();

        // Check overall length constraints (3-26 characters)
        if (trimmedName.length() < MIN_NAME_LENGTH || trimmedName.length() > MAX_NAME_LENGTH) {
            return false;
        }

        // Check for numbers and special characters (only letters and spaces allowed)
        if (SPECIAL_CHARACTERS_PATTERN.matcher(trimmedName).find()) {
            return false;
        }

        // Check for repetition of same character more than 2 times consecutively
        if (hasConsecutiveCharacterRepetition(trimmedName)) {
            return false;
        }

        // Additional check: validate individual name components
        // Each word in the name should also meet minimum length requirements
        String[] words = trimmedName.split("\\s+");
        for (String word : words) {
            if (StringUtils.isNotBlank(word) && word.trim().length() < MIN_NAME_LENGTH) {
                return false; // Individual word is too short
            }
        }

        return true;
    }

    /**
     * Checks if a string has the same character repeated more than 2 times consecutively
     *
     * @param text The text to check
     * @return true if there are more than 2 consecutive identical characters
     */
    private static boolean hasConsecutiveCharacterRepetition(String text) {
        if (text == null || text.length() < 3) {
            return false;
        }

        for (int i = 0; i < text.length() - 2; i++) {
            char currentChar = text.charAt(i);
            if (currentChar == text.charAt(i + 1) && currentChar == text.charAt(i + 2)) {
                return true; // Found 3 consecutive identical characters
            }
        }

        return false;
    }

    /**
     * Gets the full name from profile with decryption, title removal, and validation.
     * This method combines first and last name, decrypts them if needed, removes titles,
     * and validates the result according to PA-PL journey criteria.
     *
     * @param profile The profile containing first and last name
     * @param isNameEncrypted Whether the names are encrypted
     * @param decrypter The decrypter to use for decryption
     * @return The processed full name in uppercase, or empty string if invalid
     */
    public static String getFullNameFromProfile(ProfileDetailedResponse profile, Boolean isNameEncrypted, Decrypter decrypter) {
        String firstName = profile.getFirstName();
        String lastName = profile.getLastName();

        // Decrypt names if they are encrypted
        if (Boolean.TRUE.equals(isNameEncrypted)) {
            if (StringUtils.isNotBlank(firstName)) {
                firstName = decrypter.decryptString(firstName);
            }
            if (StringUtils.isNotBlank(lastName)) {
                lastName = decrypter.decryptString(lastName);
            }
        }

        // Combine first and last name
        String combinedName = getFullName(firstName, lastName).toString();

        // First strip titles from the constructed full name using LV4Util
        String nameWithoutTitles = removeTitlesFromFullName(combinedName);

        // Then apply PA-PL journey validation criteria - only prefill if the remaining name is valid
        if (isValidNameForPrefill(nameWithoutTitles)) {
            return nameWithoutTitles.toUpperCase();
        }

        // Return empty string if name doesn't meet PA-PL journey criteria after title removal
        return "";
    }

    /**
     * Retrieves and decrypts the full name from applicationDataResponse.getApplicationData().get(USER_NAME).
     * The stored name is encrypted and needs decryption before processing.
     *
     * @param applicationDataResponse The application data response containing the encrypted name
     * @param decrypter The decrypter instance to decrypt the name
     * @return The decrypted and processed full name, or empty string if not available or invalid
     */
    public static String getDecryptedFullNameFromApplicationData(ApplicationDataResponse applicationDataResponse, Decrypter decrypter) {
        if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null || decrypter == null) {
            log.warn("Invalid input parameters for name decryption");
            return "";
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        Object userNameObj = applicationData.get(USER_NAME);

        if (userNameObj == null) {
            log.debug("No userName found in application data for userId: {}", applicationDataResponse.getSmUserId());
            return "";
        }

        // The userName is stored as a Name object
        Name name = ObjectMapperUtil.get().convertValue(userNameObj, Name.class);

        if (name.isBlank()) {
            log.debug("Name is marked as blank for userId: {}", applicationDataResponse.getSmUserId());
            return "";
        }

        String firstName = name.getFirstName();
        String lastName = name.getLastName();

        // Decrypt names - they are stored encrypted
        try {
            if (StringUtils.isNotBlank(firstName)) {
                firstName = decrypter.decryptString(firstName);
            }
            if (StringUtils.isNotBlank(lastName)) {
                lastName = decrypter.decryptString(lastName);
            }
        } catch (Exception e) {
            log.error("Error decrypting name for userId: {}", applicationDataResponse.getSmUserId(), e);
            return "";
        }

        // Combine first and last name
        return getFullName(firstName, lastName).toString();
    }

    private static StringBuilder getFullName(String firstName, String lastName) {
        StringBuilder fullName = new StringBuilder();
        if (StringUtils.isNotBlank(firstName)) {
            fullName.append(firstName);
        }
        if (StringUtils.isNotBlank(lastName)) {
            if (fullName.length() > 0) {
                fullName.append(" ");
            }
            fullName.append(lastName);
        }
        return fullName;
    }

    /**
     * Retrieves and decrypts the full name with fallback logic.
     * First tries to get name from applicationDataResponse.getApplicationData().get(USER_NAME).
     * If not available, falls back to getting name from leadPageDataSourceResponse profile.
     *
     * @param applicationDataResponse The application data response containing the encrypted name
     * @param leadPageDataSourceResponse The lead page data source response (fallback)
     * @param decrypter The decrypter instance to decrypt the name
     * @return The decrypted and processed full name, or empty string if not available or invalid
     */
    public static String getDecryptedFullNameWithFallback(ApplicationDataResponse applicationDataResponse,
                                                         LeadPageDataSourceResponse leadPageDataSourceResponse,
                                                         Decrypter decrypter) {
        // First try to get name from application data
        String nameFromAppData = getDecryptedFullNameFromApplicationData(applicationDataResponse, decrypter);
        if (StringUtils.isNotBlank(nameFromAppData)) {
            log.debug("Retrieved name from application data for userId: {}", applicationDataResponse.getSmUserId());
            return nameFromAppData;
        }

        // Fallback to leadPageDataSourceResponse
        if (leadPageDataSourceResponse != null && leadPageDataSourceResponse.getProfile() != null) {
            log.debug("Falling back to leadPageDataSourceResponse for name retrieval for userId: {}", applicationDataResponse.getSmUserId());
            try {
                String nameFromProfile = getFullNameFromProfile(
                    leadPageDataSourceResponse.getProfile(),
                    leadPageDataSourceResponse.getIsNameEncrypted(),
                    decrypter);
                if (StringUtils.isNotBlank(nameFromProfile)) {
                    return nameFromProfile;
                }
            } catch (Exception e) {
                log.error("Error retrieving name from profile fallback for userId: {}", applicationDataResponse.getSmUserId(), e);
            }
        }

        log.debug("No name found in application data or profile fallback for userId: {}", applicationDataResponse.getSmUserId());
        return "";
    }

    /**
     * Retrieves the phone number from applicationDataResponse.getApplicationData().get(USER_PHONE_NUMBER).
     * The phone number is stored as a plain string (not encrypted like the name).
     *
     * @param applicationDataResponse The application data response containing the phone number
     * @return The phone number, or empty string if not available
     */
    public static String getPhoneNumberFromApplicationData(ApplicationDataResponse applicationDataResponse) {
        if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
            log.warn("Invalid input parameters for phone number retrieval");
            return "";
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        Object phoneNumberObj = applicationData.get(USER_PHONE_NUMBER);

        if (phoneNumberObj == null) {
            log.debug("No phone number found in application data for userId: {}", applicationDataResponse.getSmUserId());
            return "";
        }

        try {
            String phoneNumber = phoneNumberObj.toString();
            if (StringUtils.isNotBlank(phoneNumber)) {
                return phoneNumber.trim();
            }
        } catch (Exception e) {
            log.error("Error retrieving phone number for userId: {}", applicationDataResponse.getSmUserId(), e);
        }

        return "";
    }

    /**
     * Retrieves the phone number with fallback logic.
     * First tries to get phone number from applicationDataResponse.getApplicationData().get(USER_PHONE_NUMBER).
     * If not available, falls back to getting phone number from leadPageDataSourceResponse profile.
     *
     * @param applicationDataResponse The application data response containing the phone number
     * @param leadPageDataSourceResponse The lead page data source response (fallback)
     * @return The phone number, or empty string if not available
     */
    public static String getPhoneNumberWithFallback(ApplicationDataResponse applicationDataResponse,
                                                   com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse leadPageDataSourceResponse) {
        // First try to get phone number from application data
        String phoneFromAppData = getPhoneNumberFromApplicationData(applicationDataResponse);
        if (StringUtils.isNotBlank(phoneFromAppData)) {
            log.debug("Retrieved phone number from application data for userId: {}", applicationDataResponse.getSmUserId());
            return phoneFromAppData;
        }

        // Fallback to leadPageDataSourceResponse
        if (leadPageDataSourceResponse != null && leadPageDataSourceResponse.getProfile() != null) {
            log.debug("Falling back to leadPageDataSourceResponse for phone number retrieval for userId: {}", applicationDataResponse.getSmUserId());
            try {
                String phoneFromProfile = leadPageDataSourceResponse.getProfile().getPhoneNo();
                if (StringUtils.isNotBlank(phoneFromProfile)) {
                    return phoneFromProfile.trim();
                }
            } catch (Exception e) {
                log.error("Error retrieving phone number from profile fallback for userId: {}", applicationDataResponse.getSmUserId(), e);
            }
        }

        log.debug("No phone number found in application data or profile fallback for userId: {}", applicationDataResponse.getSmUserId());
        return "";
    }

    /**
     * Check if name is present in application data
     *
     * @param applicationDataResponse The application data response to check
     * @return true if name is present and not blank, false otherwise
     */
    public static boolean hasNameInApplicationData(ApplicationDataResponse applicationDataResponse) {
        if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null || !applicationDataResponse.getApplicationData().containsKey(USER_NAME)) {
            return false;
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        Object userNameObj = applicationData.get(USER_NAME);

        Name name = ObjectMapperUtil.get().convertValue(userNameObj, Name.class);
        return name.hasValidContent();
    }

    /**
     * Check if phone number is present in application data
     *
     * @param applicationDataResponse The application data response to check
     * @return true if phone number is present and not blank, false otherwise
     */
    public static boolean hasPhoneNumberInApplicationData(ApplicationDataResponse applicationDataResponse) {
        if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null || !applicationDataResponse.getApplicationData().containsKey(USER_PHONE_NUMBER)) {
            return false;
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        Object phoneNumberObj = applicationData.get(USER_PHONE_NUMBER);

        return phoneNumberObj != null && StringUtils.isNotBlank(phoneNumberObj.toString());
    }

    /**
     * Gets cached user data from applicationData or falls back to external API calls.
     * This method centralizes the caching logic used across LV3 transformers.
     * External API calls are made ONLY if cached data is not present.
     * If cached data is present but address is missing, fetches bureau data for address information.
     *
     * @param applicationDataResponse The application data response containing cached data
     * @param decrypter The decrypter instance (only used if cache miss)
     * @param locationRequestHandler The location request handler for fallback (only used if cache miss)
     * @param formWidgetDataFetcher The form widget data fetcher for fallback (only used if cache miss)
     * @param bureauDataManager The bureau data manager for fetching address data when missing (optional)
     * @return Map of user data, either from cache or from external API calls
     */
    public static Map<String, Object> getCachedUserDataOrFallback(ApplicationDataResponse applicationDataResponse,
                                                                 Decrypter decrypter,
                                                                 LocationRequestHandler locationRequestHandler,
                                                                 FormWidgetDataFetcher formWidgetDataFetcher,
                                                                 BureauDataManager bureauDataManager) {
        // Try to extract and process cached data first
        Map<String, Object> cachedUserData = extractCachedUserData(applicationDataResponse, decrypter,
                locationRequestHandler, formWidgetDataFetcher, bureauDataManager);

        if (cachedUserData != null) {
            return cachedUserData;
        }

        // Fallback to external API calls if cached data is not available
        return createFallbackUserData(applicationDataResponse, decrypter, locationRequestHandler, formWidgetDataFetcher);
    }

    /**
     * Extracts and processes cached user data from application data response.
     * Returns null if cached data is not available or invalid.
     */
    private static Map<String, Object> extractCachedUserData(ApplicationDataResponse applicationDataResponse,
                                                           Decrypter decrypter,
                                                           LocationRequestHandler locationRequestHandler,
                                                           FormWidgetDataFetcher formWidgetDataFetcher,
                                                           BureauDataManager bureauDataManager) {
        try {
            Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
            if (applicationData == null || !applicationData.containsKey(CACHED_USER_DATA_KEY)) {
                return null;
            }

            Map<String, Object> cachedData = (Map<String, Object>) applicationData.get(CACHED_USER_DATA_KEY);
            if (!isCachedDataValid(cachedData)) {
                if (cachedData != null && cachedData.isEmpty()) {
                    log.warn("Cached data is empty for userId: {}, need to collect information from user",
                            applicationDataResponse.getSmUserId());
                    return new HashMap<>();
                }
                return null;
            }

            return processCachedData(cachedData, applicationDataResponse, decrypter,
                    locationRequestHandler, formWidgetDataFetcher, bureauDataManager);
        } catch (Exception e) {
            log.warn("Failed to retrieve cached user data for userId: {}, falling back to external API calls: {}",
                    applicationDataResponse.getSmUserId(), e.getMessage());
            return null;
        }
    }

    /**
     * Validates if cached data is valid and encrypted.
     */
    private static boolean isCachedDataValid(Map<String, Object> cachedData) {
        return cachedData != null && !cachedData.isEmpty() &&
               Boolean.TRUE.equals(cachedData.get(CACHED_IS_ENCRYPTED_KEY));
    }

    /**
     * Processes valid cached data by extracting review data source response and handling bureau fallback.
     */
    private static Map<String, Object> processCachedData(Map<String, Object> cachedData,
                                                        ApplicationDataResponse applicationDataResponse,
                                                        Decrypter decrypter,
                                                        LocationRequestHandler locationRequestHandler,
                                                        FormWidgetDataFetcher formWidgetDataFetcher,
                                                        BureauDataManager bureauDataManager) {
        // Extract encrypted data source responses from cache
        ReviewUserDataSourceResponse reviewUserDataSourceResponse = ObjectMapperUtil.get()
                .convertValue(cachedData.get(CACHED_REVIEW_DATA_KEY), ReviewUserDataSourceResponse.class);

        if (reviewUserDataSourceResponse == null) {
            return null;
        }

        log.info("Using cached encrypted user data for userId: {}, decrypting on-demand - NO EXTERNAL API CALLS MADE",
                applicationDataResponse.getSmUserId());

        // Use FormWidgetDataFetcher to decrypt and process the cached data
        Set<String> allFields = new HashSet<>();
        Map<String, Object> userData = formWidgetDataFetcher.getDataForFields(allFields,
                reviewUserDataSourceResponse, decrypter, locationRequestHandler);

        // Handle bureau address fallback if needed
        handleBureauAddressFallback(userData, applicationDataResponse, bureauDataManager);

        return userData;
    }

    /**
     * Handles bureau address fallback when address is missing from cached data.
     */
    private static void handleBureauAddressFallback(Map<String, Object> userData,
                                                   ApplicationDataResponse applicationDataResponse,
                                                   BureauDataManager bureauDataManager) {
        if (!isAddressNotPresent(userData) || bureauDataManager == null) {
            return;
        }

        try {
            log.info("Address not present in cached data for userId: {}, fetching from bureau",
                    applicationDataResponse.getSmUserId());

            MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(
                    applicationDataResponse.getExternalUserId(),
                    applicationDataResponse.getSmUserId());
            InitialUserDataResponse bureauResponse = bureauDataManager.initialUserData(merchantUser.getSmUserId());

            if (bureauResponse != null && isBureauResponseNonEmpty(bureauResponse)) {
                log.info("Bureau response is non-empty for userId: {}, updating address data",
                        applicationDataResponse.getSmUserId());
                updateUserDataWithBureauAddress(userData, bureauResponse);
            } else {
                log.info("Bureau response is empty for userId: {}, no address data to update",
                        applicationDataResponse.getSmUserId());
            }
        } catch (Exception e) {
            log.error("Error fetching bureau data for address for userId: {}: {}",
                    applicationDataResponse.getSmUserId(), e.getMessage());
        }
    }

    /**
     * Creates fallback user data using external API calls when cached data is not available.
     */
    private static Map<String, Object> createFallbackUserData(ApplicationDataResponse applicationDataResponse,
                                                             Decrypter decrypter,
                                                             LocationRequestHandler locationRequestHandler,
                                                             FormWidgetDataFetcher formWidgetDataFetcher) {
        log.warn("Cache miss: No cached user data found for userId: {}, creating data sources and making external API calls as fallback",
                applicationDataResponse.getSmUserId());

        try {
            // Create data sources only when needed for fallback
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);

            Set<String> allFields = new HashSet<>();
            return formWidgetDataFetcher.getDataForFields(allFields, reviewUserDataSourceResponse, decrypter, locationRequestHandler);
        } catch (Exception fallbackException) {
            log.error("Failed to create fallback data for userId: {}: {}", applicationDataResponse.getSmUserId(), fallbackException.getMessage());
            return new HashMap<>(); // Return empty map as last resort
        }
    }

    /**
     * Backward compatibility method - calls the main method with null bureauDataManager.
     * Gets cached user data from applicationData or falls back to external API calls.
     * This method centralizes the caching logic used across LV3 transformers.
     * External API calls are made ONLY if cached data is not present.
     *
     * @param applicationDataResponse The application data response containing cached data
     * @param decrypter The decrypter instance (only used if cache miss)
     * @param locationRequestHandler The location request handler for fallback (only used if cache miss)
     * @param formWidgetDataFetcher The form widget data fetcher for fallback (only used if cache miss)
     * @return Map of user data, either from cache or from external API calls
     */
    public static Map<String, Object> getCachedUserDataOrFallback(ApplicationDataResponse applicationDataResponse,
                                                                 Decrypter decrypter,
                                                                 LocationRequestHandler locationRequestHandler,
                                                                 FormWidgetDataFetcher formWidgetDataFetcher) {
        return getCachedUserDataOrFallback(applicationDataResponse, decrypter, locationRequestHandler, formWidgetDataFetcher, null);
    }

    /**
     * Checks if address is not present in the user data.
     * Address is considered not present if all of the following are missing or empty:
     * - HOUSE_NUMBER_STRING (address line 1)
     * - PINCODE_DETAILS_STRING (pincode)
     * - ADDRESSES_STRING (experian addresses)
     */
    private static boolean isAddressNotPresent(Map<String, Object> userData) {
        String addressLineOne = (String) userData.getOrDefault(HOUSE_NUMBER_STRING, "");
        PincodeDetailsResponse pinCode = (PincodeDetailsResponse) userData.getOrDefault(PINCODE_DETAILS_STRING, null);
        List<CAISHolderAddressDetails> addressOptions = (List<CAISHolderAddressDetails>) userData.getOrDefault(ADDRESSES_STRING, Collections.emptyList());

        boolean defaultAddressPresent = StringUtils.isNotBlank(addressLineOne) && pinCode != null && StringUtils.isNotBlank(pinCode.getPincode());
        boolean atLeastOneValidAddressFound = isAtLeastOneValidAddressPresent(addressOptions);

        return !(defaultAddressPresent || atLeastOneValidAddressFound);
    }

    /**
     * Checks if at least one valid address is present in the address options.
     */
    private static boolean isAtLeastOneValidAddressPresent(List<CAISHolderAddressDetails> addressOptions) {
        if (addressOptions == null || addressOptions.isEmpty()) {
            return false;
        }
        for (CAISHolderAddressDetails address : addressOptions) {
            if (StringUtils.isNotBlank(address.getFirstLineOfAddress()) && StringUtils.isNotBlank(address.getZipPostalCodeOfAddress())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the bureau response contains non-empty address data.
     */
    private static boolean isBureauResponseNonEmpty(InitialUserDataResponse bureauResponse) {
        // Check if addressDetailResponse has data
        if (bureauResponse.getAddressDetailResponse() != null) {
            if (StringUtils.isNotBlank(bureauResponse.getAddressDetailResponse().getAddressLine1()) ||
                StringUtils.isNotBlank(bureauResponse.getAddressDetailResponse().getAddressLine2()) ||
                StringUtils.isNotBlank(bureauResponse.getAddressDetailResponse().getPincode())) {
                return true;
            }
        }

        // Check if experianAddressDetails has data
        if (bureauResponse.getExperianAddressDetails() != null && !bureauResponse.getExperianAddressDetails().isEmpty()) {
            for (CAISHolderAddressDetails address : bureauResponse.getExperianAddressDetails()) {
                if (StringUtils.isNotBlank(address.getFirstLineOfAddress()) && StringUtils.isNotBlank(address.getZipPostalCodeOfAddress())) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Updates the user data with address information from bureau response.
     */
    private static void updateUserDataWithBureauAddress(Map<String, Object> userData, InitialUserDataResponse bureauResponse) {
        // Update addressDetailResponse if available
        if (bureauResponse.getAddressDetailResponse() != null) {
            if (StringUtils.isNotBlank(bureauResponse.getAddressDetailResponse().getAddressLine1())) {
                userData.put(HOUSE_NUMBER_STRING, bureauResponse.getAddressDetailResponse().getAddressLine1());
            }
            if (StringUtils.isNotBlank(bureauResponse.getAddressDetailResponse().getPincode())) {
                // Create PincodeDetailsResponse object instead of storing raw string
                // This ensures consistency with isAddressNotPresent method expectations
                PincodeDetailsResponse pincodeDetailsResponse = PincodeDetailsResponse.builder()
                        .pincode(bureauResponse.getAddressDetailResponse().getPincode())
                        .isValid(true) // Assume bureau data is valid
                        .build();
                userData.put(PINCODE_DETAILS_STRING, pincodeDetailsResponse);
            }
        }

        // Update experianAddressDetails if available
        if (bureauResponse.getExperianAddressDetails() != null && !bureauResponse.getExperianAddressDetails().isEmpty()) {
            userData.put(ADDRESSES_STRING, bureauResponse.getExperianAddressDetails());
        }
    }

    @Getter
    public static class ApprovedAmount {
        private final boolean appendUpto;
        private final long amount;

        public ApprovedAmount(boolean appendUpto, long amount) {
            this.appendUpto = appendUpto;
            this.amount = amount;
        }
    }
}
