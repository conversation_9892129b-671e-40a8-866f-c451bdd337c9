package com.flipkart.fintech.pinaka.service.data.model;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "disbursals")
public class DisbursalsEntity {

    @Id
    @Column(name = "application_id")
    private String applicationId;

    @Column(name = "sm_user_id")
    private String smUserId;

    @Column(name = "loan_amount")
    private Integer loanAmount;

    @Column(name = "disbursal_date")
    private Date disbursalDate;

    @Column(name = "lender")
    private String lender;
}
