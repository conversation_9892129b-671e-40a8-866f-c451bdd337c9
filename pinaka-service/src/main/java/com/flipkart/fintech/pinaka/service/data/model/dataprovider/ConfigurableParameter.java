package com.flipkart.fintech.pinaka.service.data.model.dataprovider;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Created by rajat.mathur on 09/12/18.
 */
@Entity
@Table(name = "ConfigurableParameter")
@NamedQueries({
        @NamedQuery(name = "fetchAllConfigurableParameters", query = "from ConfigurableParameter where configurationContext = :configurationContext"),
        @NamedQuery(name = "fetchConfigurableParameter", query = "from ConfigurableParameter where key = :key")
})
public class ConfigurableParameter {

    @Id
    @Column(name = "id")
    private String key;

    @Column(name = "type")
    private String type;

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "multi_input")
    private String multiInput;

    @Column(name = "data_type")
    private String dataType;

    @Column(name = "callback_uri")
    private String callBackUri;

    @Column(name = "searchable")
    private String searchable;

    @Column(name = "advanced")
    private String advanced;

    @Column(name = "stamp_created")
    private Timestamp stampCreated;

    @Column(name = "stamp_modified")
    private Timestamp stampModified;

    @Column(name = "enumerated_values")
    private String enumeratedValues;

    @Column(name = "configuration_context")
    private String configurationContext;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getMultiInput() {
        return multiInput;
    }

    public void setMultiInput(String multiInput) {
        this.multiInput = multiInput;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCallBackUri() {
        return callBackUri;
    }

    public void setCallBackUri(String callBackUri) {
        this.callBackUri = callBackUri;
    }

    public String getSearchable() {
        return searchable;
    }

    public void setSearchable(String searchable) {
        this.searchable = searchable;
    }

    public String getAdvanced() {
        return advanced;
    }

    public void setAdvanced(String advanced) {
        this.advanced = advanced;
    }

    public Timestamp getStampCreated() {
        return stampCreated;
    }

    public void setStampCreated(Timestamp stampCreated) {
        this.stampCreated = stampCreated;
    }

    public Timestamp getStampModified() {
        return stampModified;
    }

    public void setStampModified(Timestamp stampModified) {
        this.stampModified = stampModified;
    }

    public String getEnumeratedValues() {
        return enumeratedValues;
    }

    public void setEnumeratedValues(String enumeratedValues) {
        this.enumeratedValues = enumeratedValues;
    }

    public String getConfigurationContext() {
        return configurationContext;
    }

    public void setConfigurationContext(String configurationContext) {
        this.configurationContext = configurationContext;
    }

    @Override
    public String toString() {
        return "ConfigurableParameter{" + "key='" + key + '\'' +
                ", type='" + type + '\'' +
                ", displayName='" + displayName + '\'' +
                ", multiInput='" + multiInput + '\'' +
                ", dataType='" + dataType + '\'' +
                ", callBackUri='" + callBackUri + '\'' +
                ", searchable='" + searchable + '\'' +
                ", advanced='" + advanced + '\'' +
                ", stampCreated=" + stampCreated +
                ", stampModified=" + stampModified +
                ", enumeratedValues='" + enumeratedValues + '\'' +
                ", configurationContext='" + configurationContext + '\'' +
                '}';
    }
}
