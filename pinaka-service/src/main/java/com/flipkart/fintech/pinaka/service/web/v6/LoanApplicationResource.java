package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.lending.orchestrator.service.LendingOrchestrator;
import com.flipkart.fintech.pinaka.api.request.v6.ResumePageRequest;
import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.core.LmsService;
import com.google.inject.Inject;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import lombok.CustomLog;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("1/pl")
public class LoanApplicationResource {

  private final LendingOrchestrator lendingOrchestrator;
  private final LmsService lmsService;

  @Inject
  public LoanApplicationResource(LendingOrchestrator lendingOrchestrator, LmsService lmsService) {
    this.lendingOrchestrator = lendingOrchestrator;
    this.lmsService = lmsService;
  }

  @POST
  @Timed
  @ExceptionMetered
  @ApiOperation("Personal Loan Application Deletion")
  @Path("/try-another-lender")
  @UnitOfWork
  @UnitOfWork(value="profile_service")
  public PageActionResponse tryWithAnotherLender(@javax.validation.Valid ResumePageRequest resumePageRequest,
      @NotNull @HeaderParam("X-Request-Id") String requestId,
      @HeaderParam("X-User-Agent") String userAgent,
      @NotNull @HeaderParam("X-Merchant-Id") String merchantId) {
    try {
      log.info("try another lender payload is : {} ",resumePageRequest.toString());
      return lendingOrchestrator.tryWithAnotherLender(
        merchantId, requestId, resumePageRequest, userAgent);
    } catch (Exception e) {
      PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_LoanApplicationResource_tryWithAnotherLender").mark();
      PageActionResponse pageActionResponse = new PageActionResponse();
      log.error("Error while Resume Page Request for accountId {}: {}", resumePageRequest.getSmUserId(), e.getMessage());
      pageActionResponse.setActionSuccess(false);
      ErrorOperation error = ErrorOperation.builder()
          .message("Error while retrying with another lender").build();
      pageActionResponse.setError(error);
      return pageActionResponse;
    }
  }

  // todo: move this entire logic to LMS service
  @GET
  @Timed
  @ExceptionMetered
  @ApiOperation("Get disbursed lenders")
  @Path("/disbursed-lenders")
  public Response getDisbursedLenders(@javax.validation.Valid ResumePageRequest resumePageRequest,
                                      @NotNull @HeaderParam("X-Request-Id") String requestId,
                                      @HeaderParam("X-User-Agent") String userAgent,
                                      @NotNull @HeaderParam("X-Sm-User-Id") String merchantId) {
    try {
      return Response.ok(lmsService.getDisbursedLenders(resumePageRequest.getSmUserId())).build();
    } catch (Exception e) {
      String errMsg = String.format("Error while fetching disbursed lenders for smUserId {}: ", resumePageRequest.getSmUserId());
      log.error(errMsg, e);
      return Response.serverError().entity(errMsg).build();
    }
  }

}
