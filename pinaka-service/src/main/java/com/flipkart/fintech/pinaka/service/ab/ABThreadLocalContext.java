package com.flipkart.fintech.pinaka.service.ab;

import java.util.Set;
import lombok.CustomLog;

@CustomLog
public class ABThreadLocalContext {

    private static final ThreadLocal<Set<String>> abThreadLocal = new ThreadLocal<>();

    public ABThreadLocalContext() {

    }

    public static Set<String> getAbIds() {
        log.debug("Getting AbIds from local thread");
        return abThreadLocal.get();
    }

    public static void remove() {
        abThreadLocal.remove();
    }


    public static void setAbIds(Set<String> abIds) {
        log.debug("Setting AbIds set into local thread val: {}", abIds);
        abThreadLocal.set(abIds);
    }
}

