package com.flipkart.fintech.pinaka.service.core.v6.impl;

import static com.flipkart.fintech.pinaka.service.utils.TransformerUtils.GetAppStateEvent.webhookToAppStateEvent;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.lending.orchestrator.service.PersonalLoanOrchestrator;
import com.flipkart.fintech.pandora.api.model.pl.request.IfscRequest;
import com.flipkart.fintech.pandora.api.model.pl.response.IfscResponse;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.model.bigfoot.PlApplication;
import com.flipkart.fintech.pinaka.api.request.v6.AlmDiscardRequest;
import com.flipkart.fintech.pinaka.api.request.v6.AxisWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.request.v6.SandboxWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.StateChangeRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SubmitRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.SearchResponse;
import com.flipkart.fintech.pinaka.api.response.v6.StateChangeResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.api.response.v6.WebhooksResponse;
import com.flipkart.fintech.pinaka.service.ams.AxisResumeApplicationBuilder;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.actionfactory.ResumeActionBuilder;
import com.flipkart.fintech.pinaka.service.core.page.InvalidMerchantBehaviour;
import com.flipkart.fintech.pinaka.service.core.page.RetryWithEditBehaviour;
import com.flipkart.fintech.pinaka.service.core.page.RetryWithoutEditBehaviour;
import com.flipkart.fintech.pinaka.service.core.v6.EmploymentService;
import com.flipkart.fintech.pinaka.service.core.v6.EventHandler;
import com.flipkart.fintech.pinaka.service.core.v6.LoanHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.transformer.IfscTransformer;
import com.flipkart.fintech.pinaka.service.utils.DateTransformer;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantCheckUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import com.flipkart.fintech.winterfell.api.request.ApplicationResponse;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.UpdatePartnerStateRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;
import com.supermoney.ams.bridge.AmsBridge;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.schema.PinakaService.ApplicationStateEventV1;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.collections.CollectionUtils;

@CustomLog
public class EventHandlerImpl implements EventHandler {
    private final ApplicationService applicationService;
    private final LoanHandler loanHandler;
    private final ResumeActionBuilder resumeActionBuilder;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AmsBridge amsBridge;
    private final PlClient plClient;
    private final AxisResumeApplicationBuilder axisResumeApplicationBuilder;
    private final EmploymentService employmentService;

    @Inject
    public EventHandlerImpl(ApplicationService applicationService,
                            LoanHandler loanHandler,
                            ResumeActionBuilder resumeActionBuilder,
                            AxisResumeApplicationBuilder axisResumeApplicationBuilder,
                            AmsBridge amsBridge,
                            PlClient plClient,
                            EmploymentService employmentService) {
        this.applicationService = applicationService;
        this.loanHandler = loanHandler;
        this.resumeActionBuilder = resumeActionBuilder;
        this.axisResumeApplicationBuilder = axisResumeApplicationBuilder;
        this.amsBridge = amsBridge;
        this.plClient = plClient;
        this.employmentService = employmentService;
    }

    private static boolean isSameTask(String taskId, List<PendingTask> pendingTasks) {
        return CollectionUtils.isNotEmpty(pendingTasks) &&
                taskId.equals(pendingTasks.get(0).getTaskId());
    }

    @Override
    public PageActionResponse submitUserAction(SubmitRequest submitRequest, String merchantId, String requestId) throws PinakaException {
        ApplicationResponse applicationResponse;
        String applicationId = submitRequest.getApplicationId();
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(submitRequest.getAccountId(), submitRequest.getSmUserId());
        try {
            ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser, applicationId);

            if (Objects.isNull(applicationDataResponse)) {  // this scenario is only expected if applicationDataResponse is not received due to system issues
                return RetryWithEditBehaviour.getPageActionResponse();
            }

            if (!MerchantCheckUtils.isMerchantAllowedForJourney(merchantId, applicationDataResponse.getMerchantId())) {
                PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(PersonalLoanOrchestrator.class,
                        "nonWhitelistedBehaviour_invalidMerchant_submitUserAction")).mark();
                return InvalidMerchantBehaviour.getPageActionResponse(merchantId, applicationDataResponse.getMerchantId(), applicationId);
            }

            if (!isSameTask(submitRequest.getTaskId(), applicationDataResponse.getPendingTask())) { // redirect to correct landing page
                return loanHandler.getPageActionResponse(applicationDataResponse, requestId, merchantUser);
            }
            applicationResponse = resumeApplication(merchantUser, submitRequest, requestId, applicationDataResponse);

            ApplicationDataResponse applicationDataResponse1 = applicationService.fetchApplicationData(merchantUser, applicationId);

            if (CollectionUtils.isEmpty(applicationResponse.getPendingTask())) {
                return loanHandler.getPageActionResponse(applicationDataResponse1, requestId, merchantUser);
            }

            Action action = resumeActionBuilder.getAction(requestId, merchantUser, applicationDataResponse1);

            LoanApplication loanApplication = objectMapper.convertValue(applicationDataResponse1.getApplicationData(), LoanApplication.class);

            if (Status.RETRY_WITH_EDIT.equals(loanApplication.getCode())) {
                return RetryWithEditBehaviour.getPageActionResponse();
            } else if (Status.RETRY_WITHOUT_EDIT.equals(loanApplication.getCode())) {
                return RetryWithoutEditBehaviour.getPageActionResponse();
            }
            return new PageActionResponse(action, true, null, null);
        } catch (DataEnrichmentException ex) {
            log.error("Error while enriching the submitted details for accountId: {}, applicationId: {}: {}",
                    merchantUser.getMerchantUserId(), applicationId, ex.getMessage());
            return RetryWithEditBehaviour.getPageActionResponse();
        } catch (PinakaException | JsonProcessingException | URISyntaxException ex) {
            log.error("Error while submitting details for accountId: {}, applicationId: {}: {}",
                    merchantUser.getMerchantUserId(), applicationId, ex.getMessage());
            return RetryWithoutEditBehaviour.getPageActionResponse();
        }
    }

    private ApplicationResponse resumeApplication(MerchantUser merchantUser, SubmitRequest submitRequest,
                                                  String requestId, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        String applicationId = submitRequest.getApplicationId();
        ResumeApplicationRequest request = null;
        try {
            request = axisResumeApplicationBuilder.build(
                    submitRequest, applicationDataResponse, merchantUser, requestId);
        } catch (InvalidMerchantException e) {
            throw new PinakaException(e);
        }
        return applicationService.resumeApplication(merchantUser, applicationId, request);
    }

    @Override
    public WebhooksResponse submitLenderEvent(AxisWebhooksRequest axisWebhooksRequest,
        String requestId) {
        WebhooksResponse webhooksResponse = new WebhooksResponse();

        try {
            String applicationId = axisWebhooksRequest.getLspApplicationId();
            ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(
                MerchantUser.getEmptyUser(), applicationId);
            String applicationState = applicationDataResponse.getApplicationState();

            if (PinakaConstants.PLConstants.PL_TERMINAL_STATES.contains(applicationState)) {
                webhooksResponse.setStatus(Status.SUCCESS);
                return webhooksResponse;
            }
            PlApplication webhookEvent = enrichLenderWebhooksToWebhookEvent(axisWebhooksRequest, applicationDataResponse);
            webhooksResponse.setStatus(Status.SUCCESS);
            ApplicationStateEventV1 applicationStateEventV1 = webhookToAppStateEvent(
                webhookEvent, applicationDataResponse);
            UpdatePartnerStateRequest updatePartnerStateRequest = UpdatePartnerStateRequest.builder()
                .smUserId(applicationStateEventV1.getSmUserId())
                .applicationId(applicationStateEventV1.getApplicationId())
                .merchantId(applicationStateEventV1.getMerchant())
                .partnerState(applicationStateEventV1.getLenderStatus())
                .partnerSubState(applicationStateEventV1.getLenderSubStatus())
                .build();
            applicationService.updatePartnerState(updatePartnerStateRequest);
        } catch (Exception ex) {
            PinakaMetricRegistry.getMetricRegistry()
                .meter(MetricRegistry.name(this.getClass(), "error.ingesting.data.axis")).mark();
            log.info("Error in ingesting the data for applicationid: {}",
                axisWebhooksRequest.getLspApplicationId());
            webhooksResponse.setStatus(Status.FAILURE);

            return webhooksResponse;
        }

        return webhooksResponse;
    }

    @Override
    public WebhooksResponse submitSandboxLenderEvent(SandboxWebhooksRequest sandboxWebhooksRequest) {
        WebhooksResponse webhooksResponse = new WebhooksResponse();
        try {
            String applicationId = sandboxWebhooksRequest.getLspApplicationId();
            ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(
                applicationId, MerchantUser.getEmptyUser());

            PlApplication webhookEvent = enrichSandboxWebhookstoWebhookEvent(sandboxWebhooksRequest,
                applicationDataResponse);
            webhooksResponse.setStatus(Status.SUCCESS);
            //TODO remove  FDP ingestor and make this compulsory
            ApplicationStateEventV1 applicationStateEventV1 = webhookToAppStateEvent(
                webhookEvent, applicationDataResponse);
            UpdatePartnerStateRequest updatePartnerStateRequest = UpdatePartnerStateRequest.builder()
                .smUserId(applicationStateEventV1.getSmUserId())
                .applicationId(applicationStateEventV1.getApplicationId())
                .merchantId(applicationStateEventV1.getMerchant())
                .partnerState(applicationStateEventV1.getLenderStatus())
                .partnerSubState(applicationStateEventV1.getLenderSubStatus())
                .build();
            applicationService.updatePartnerState(updatePartnerStateRequest);
            if (Objects.equals(sandboxWebhooksRequest.getApplicationStatus(), "USER_CANCELLED")) {
                try {
                    discardApplication(applicationDataResponse, applicationId);
                } catch (Exception ex) {
                    PinakaMetricRegistry.getMetricRegistry()
                            .meter(MetricRegistry.name(this.getClass(), "error.discard.cancelled.application.sandbox")).mark();
                    log.error("Error while discarding user_cancelled sandbox application : {} {}",
                            sandboxWebhooksRequest.getLspApplicationId(), ex);
                }
            }
        } catch (Exception ex) {
            PinakaMetricRegistry.getMetricRegistry()
                .meter(MetricRegistry.name(this.getClass(), "error.ingesting.data.sandbox")).mark();
            log.info("Error in ingesting the data for applicationid: {}, {}",
                sandboxWebhooksRequest.getLspApplicationId(), ex.getMessage());
            webhooksResponse.setStatus(Status.FAILURE);

            return webhooksResponse;
        }

        return webhooksResponse;
    }

    private Boolean discardApplication(ApplicationDataResponse applicationDataResponse, String applicationId) {
        MerchantUser merchantUser = MerchantUser.getMerchantUser(
                applicationDataResponse.getMerchantId(),
                applicationDataResponse.getExternalUserId(),
                applicationDataResponse.getSmUserId()
        );
        return applicationService.discardApplication(merchantUser, applicationId);
    }

    private PlApplication enrichLenderWebhooksToWebhookEvent(AxisWebhooksRequest axisWebhooksRequest,
                                                             ApplicationDataResponse applicationDataResponse) {
        PlApplication webhookEvent = new PlApplication();

        LoanApplication loanApplication = new ObjectMapper().convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);

        String applicationState = applicationDataResponse.getApplicationState();
        webhookEvent.setApplicationState(applicationState);
        Status status = axisWebhooksRequest.getStage().getStatus();
        webhookEvent.setStepStatus(String.valueOf(status));
        Date date = new Date();
        webhookEvent.setCreatedAt(loanApplication.getApplicationCreatedAt());
        webhookEvent.setUpdatedAt(DateTransformer.formatDate(date));
        webhookEvent.setAccountId(applicationDataResponse.getExternalUserId());
        webhookEvent.setSmUserId(applicationDataResponse.getSmUserId());
        webhookEvent.setLenderApplicationId(axisWebhooksRequest.getLenderApplicationId());
        webhookEvent.setLspApplicationId(axisWebhooksRequest.getLspApplicationId());
        webhookEvent.setStep(axisWebhooksRequest.getStage().getStep().toString());
        webhookEvent.setStateTransitionTime(axisWebhooksRequest.getStateTransitionTime());
        webhookEvent.setLender(axisWebhooksRequest.getStage().getLender().name());
        webhookEvent.setTenant(Tenant.CALM);
        webhookEvent.setProductType(String.valueOf(ProductType.PERSONAL_LOAN));
        webhookEvent.setMerchantId(applicationDataResponse.getMerchantId());

        return webhookEvent;
    }

    private PlApplication enrichSandboxWebhookstoWebhookEvent(SandboxWebhooksRequest sandboxWebhooksRequest, ApplicationDataResponse applicationDataResponse) {
        String applicationState = applicationDataResponse.getApplicationState();
        Date date = new Date();

        return PlApplication.builder()
                .merchantId(applicationDataResponse.getMerchantId())
                .accountId(applicationDataResponse.getExternalUserId())
                .smUserId(applicationDataResponse.getSmUserId())
                .productType(String.valueOf(ProductType.PERSONAL_LOAN))
                .lender((String) applicationDataResponse.getApplicationData().get("financial_provider"))
                .lenderApplicationId(sandboxWebhooksRequest.getLenderApplicationId())
                .lspApplicationId(sandboxWebhooksRequest.getLspApplicationId())
                .tenant(Tenant.CALM)
                .applicationState(applicationState)
                .step(sandboxWebhooksRequest.getApplicationState()) // application state from webhook event
                .stepStatus(sandboxWebhooksRequest.getStatus())
                .stepStatusDescription(sandboxWebhooksRequest.getSubStatus())
                .stateTransitionTime(sandboxWebhooksRequest.getStateTransitionTime())
                .createdAt((String) applicationDataResponse.getApplicationData().get("application_created_at"))
                .updatedAt(DateTransformer.formatDate(date))
                .build();
    }

    @Override
    public SearchResponse getEmpSuggestions(String prefix) {
        return employmentService.getPlEmployerNameSug(prefix);
    }

    @Override
    public SearchResponse getEmpSuggestionV2(String prefix) {
        return employmentService.getEmployerNameSug(prefix);
    }

    @Override
    public StateChangeResponse hasStateChanged(StateChangeRequest submitRequest)
            throws PinakaException {
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUser(submitRequest.getAccountId(), submitRequest.getSmUserId());
        ApplicationState state1 = getActiveApplicationState(submitRequest, merchantUser);
        ApplicationState state2 = ApplicationState.builder()
                .applicationState(submitRequest.getState())
                .applicationType(state1.getApplicationType())
                .build();
        boolean isSameInteraction = amsBridge.isSameInteraction(state1, state2);
        return new StateChangeResponse(!isSameInteraction, null);
    }

    @Override
    public IfscSearchResponse searchIfsc(IfscSearchRequest request) {
        IfscRequest ifscRequest = IfscTransformer.transform(request);
        IfscResponse ifscDetails = plClient.getIfscDetails("idfc", ifscRequest);
        return IfscTransformer.transform(ifscDetails);
    }

    @Override
    public boolean almDiscard(AlmDiscardRequest almDiscardRequest) throws PinakaException{
        return applicationService.almDiscard(almDiscardRequest);
    }

    private ApplicationState getActiveApplicationState(StateChangeRequest submitRequest, MerchantUser merchantUser)
            throws PinakaException {
        ApplicationDataResponse activeApplication = applicationService.fetchApplicationData(
                merchantUser, submitRequest.getApplicationId());
        if (activeApplication == null)
            throw new PinakaException("Invalid request");
        return ApplicationState.create(activeApplication);
    }
}
