package com.flipkart.fintech.pinaka.service.core;

import com.flipkart.affordability.robinhood.api.model.cbc.JourneyStatus;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.exception.WhiteListException;

import java.util.List;
import java.util.Optional;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 31/08/17.
 * @Deprecated to be replaced with BorrowerServiceV7
 */
@Deprecated
public interface BorrowerService {
    List<BorrowerEntity> getActiveBorrowers(String externalId, String merchantKey, List<ProductType> productTypeList);

}
