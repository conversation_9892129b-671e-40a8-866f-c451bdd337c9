package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.utils.TokenUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.submitbuttonwidget.SubmitButtonWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichButtonValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.HashMap;
import java.util.Map;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.UNALLOCATED_USER;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils.getToken;

@CustomLog
public class ErrorScreenTransformer implements SubmitButtonWidgetTransformer {

  private final String waitingScreenJson;
  private final String genericOfferRejectionScreenJson;
  private final String submitButtonJson;
  private final Map<String, String> rejectScreenMap;

  private final String tryWithAnotherLenderButtonJson;

  private final String tryWithAnotherLenderAnnouncement;
  private final String noLenderAssignedAnnouncement;

  @Inject
  ErrorScreenTransformer(@Named("mandateWaitingScreen") String waitingScreenJson,
      @Named("genericRejectScreen") String offerRejectionJson,
      @Named("creditScoreButton") String submitButtonJson,
      @Named("merchantRejectScreens") Map<String, String> rejectScreenMap,
      @Named("tryAnotherLenderButton") String tryWithAnotherLenderButtonJson,
      @Named("tryAnotherLenderAnnouncement") String tryWithAnotherLenderAnnouncement,
      @Named("noLenderAssignedAnnouncement") String noLenderAssignedAnnouncement) {
    this.waitingScreenJson = waitingScreenJson;
    this.genericOfferRejectionScreenJson = offerRejectionJson;
    this.submitButtonJson = submitButtonJson;
    this.rejectScreenMap = rejectScreenMap;
    this.tryWithAnotherLenderButtonJson = tryWithAnotherLenderButtonJson;
    this.tryWithAnotherLenderAnnouncement = tryWithAnotherLenderAnnouncement;
    this.noLenderAssignedAnnouncement = noLenderAssignedAnnouncement;
  }

  public AnnouncementV2WidgetData buildofferRejectionErrorWidgetData(
      ApplicationDataResponse applicationDataResponse, SlotInfo slotInfo) throws PinakaClientException {
    AnnouncementV2WidgetData announcementV2WidgetData;
    try {
      if(StringUtils.isNotBlank(slotInfo.getContentId())) {
        return contentBasedAnnouncement(slotInfo);
      }
      if (StringUtils.isNotBlank(applicationDataResponse.getMerchantId())) {
        announcementV2WidgetData = ObjectMapperUtil.get().readValue(
            rejectScreenMap.getOrDefault(applicationDataResponse.getMerchantId(),
                tryWithAnotherLenderAnnouncement), AnnouncementV2WidgetData.class);
      } else {
        announcementV2WidgetData = ObjectMapperUtil.get()
            .readValue(tryWithAnotherLenderAnnouncement, AnnouncementV2WidgetData.class);
      }
      return announcementV2WidgetData;
    } catch (Exception e) {
      log.error("AnnouncementV2WidgetData build offerRejectionWidget Data failed with error : {}",
          e.getMessage());
      throw new PinakaClientException(e);
    }
  }

  private AnnouncementV2WidgetData contentBasedAnnouncement(SlotInfo slotInfo) throws JsonProcessingException {
    if(UNALLOCATED_USER.equals(slotInfo.getContentId())) {
      return ObjectMapperUtil.get().readValue(noLenderAssignedAnnouncement, AnnouncementV2WidgetData.class);
    }
    throw new IllegalStateException("Should not reach here without contentId");
  }

  public AnnouncementV2WidgetData buildemandateWaitingScreen() throws PinakaClientException {
    try {
      return ObjectMapperUtil.get().readValue(waitingScreenJson, AnnouncementV2WidgetData.class);
    } catch (Exception e) {
      log.error("AnnouncementV2WidgetData build emandateWaitingData failed with error : {}",
          e.getMessage());
      throw new PinakaClientException(e);
    }
  }

  @Override
  public SubmitButtonWidgetData buildSubmitButtonWidgetData(
      ApplicationDataResponse applicationDataResponse) throws PinakaClientException {
    {
      try {
        if (applicationDataResponse.getMerchantId().equalsIgnoreCase("MYNTRA")) {
          return null;
        }
        SubmitButtonWidgetData submitButtonWidgetData = ObjectMapperUtil.get()
            .readValue(tryWithAnotherLenderButtonJson, SubmitButtonWidgetData.class);
        updateToken(submitButtonWidgetData, applicationDataResponse);
        return submitButtonWidgetData;
      } catch (Exception e) {
        log.error("SubmitButtonWidgetData build Widget Data failed with error : {}",
            e.getMessage());
        throw new PinakaClientException(e);
      }
    }
  }

  private void updateToken(SubmitButtonWidgetData submitButtonWidgetData, ApplicationDataResponse applicationDataResponse)
      throws JsonProcessingException {
    RenderableComponent<RichButtonValue> button = submitButtonWidgetData.getSubmitButton().getButton();
    Map<String, Object> params = new HashMap<>();
    params.put("applicationId", applicationDataResponse.getApplicationId());
    params.put("token", getToken(applicationDataResponse.getApplicationId()));
    button.getAction().setParams(params);
  }



}
