package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.apache.commons.lang.StringUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V3_TRAFFIC;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.LEAD_V3_WL_USERS;

public class LV3Util {

    private static final Random random = new Random();

    public static void updateGroupedWidgetSubmitButton(SubmitButtonValue submitButton, ReviewUserDataSourceResponse reviewUserDataSourceResponse) throws PinakaException {
        if (submitButton != null && submitButton.getButton().getAction() != null) {
            Action action = submitButton.getButton().getAction();
            action.setParams(reviewUserDataSourceResponse.getQueryParams());
            action.setEncryption(reviewUserDataSourceResponse.getEncryptionData());
            return;
        }
        throw new PinakaException("Submit button/submit button action is null for Review Page 2 LV3");
    }

    public static void updateSubmitButtons(Map<String, SubmitButtonValue> formFieldSubmitButtons, ReviewUserDataSourceResponse reviewUserDataSourceResponse) throws PinakaException {
        for (SubmitButtonValue submitButton : formFieldSubmitButtons.values()) {
            updateGroupedWidgetSubmitButton(submitButton, reviewUserDataSourceResponse);
        }
    }

    public static boolean isLv3Enabled(DynamicBucket dynamicBucket, String smUserId) {
        List<String> leadV3Users = dynamicBucket.getStringArray(LEAD_V3_WL_USERS);
        return leadV3Users.contains(smUserId) || random.nextInt(100) < dynamicBucket.getInt(LEAD_V3_TRAFFIC);
    }

    public static boolean isLv3Application(ApplicationDataResponse applicationDataResponse) {
        if(applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
            return false;
        }
        if (StringUtils.isNotBlank(applicationDataResponse.getApplicationState()) && applicationDataResponse.getApplicationState().contains("LEAD_V3")) {
            return true;
        }

        Map<String, Object> applicationData = applicationDataResponse.getApplicationData();
        return isLv3Application(applicationData);
    }

    public static boolean isLv3Application(Map<String, Object> applicationData) {
        Set<String> keys = applicationData.keySet();
        if (!keys.isEmpty()) {
            for (String key : keys) {
                if (key.contains("leadV3")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Generic method to create lead events for any version (V3, V4, etc.)
     * Uses LeadV3Events schema but works for all lead versions
     *
     * @param applicationDataResponse Application data
     * @param pageState Current page state
     * @param applicationState Current application state
     * @param version Lead version (V3, V4, etc.)
     * @return LeadV3Events object (used generically for all versions)
     */
    public static LeadV3Events getLeadEvents(ApplicationDataResponse applicationDataResponse, String pageState, String applicationState, String version) {
        return LeadV3Events.newBuilder()
                .setApplicationId(applicationDataResponse.getApplicationId())
                .setEventId(String.format("%s_%s_%s_%s", version, applicationDataResponse.getSmUserId(), pageState, applicationDataResponse.getApplicationId()))
                .setEventTimestamp(Instant.now())
                .setPageState(pageState)
                .setApplicationState(applicationState)
                .build();
    }

    public static String getVersion(LeadDetails.LeadState leadState) throws PinakaException {
        if (leadState.name().contains("LEAD_V3")) {
            return "V3";
        } else if (leadState.name().contains("LEAD_V4")) {
            return "V4";
        }
        throw new PinakaException("Unknown lead state: " + leadState);
    }

    /**
     * Backward compatibility method for V3
     * @deprecated Use getLeadEvents(applicationDataResponse, pageState, applicationState, "V3") instead
     */
    @Deprecated
    public static LeadV3Events getLeadV3Events(ApplicationDataResponse applicationDataResponse, String pageState, String applicationState) {
        return getLeadEvents(applicationDataResponse, pageState, applicationState, "V3");
    }
}
