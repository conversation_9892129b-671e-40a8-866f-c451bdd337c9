package com.flipkart.fintech.pinaka.service.external;

import com.flipkart.de.entity.client.request.pl.PlDiscoveryRequest;
import com.flipkart.de.entity.decision.response.DecisionResponseEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;
import de.client.shade.com.flipkart.de.entity.client.request.CfOnboardRequestEntity;
import de.client.shade.com.flipkart.de.entity.decision.usecase.CfOnboardDecisionEntity;

@ImplementedBy(CriClientImpl.class)
public interface CriClient {


    DecisionResponseEntity getTNSDetails(PlDiscoveryRequest plDiscoveryRequest);




}
