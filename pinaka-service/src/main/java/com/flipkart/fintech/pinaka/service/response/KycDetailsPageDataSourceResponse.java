package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.pandora.api.model.pl.response.AadhaarValidateOtpResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class KycDetailsPageDataSourceResponse {
    private SearchCkycResponse searchCkycResponse;
    private DigilockerAadhaarResponse digilockerAadhaarResponse;
    private AadhaarValidateOtpResponse aadhaarValidateOtpResponse;
    private EncryptionData encryptionData;
    private Map<String, Object> queryParams;

}