package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.Data;

import java.util.Map;

@Data
public class AddressDetailsPageDataSourceResponse extends Value {

    private Map<String, Object> queryParams;
    private EncryptionData encryptionData;

}
