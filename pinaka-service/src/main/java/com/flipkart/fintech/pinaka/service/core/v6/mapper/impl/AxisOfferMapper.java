package com.flipkart.fintech.pinaka.service.core.v6.mapper.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.response.v6.UserOfferDataResponse;
import com.flipkart.fintech.pinaka.service.core.v6.mapper.LenderOfferMapper;
import com.flipkart.fintech.pinaka.service.exception.InvalidOfferDataException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import lombok.CustomLog;

import javax.inject.Inject;

@CustomLog
public class AxisOfferMapper implements LenderOfferMapper {

    private final ObjectMapper objectMapper;

    @Inject
    public AxisOfferMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public UserOfferDataResponse mapToUserOfferData(ApplicationDataResponse applicationDataResponse) throws InvalidOfferDataException {
        try {
            if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
                log.error("Null application data received for Axis offer");
                return null;
            }

            JsonNode appDataNode = objectMapper.valueToTree(applicationDataResponse.getApplicationData());
            JsonNode generatedOfferNode = appDataNode.path("lender_details").path("generated_offer");

            if (generatedOfferNode.isMissingNode() || generatedOfferNode.isEmpty()) {
                log.error("Missing or empty generated_offer in Axis data");
                throw new InvalidOfferDataException("Missing or empty generated_offer in Axis data "+ applicationDataResponse.getApplicationData());
            }

            JsonNode firstOffer = generatedOfferNode.get(0);
            if (firstOffer == null) {
                log.error("No offer found in Axis generated_offer array");
                throw new InvalidOfferDataException("No offer found in Axis generated_offer array");
            }

            int maxLoanAmount = firstOffer.path("loanAmountDetail").path("max").asInt();
            double roi = firstOffer.path("roi").asDouble();
            int maxTenure = firstOffer.path("tenureDetails").path("max").asInt();

            return UserOfferDataResponse.builder()
                    .loanAmount(String.valueOf(maxLoanAmount))
                    .roi(String.valueOf(roi))
                    .tenure(String.valueOf(maxTenure))
                    .build();

        } catch (Exception e) {
            log.error("Error processing Axis offer data", e);
            throw new InvalidOfferDataException("Error processing Axis offer data");
        }
    }
}
