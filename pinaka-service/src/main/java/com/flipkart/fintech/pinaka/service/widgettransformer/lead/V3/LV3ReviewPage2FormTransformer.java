package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.LeadFormWidgetTransformer;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import lombok.CustomLog;
import org.apache.commons.text.StringSubstitutor;

import java.util.Map;
import java.util.Objects;

import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.getLeadEvents;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.getVersion;

@CustomLog
public class LV3ReviewPage2FormTransformer implements LeadFormWidgetTransformer {

    private static final String ONLY_WORK_DETAILS_FORM_TEMPLATE_V3;
    private static final String ONLY_WORK_DETAILS_FORM_TEMPLATE_V4;

    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final FormWidgetDataJsonParser formWidgetDataJsonParser;
    private final LocationRequestHandler locationRequestHandler;
    private final BqIngestionHelper bqIngestionHelper;
    private final BureauDataManager bureauDataManager;

    private final FormConfig formConfig = new FormConfig("18", "90",
            PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);

    static public class WorkDetailsBannerFormTransformer implements BannerWidgetTransformer {

        private static final String NAME_PAGE_BANNER;

        static {
            NAME_PAGE_BANNER = TransformerUtils.readFileasString("template/lead/V3/ReviewScreenBanner.json");
        }

        @Override
        public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
            BannerWidgetData bannerWidgetData;
            try {
                bannerWidgetData = ObjectMapperUtil.get().readValue(NAME_PAGE_BANNER, BannerWidgetData.class);
            } catch (Exception e) {
                throw new PinakaException("Error while building WorkDetails Banner Widget for LV3, userId: " + applicationDataResponse.getSmUserId(), e);
            }
            return bannerWidgetData;
        }
    }

    static {
        ONLY_WORK_DETAILS_FORM_TEMPLATE_V3 = TransformerUtils.readFileasString("template/lead/V3/WorkDetails.json");
        ONLY_WORK_DETAILS_FORM_TEMPLATE_V4 = TransformerUtils.readFileasString("template/lead/V4/WorkDetails.json");
    }

    public LV3ReviewPage2FormTransformer(Decrypter decrypter, DynamicBucket dynamicBucket,
                                         FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
                                         FormWidgetDataFetcher formWidgetDataFetcher,
                                         FormWidgetDataJsonParser formWidgetDataJsonParser,
                                         LocationRequestHandler locationRequestHandler, BqIngestionHelper bqIngestionHelper,
                                         BureauDataManager bureauDataManager) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.formWidgetDataJsonParser = formWidgetDataJsonParser;
        this.locationRequestHandler = locationRequestHandler;
        this.bqIngestionHelper = bqIngestionHelper;
        this.bureauDataManager = bureauDataManager;
    }

    @Override
    public GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse, LeadDetails.LeadState leadState) throws PinakaException {
        GenericFormWidgetData genericFormWidgetData = null;
        String pageState = "WORK_DETAILS";
        try {
            genericFormWidgetData = ObjectMapperUtil.get()
                    .readValue(getFormJson(getOnlyWorkDetailsFormTemplate(leadState), applicationDataResponse), GenericFormWidgetData.class);
            Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getFormFieldValueMapToPrefillForRenderableComponents(genericFormWidgetData.getRenderableComponents());
            // Try to get cached user data first, fallback to external API calls ONLY if cache miss
            Map<String, Object> userData = LV4Util.getCachedUserDataOrFallback(
                    applicationDataResponse, decrypter, locationRequestHandler, this.formWidgetDataFetcher, this.bureauDataManager);

            // Create reviewUserDataSourceResponse only if needed for submit button update
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            LV3Util.updateGroupedWidgetSubmitButton(Objects.requireNonNull(genericFormWidgetData.getSubmitButton()), reviewUserDataSourceResponse);

            formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
            formWidgetDataJsonParser.updateFormFieldValueMapToPrefill(formFieldValueMapToPrefill, genericFormWidgetData.getRenderableComponents());
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), getVersion(leadState)));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), getVersion(leadState)));
            throw new PinakaException(String.format("Error while building WorkDetails Widget for state: %s, for userId: %s",leadState.toString(), applicationDataResponse.getSmUserId()), e);
        }
        return genericFormWidgetData;
    }

    String getFormJson(String leadNamePage, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(leadNamePage);
    }

    String getOnlyWorkDetailsFormTemplate(LeadDetails.LeadState leadState) throws PinakaException {
        if (leadState == LeadDetails.LeadState.LEAD_V3_PAGE_2) {
            return ONLY_WORK_DETAILS_FORM_TEMPLATE_V3;
        } else if (leadState == LeadDetails.LeadState.LEAD_V4_PAGE_2) {
            return ONLY_WORK_DETAILS_FORM_TEMPLATE_V4;
        }
        throw new PinakaException("Unknown lead state: " + leadState);
    }
}
