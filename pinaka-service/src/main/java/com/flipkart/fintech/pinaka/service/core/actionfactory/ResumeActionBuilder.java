package com.flipkart.fintech.pinaka.service.core.actionfactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.v6.Operation;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.utils.TokenUtils;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.List;
import javax.inject.Inject;
import lombok.CustomLog;
import org.apache.http.client.utils.URIBuilder;

@CustomLog
public class ResumeActionBuilder {

  private final LenderActionBuilder lenderActionBuilder;

  private final ProductType productType;

  @Inject
  public ResumeActionBuilder(LenderActionBuilder lenderActionBuilder) {
    this.lenderActionBuilder = lenderActionBuilder;
    this.productType = ProductType.PERSONAL_LOAN;
  }

  public Action getAction(String requestId, MerchantUser merchantUser,
      ApplicationDataResponse applicationDataResponse) throws URISyntaxException, JsonProcessingException {
      PendingTask pendingTask = applicationDataResponse.getPendingTask().get(0);
      List<String> urlActionLink = Arrays.asList(pendingTask.getFormKey().split(PinakaConstants.PLConstants.SEPARATOR));
      ActionType actionType = ActionType.valueOf(urlActionLink.get(0));
      if (ActionType.REDIRECTION.equals(actionType)) {
          return lenderActionBuilder.getAction(requestId, merchantUser, applicationDataResponse);
      }
      String urlLink = urlActionLink.get(1);
      String url = getUrl(pendingTask, urlLink, applicationDataResponse);
      return new Action(url, actionType);
  }


  private String getUrl(PendingTask pendingTask,
      String urlLink, ApplicationDataResponse applicationDataResponse)
      throws URISyntaxException, JsonProcessingException {
    String applicationId = applicationDataResponse.getApplicationId();
    String applicationStatus = applicationDataResponse.getApplicationState();
    String encryptedToken = createEncryptedToken(applicationId);

    return new URIBuilder(urlLink)
        .addParameter(PinakaConstants.PLConstants.APPLICATION_ID, applicationId)
        .addParameter(PinakaConstants.PLConstants.TASK_ID, pendingTask.getTaskId())
        .addParameter(PinakaConstants.PLConstants.TASK_KEY, "dummy_task_key") //TODO: Get rid of these dummy fields
        .addParameter(PinakaConstants.PLConstants.PROCESS_INSTANCE_ID, "dummy_process_id")
        .addParameter(PinakaConstants.PLConstants.CUSTOMER_STATUS, applicationStatus)
        .addParameter(PinakaConstants.PLConstants.TOKEN, encryptedToken)
        .toString();
  }

  private static String createEncryptedToken(String applicationId) throws JsonProcessingException {
    Token token = Token.builder()
        .applicationId(applicationId)
        .operation(Operation.RESUME)
        .build();
    return TokenUtils.getEncryptedToken(token);
  }
}
