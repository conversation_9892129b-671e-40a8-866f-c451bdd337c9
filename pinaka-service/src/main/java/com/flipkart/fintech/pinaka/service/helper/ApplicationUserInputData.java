package com.flipkart.fintech.pinaka.service.helper;

import com.flipkart.fintech.pinaka.api.model.ApplicationUserData;
import com.flipkart.fintech.pinaka.api.model.PaOffer;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.model.EmploymentType;
import lombok.CustomLog;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.PA_OFFER;

@Data
@CustomLog
public class ApplicationUserInputData {

    public static ApplicationUserData getApplicationResponseData(Map<String, Object> applicationDataResponse) throws PinakaException {
        ApplicationUserData data = new ApplicationUserData();

        Map<String, Object> combinedDetails = new HashMap<>();
        combinedDetails.putAll(ApplicationInputDataMapper.getNameDetails(applicationDataResponse));
        combinedDetails.putAll(ApplicationInputDataMapper.getBasicDetails(applicationDataResponse));
        combinedDetails.putAll(ApplicationInputDataMapper.getIncomeDetails(applicationDataResponse));

        data.setFirstName(getString(combinedDetails, "firstName"));
        data.setLastName(getString(combinedDetails, "lastName"));
        data.setPhoneNumber(getString(combinedDetails, "phoneNumber"));
        data.setDob(getString(combinedDetails, "dob"));
        data.setGender(getString(combinedDetails, "gender"));
        data.setArea(getString(combinedDetails, "area"));
        data.setEmail(getString(combinedDetails, "email"));
        data.setIncome(getString(combinedDetails, "income"));
        data.setLoanPurpose(getString(combinedDetails, "loanPurpose"));
        data.setHouseNumber(getString(combinedDetails, "houseNumber"));
        data.setIncomeSource(getString(combinedDetails, "incomeSource"));
        data.setAnnualTurnOver(getString(combinedDetails, "annualTurnOver"));
        data.setBonusIncome(getString(combinedDetails, "bonusIncome"));

        Object employment = combinedDetails.get("employmentType");
        if (employment instanceof String && StringUtils.isNotBlank((String) employment)) {
            data.setEmploymentType(EmploymentType.valueOf((String) employment));
        }

        data.setOrganization(combinedDetails.get("organization"));
        data.setIndustryName(combinedDetails.get("industryName"));
        data.setConsentListData(combinedDetails.get("consentListData"));
        data.setConsentData(combinedDetails.get("consentData"));

        // Handle pan or panNumber
        data.setPan(getString(combinedDetails, "panNumber"));
        if (StringUtils.isBlank(data.getPan())) {
            data.setPan(getString(combinedDetails, "pan"));
        }

        // Handle pincodeDetails or flat
        Object pincodeObj = combinedDetails.get("pincodeDetails");
        if (pincodeObj instanceof Map) {
            Map<?, ?> pincodeMap = (Map<?, ?>) pincodeObj;
            data.setPincode(parseIntSafe(pincodeMap.get("pincode"), "pincode"));
            data.setCity(getString(pincodeMap, "city"));
            data.setState(getString(pincodeMap, "state"));
        } else {
            data.setPincode(parseIntSafe(combinedDetails.get("pincode"), "pincode"));
            data.setCity(getString(combinedDetails, "city"));
            data.setState(getString(combinedDetails, "state"));
        }

        if (applicationDataResponse.get(PA_OFFER) instanceof PaOffer) {
            data.setPaOffer((PaOffer) applicationDataResponse.get(PA_OFFER));
        }

        return data;
    }

    private static String getString(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value instanceof String && StringUtils.isNotBlank(value.toString()) ? value.toString() : null;
    }

    private static Integer parseIntSafe(Object value, String field) throws PinakaException {
        try {
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof String && StringUtils.isNumeric((String) value)) {
                return Integer.parseInt((String) value);
            }
        } catch (Exception e) {
            throw new PinakaException(String.format("Failed to parse integer for field: %s, value: %s ", field, value));
        }
        return null;
    }
}
