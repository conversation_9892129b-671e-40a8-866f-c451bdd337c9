package com.flipkart.fintech.pinaka.service.utils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtils {

  private DateUtils() {

  }

  public static final SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat(
      "yyyy-MM-dd HH:mm:ss");

  public static final SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("MMM dd, yyyy");

  public static final SimpleDateFormat simpleDateFormat3 = new SimpleDateFormat("dd-MM-yyyy");

  public static final SimpleDateFormat simpleDateFormat4 = new SimpleDateFormat("yyyy-MM-dd");

  public static final SimpleDateFormat simpleDateFormat5 = new SimpleDateFormat("MM");

  public static final SimpleDateFormat simpleDateFormat6 = new SimpleDateFormat("yyyy");

  public static final SimpleDateFormat simpleDateFormat7 = new SimpleDateFormat("dd");

  public static final SimpleDateFormat simpleDateFormat8 = new SimpleDateFormat("MMM dd");

  public static final SimpleDateFormat getSimpleDateFormat9 = new SimpleDateFormat("dd/MM/yyyy");

  public static final SimpleDateFormat experianDateFormat = new SimpleDateFormat("yyyyMMdd");

  public static String convertDateFormat(String dateString, SimpleDateFormat originalFormat,
      SimpleDateFormat targetFormat) throws ParseException {
    Date date = originalFormat.parse(dateString);
    return targetFormat.format(date);
  }

  public static String getTimeStampToDate(String dateInMilliSeconds,
      SimpleDateFormat dateFormatter) {
    Timestamp now = new Timestamp(Long.parseLong(dateInMilliSeconds));
    return dateFormatter.format(now);
  }

  public static Date getDateFromString(String dateString, SimpleDateFormat formatter)
      throws ParseException {
    return formatter.parse(dateString);
  }

  public static String addDaysToDate(String date, SimpleDateFormat dateFormatter, int days)
      throws ParseException {
    Calendar c = Calendar.getInstance();
    c.setTime(dateFormatter.parse(date));
    c.add(Calendar.DAY_OF_MONTH, days);
    return dateFormatter.format(c.getTime());
  }

  public static String addMonthsToDate(String date, SimpleDateFormat dateFormatter, int months)
      throws ParseException {
    Calendar c = Calendar.getInstance();
    c.setTime(dateFormatter.parse(date));
    c.add(Calendar.MONTH, months);
    return dateFormatter.format(c.getTime());
  }

  public static Long getCurrentDateInSeconds() {
    return new Date().getTime() / 1000;
  }
}