package com.flipkart.fintech.pinaka.service.core.v7;

import com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants;
import com.flipkart.fintech.security.aes.AESService;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public class FinalName {

  private final String firstName;
  private final String lastName;

  private FinalName(String firstName, String lastName) {
    this.firstName = firstName;
    this.lastName = lastName;
  }

  public static FinalName from(Map<String, Object> verifyPanName,
      Map<String, Object> basicDetails) {
    if (verifyPanName != null && verifyPanName.containsKey("firstName") && StringUtils.isNotBlank(
        decrypt((String) verifyPanName.get("firstName")))) {
      return new FinalName(decrypt((String) verifyPanName.get("firstName")),
          decrypt((String) verifyPanName.get("lastName")));
    }
    return new FinalName(decrypt((String) basicDetails.get("firstName")),
        decrypt((String) basicDetails.get("lastName")));
  }

  private static String decrypt(String data) {
    if (StringUtils.isBlank(data)) {
      return "";
    }
    byte[] plaintextBytes = AESService.decrypt(PLConstants.SMONEY_PL_DATA_ENCRYPTION_KEY,
        Base64.getDecoder().decode(data.getBytes(StandardCharsets.UTF_8)));
    return new String(plaintextBytes, StandardCharsets.UTF_8);
  }

  public String getEncryptedFirstName() {
    return encrypt(firstName);
  }

  public String getEncryptedLastName() {
    return encrypt(lastName);
  }

  private String encrypt(String data) {
    if (StringUtils.isBlank(data)) {
      return "";
    }
    byte[] encryptedBytes = AESService.encrypt(PLConstants.SMONEY_PL_DATA_ENCRYPTION_KEY,
        data.getBytes(StandardCharsets.UTF_8));
    return Base64.getEncoder().encodeToString(encryptedBytes);
  }
}
