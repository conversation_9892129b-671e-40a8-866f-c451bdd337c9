package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LeadApprovedAmountCard;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LandingPageTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LenderCarouselTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4SubmitButtonWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.google.inject.Inject;
import lombok.CustomLog;

/**
 * CardWidgetTransformer for Lead V4 Landing Page
 * Returns card-based widget data based on user scenarios and templates
 */
@CustomLog
public class CardWidgetTransformer {

    private final LV4LandingPageTransformer lv4LandingPageTransformer;
    private final LV4LenderCarouselTransformer lv4LenderCarouselTransformer;
    private final LV4SubmitButtonWidgetTransformer lv4SubmitButtonWidgetTransformer;
    private final LeadApprovedAmountCard leadApprovedAmountCard;

    @Inject
    public CardWidgetTransformer(LV4LandingPageTransformer lv4LandingPageTransformer,
                                 LV4SubmitButtonWidgetTransformer lv4SubmitButtonWidgetTransformer,
                                 LV4LenderCarouselTransformer lv4LenderCarouselTransformer,
                                 LeadApprovedAmountCard leadApprovedAmountCard) {
        this.lv4LandingPageTransformer = lv4LandingPageTransformer;
        this.lv4LenderCarouselTransformer = lv4LenderCarouselTransformer;
        this.lv4SubmitButtonWidgetTransformer = lv4SubmitButtonWidgetTransformer;
        this.leadApprovedAmountCard = leadApprovedAmountCard;
    }

    /**
     * Build widget data for card-based forms
     * Used by PageHandler for CARD_SUMMARY_LIST widget type
     */
    public WidgetData buildWidgetGroupData(int slotId, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        try {
            if (1 == slotId)
                return lv4LandingPageTransformer.buildWidgetGroupData(applicationDataResponse);
            else if (2 == slotId)
                return lv4LenderCarouselTransformer.buildWidgetGroupData(applicationDataResponse);
            else if (3 == slotId)
                return lv4LenderCarouselTransformer.buildWidgetData(applicationDataResponse);
            else throw new PinakaException("Unknown slotId : " + slotId);
        } catch (Exception e) {
            log.error("Error building card widget group data for userId: {}", applicationDataResponse.getSmUserId(), e);
            throw new PinakaException("Error building card widget group data for userId: " + applicationDataResponse.getSmUserId(), e);
        }
    }

    public WidgetData buildWidgetGroupData(String formType, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        try {
            switch (formType) {
                case "LEAD_V4_APPROVED_AMOUNT_WIDGET":
                    return leadApprovedAmountCard.buildWidgetGroupData(applicationDataResponse);
                default:
                    throw new PinakaException("Unknown formType : " + formType);
            }
        } catch (Exception e) {
            log.error("Error building card widget group data for userId: {}", applicationDataResponse.getSmUserId(), e);
            throw new PinakaException("Error building card widget group data for userId: " + applicationDataResponse.getSmUserId(), e);
        }
    }

    public GenericFormWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse, WidgetTypeV4 widgetType) throws PinakaException {
        try {
            return lv4SubmitButtonWidgetTransformer.buildWidgetData(applicationDataResponse, widgetType);
        } catch (Exception e) {
            log.error("Error building card widget group data for userId: {}", applicationDataResponse.getSmUserId(), e);
            throw new PinakaException("Error building card widget group data for userId: " + applicationDataResponse.getSmUserId(), e);
        }
    }

}
