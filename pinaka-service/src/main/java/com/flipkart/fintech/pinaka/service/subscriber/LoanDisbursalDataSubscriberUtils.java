package com.flipkart.fintech.pinaka.service.subscriber;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.supermoney.schema.PandoraService.OfferEventV1;
import com.supermoney.schema.PandoraService.OfferDetail;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class LoanDisbursalDataSubscriberUtils {

    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(OfferEventV1.class, new OfferEventV1Deserializer());
        objectMapper.registerModule(module);
    }

    public static OfferEventV1 parseOfferEventV1(String jsonMessage) throws IOException {
        return objectMapper.readValue(jsonMessage, OfferEventV1.class);
    }

    // Inner custom deserializer
    private static class OfferEventV1Deserializer extends JsonDeserializer<OfferEventV1> {
        @Override
        public OfferEventV1 deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            ObjectNode node = p.getCodec().readTree(p);

            // Handle offer_details array
            List<OfferDetail> details = new ArrayList<>();
            JsonNode detailsNode = node.get("offer_details");
            if (detailsNode != null && detailsNode.isArray()) {
                for (JsonNode detailNode : detailsNode) {
                    OfferDetail detail = OfferDetail.newBuilder()
                            .setMinAmount(getDouble(detailNode, "min_amount"))
                            .setMaxAmount(getDouble(detailNode, "max_amount"))
                            .setMinTenure(getDouble(detailNode, "min_tenure"))
                            .setMaxTenure(getDouble(detailNode, "max_tenure"))
                            .setRoi(getDouble(detailNode, "roi"))
                            .setPfAmount(getDouble(detailNode, "pf_amount"))
                            .setPfType(getString(detailNode, "pf_type"))
                            .setGst(getDouble(detailNode, "gst"))
                            .setStampDuty(getDouble(detailNode, "stamp_duty"))
                            .setNetDisbursed(getLong(detailNode, "net_disbursed"))
                            .setEmi(getLong(detailNode, "emi"))
                            .setFirstEmiDate(getLong(detailNode, "first_emi_date"))
                            .setValidity(getLong(detailNode, "validity"))
                            .build();
                    details.add(detail);
                }
            }

            return OfferEventV1.newBuilder()
                    .setEventId(getString(node, "event_id"))
                    .setDate(getString(node, "date"))
                    .setAdditionalData(getString(node, "additional_data"))
                    .setLender(getString(node, "lender"))
                    .setAccountId(getString(node, "account_id"))
                    .setProgramId(getString(node, "program_id"))
                    .setValidity(getLong(node, "validity"))
                    .setApplicationId(getString(node, "application_id"))
                    .setLead(getString(node, "lead"))
                    .setOfferType(getString(node, "offer_type"))
                    .setOfferId(getString(node, "offer_id"))
                    .setExternalUserId(getString(node, "external_user_id"))
                    .setOfferDetails(details)
                    .build();
        }

        private String getString(JsonNode node, String field) {
            JsonNode val = node.get(field);
            return (val != null && val.has("string")) ? val.get("string").asText() : null;
        }

        private Long getLong(JsonNode node, String field) {
            JsonNode val = node.get(field);
            return (val != null && val.has("long")) ? val.get("long").asLong() : null;
        }

        private Double getDouble(JsonNode node, String field) {
            JsonNode val = node.get(field);
            return (val != null && val.has("double")) ? val.get("double").asDouble() : null;
        }
    }
}