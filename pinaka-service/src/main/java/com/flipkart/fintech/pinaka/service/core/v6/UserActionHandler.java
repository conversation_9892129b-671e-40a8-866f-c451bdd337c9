package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.UserActionHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;

@ImplementedBy(UserActionHandlerImpl.class)
public interface UserActionHandler {
    PageActionResponse submit(UserActionRequest submitRequest, String requestId, String userAgent) throws PinakaException;
}
