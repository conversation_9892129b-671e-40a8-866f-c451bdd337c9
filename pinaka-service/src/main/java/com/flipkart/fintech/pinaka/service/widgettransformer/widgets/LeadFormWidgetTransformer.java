package com.flipkart.fintech.pinaka.service.widgettransformer.widgets;

import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;

public interface LeadFormWidgetTransformer {
    GenericFormWidgetData buildWidgetData(ApplicationDataResponse applicationDataResponse, LeadDetails.LeadState leadState) throws PinakaException;
}
