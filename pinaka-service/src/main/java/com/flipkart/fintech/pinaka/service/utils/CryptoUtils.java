package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.fintech.security.aes.AESService;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 24/04/18.
 */
public class CryptoUtils {
    private CryptoUtils(){

    }

    private static SecretKey secretKey;

    public static void initCrypto(String algo, String key){
        secretKey = new SecretKeySpec(key.getBytes(), algo);
    }

    public static String encrypt(String plainText){
        byte[] byteData = AESService.encrypt(secretKey, plainText.getBytes());
        return new String(Base64.getEncoder().encode(byteData));
    }

    public static String decrypt(String encryptedText){
        byte[] byteData =  AESService.decrypt(secretKey, Base64.getDecoder().decode(encryptedText.getBytes()));
        return new String(byteData);
    }
}
