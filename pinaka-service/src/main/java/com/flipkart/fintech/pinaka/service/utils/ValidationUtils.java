package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.regex.Pattern;

public class ValidationUtils {
    private ValidationUtils() {

    }

    public static boolean isValidName(String name, String regex) {
        return (!StringUtils.isEmpty(name) && Pattern.matches(regex, name));
    }

    public static boolean isCustomerIdAddrEmpty(AddressDetailResponse addressDetailResponse){
        return Objects.isNull(addressDetailResponse)||
                StringUtils.isEmpty(addressDetailResponse.getAddressLine1())||
                StringUtils.isEmpty(addressDetailResponse.getPincode())||
                StringUtils.isEmpty(addressDetailResponse.getCity())||
                StringUtils.isEmpty(addressDetailResponse.getState())||
                StringUtils.isEmpty(addressDetailResponse.getAddressLine2());
    }
}
