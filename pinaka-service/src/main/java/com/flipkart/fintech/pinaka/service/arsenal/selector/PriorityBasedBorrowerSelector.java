package com.flipkart.fintech.pinaka.service.arsenal.selector;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.flipkart.fintech.pinaka.api.enums.Lender.*;

/**
 * <AUTHOR>
 * @date 11/01/24
 */
public class PriorityBasedBorrowerSelector implements BorrowerSelector{

    private final BorrowerSelector borrowerSelector;


    public PriorityBasedBorrowerSelector() {
        this.borrowerSelector = new RandomBorrowerBorrowerSelector();
    }

    @Override
    public Optional<BorrowerEntity> select(MerchantUser merchantUser, List<BorrowerEntity> list) {

        if (list.isEmpty()) return Optional.empty();

        list = list.stream().filter(borrowerEntity -> merchantUser.getMerchantKey().equals(borrowerEntity.getWhitelist().getMerchant().getMerchantKey())).collect(Collectors.toList());

        Set<String> borrowerSet = list.stream().map(BorrowerEntity::getWhitelist).map(WhitelistEntity::getLender).collect(Collectors.toSet());
        if(borrowerSet.contains(FIBE.name())){
            return getBorrowerFromList(list, FIBE.name());
        } else if(borrowerSet.contains(MONEYVIEW.name())){
            return getBorrowerFromList(list, MONEYVIEW.name());
        } else if(borrowerSet.contains(FIBE.name())){
            return getBorrowerFromList(list, FIBE.name());
        } else if(borrowerSet.contains(MONEYVIEWOPENMKT.name())){
            return getBorrowerFromList(list, MONEYVIEWOPENMKT.name());
        } else if(borrowerSet.contains(IDFC.name())){
            return getBorrowerFromList(list, IDFC.name());
        } else {
            return borrowerSelector.select(merchantUser, list);
        }
    }

    private Optional<BorrowerEntity> getBorrowerFromList(List<BorrowerEntity> list, String borrower){
        Stream<BorrowerEntity> borrowerEntityCollection = list.stream().filter(borrowerEntity -> borrower.equals(borrowerEntity.getWhitelist().getLender()));
        return borrowerEntityCollection.max(Comparator.comparing(BorrowerEntity::getUpdatedAt));
    }
}
