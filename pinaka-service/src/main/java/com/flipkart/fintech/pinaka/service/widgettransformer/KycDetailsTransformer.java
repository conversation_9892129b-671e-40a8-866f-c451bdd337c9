package com.flipkart.fintech.pinaka.service.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.api.model.pl.response.AadhaarValidateOtpKyc;
import com.flipkart.fintech.pandora.api.model.pl.response.CkycAddress;
import com.flipkart.fintech.pandora.api.model.pl.response.PersonalDetail;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.flipkart.fintech.pinaka.service.core.v6.DocumentService;
import com.flipkart.fintech.pinaka.service.response.KycDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.leaf.value.KeyValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.KeyValueWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.CustomLog;

@CustomLog
public class KycDetailsTransformer {

    public static final String FULL_NAME = "Full name";
    public static final String DOB = "DOB";
    public static final String ADDRESS = "Address";
    private static String keyValueJson;
    private static String textV2Json;

    private static String eKYCtextV2Json;
    private final DocumentService documentService;


    private KycDetailsPageDataSourceResponse kycDetailsPageDataSourceResponse;
    static {
            keyValueJson = TransformerUtils.readFileasString("template/idfc/KYCDetailsKeyValue.json");
            textV2Json = TransformerUtils.readFileasString("template/idfc/KYCDetailsTextV2.json");
            eKYCtextV2Json = TransformerUtils.readFileasString("template/idfc/EKYCDetailsTextV2.json");
    }

    public KycDetailsTransformer(DocumentService documentService) {
        this.documentService = documentService;
    }

    public KeyValueWidgetData buildkeyValueWidgetData(KycDetailsPageDataSourceResponse pageDataSourceReponse,boolean isEkyc)
            throws JsonProcessingException {
        KeyValueWidgetData keyValueWidgetDataCopy = ObjectMapperUtil.get()
                .readValue(keyValueJson, KeyValueWidgetData.class);
        kycDetailsPageDataSourceResponse = pageDataSourceReponse;
        updateSubmitButton(pageDataSourceReponse, keyValueWidgetDataCopy);
        if(Objects.nonNull(pageDataSourceReponse.getDigilockerAadhaarResponse())){
            updateDataDigilocker(pageDataSourceReponse.getDigilockerAadhaarResponse(),keyValueWidgetDataCopy);
            return keyValueWidgetDataCopy;
        }
        if(isEkyc)
        {
            updateDataUsingEkyc(kycDetailsPageDataSourceResponse.getAadhaarValidateOtpResponse().getKyc(), keyValueWidgetDataCopy);

        }
        else {
            PersonalDetail personalDetail = pageDataSourceReponse.getSearchCkycResponse().getKyc().getPersonalDetails()
                    .get(0);
            updateData(personalDetail, keyValueWidgetDataCopy);
        }
        return keyValueWidgetDataCopy;

    }


    private void updateDataUsingEkyc(AadhaarValidateOtpKyc ekyc, KeyValueWidgetData keyValueWidgetDataCopy) {
        String address  = ekyc.getAddress().getHouse()+", "+ekyc.getAddress().getVtc()+", "+ekyc.getAddress().getState()+", "+ekyc.getAddress().getCountry()+", "+ekyc.getAddress().getPc();
        List<KeyValue> keyValueList = keyValueWidgetDataCopy.getKeyValuePairs();
        KeyValue imageKeyValue = keyValueList.get(0);
        imageKeyValue.getImageValue().setDynamicImageUrl(getImageString(ekyc.getImageId()));
        KeyValue keyValue = new KeyValue();
        keyValue.setKey(FULL_NAME);
        keyValue.setValue(ekyc.getName());
        keyValueList.add(keyValue);
        KeyValue keyValue1 = new KeyValue();
        keyValue1.setKey("Gender");
        keyValue1.setValue(ekyc.getGender());
        keyValueList.add(keyValue1);
        KeyValue keyValue3 = new KeyValue();
        keyValue3.setKey(DOB);
        keyValue3.setValue(ekyc.getDob());
        keyValueList.add(keyValue3);
        KeyValue keyValue2 = new KeyValue();
        keyValue2.setKey(ADDRESS);
        keyValue2.setValue(address);
        keyValueList.add(keyValue2);
        keyValueWidgetDataCopy.setKeyValuePairs(keyValueList);
    }

    private void updateData(PersonalDetail personalDetail,KeyValueWidgetData keyValueWidgetDataCopy) {
        CkycAddress ckycAddress=kycDetailsPageDataSourceResponse.getSearchCkycResponse().getAddress();
        String address  = ckycAddress.getAddressLine1()+", "+ckycAddress.getCity()+", "+ckycAddress.getState()+", "+ckycAddress.getCountry()+", "+ckycAddress.getPincode();
        List<KeyValue> keyValueList = keyValueWidgetDataCopy.getKeyValuePairs();
        KeyValue imageKeyValue = keyValueList.get(0);
        imageKeyValue.getImageValue().setDynamicImageUrl(getImageString(kycDetailsPageDataSourceResponse.getSearchCkycResponse().getKyc().getImages().get(0)
                .getId()));
        KeyValue keyValue = new KeyValue();
        keyValue.setKey(FULL_NAME);
        keyValue.setValue(personalDetail.getCKycFullName());
        keyValueList.add(keyValue);
        KeyValue keyValue1 = new KeyValue();
        keyValue1.setKey("Father’s name");
        keyValue1.setValue(personalDetail.getCKycFatherFullName());
        keyValueList.add(keyValue1);
        KeyValue keyValue3 = new KeyValue();
        keyValue3.setKey(DOB);
        keyValue3.setValue(kycDetailsPageDataSourceResponse.getSearchCkycResponse().getKyc().getPersonalDetails().get(0).getCkycdob());
        keyValueList.add(keyValue3);
        KeyValue keyValue2 = new KeyValue();
        keyValue2.setKey(ADDRESS);
        keyValue2.setValue(address);
        keyValueList.add(keyValue2);
        keyValueWidgetDataCopy.setKeyValuePairs(keyValueList);
    }

    private void updateDataDigilocker(DigilockerAadhaarResponse personalDetail, KeyValueWidgetData keyValueWidgetDataCopy) {
        List<KeyValue> keyValueList = keyValueWidgetDataCopy.getKeyValuePairs();
        KeyValue imageKeyValue = keyValueList.get(0);
        imageKeyValue.getImageValue().setDynamicImageUrl(getImageFromBase64(personalDetail.getResult().getPhoto()));
        KeyValue keyValue = new KeyValue();
        keyValue.setKey(FULL_NAME);
        keyValue.setValue(personalDetail.getResult().getName());
        keyValueList.add(keyValue);
        KeyValue keyValue3 = new KeyValue();
        keyValue3.setKey(DOB);
        keyValue3.setValue(personalDetail.getResult().getDob());
        keyValueList.add(keyValue3);
        KeyValue keyValue2 = new KeyValue();
        keyValue2.setKey(ADDRESS);
        keyValue2.setValue(personalDetail.getResult().getAddress());
        keyValueList.add(keyValue2);
        keyValueWidgetDataCopy.setKeyValuePairs(keyValueList);
    }

    private String getImageString(String imageId) {
        try {
            String imageUrl = "data:image/jpeg;base64,";
            String kycImageId = imageId;
            DocumentDownloadResponse downloadResponse = documentService.downloadDocument(kycImageId);
            String base64EncodedKYCImage= downloadResponse.getBase64EncodedImage();
            Map<String,String> imageMetaData = downloadResponse.getImageMetaData();
            if(imageMetaData.containsKey("documenttype") && imageMetaData.get("documenttype").equals("KYC_IMAGE"))
            {
                imageUrl+=base64EncodedKYCImage;
            }
            return imageUrl;
        } catch (Exception e) {
            log.error("Error while getting kycImage with error : {}", e.getMessage());
        }
        return null;
    }

    private String getImageFromBase64(String base64EncodedKYCImage) {
        String imageUrl = "data:image/jpeg;base64,";
        imageUrl += base64EncodedKYCImage;
        return imageUrl;
    }

    public RichMessageWidgetData buildtextV2WidgetData(boolean isEKyc)
            throws JsonProcessingException {
        RichMessageWidgetData richMessageWidgetDataCopy = ObjectMapperUtil.get().readValue(textV2Json, RichMessageWidgetData.class);
        if(isEKyc) {
            richMessageWidgetDataCopy = ObjectMapperUtil.get()
                    .readValue(eKYCtextV2Json, RichMessageWidgetData.class);
            return richMessageWidgetDataCopy;
        }
        return richMessageWidgetDataCopy;
    }
    public void updateSubmitButton(KycDetailsPageDataSourceResponse kycDetailsPageDataSourceResponse,KeyValueWidgetData keyValueWidgetDataCopy)
    {
        Action action = keyValueWidgetDataCopy.getSubmitButton().getButton().getAction();
        action.setParams(kycDetailsPageDataSourceResponse.getQueryParams());
        action.setEncryption(kycDetailsPageDataSourceResponse.getEncryptionData());
        keyValueWidgetDataCopy.getSubmitButton().getButton().setAction(action);
    }


}