package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.api.builders.UploadDocumentRequestV6Builder;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentUploadResponse;
import com.flipkart.fintech.pinaka.service.core.v6.DocumentService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.io.IOException;
import java.io.InputStream;
import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.CustomLog;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Path("6/document")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Api(value = "DocumentResource")
@CustomLog
public class DocumentResource {

  private final DocumentService documentService;

  @Inject
  public DocumentResource(DocumentService documentService) {
    this.documentService = documentService;
  }

  @Path("/{document_type}/application/{application_id}")
  @POST
  @UnitOfWork
  @ApiOperation(value = "uploadDocument document")
  @ApiResponses(value = {
      @ApiResponse(code = 201, message = "Registered successfully")
  })
  @Timed
  @ExceptionMetered
  @Consumes(MediaType.MULTIPART_FORM_DATA)
  @Produces(MediaType.APPLICATION_JSON)
  public Response uploadDocument(
      @NotNull @HeaderParam("X-Account-Id") String merchantAccountId,
      @PathParam("document_type") String documentType,
      @PathParam("application_id") String applicationId,
      @FormDataParam("encrypted_symmetric_key") String encryptedSymmetricKey,
      @FormDataParam("public_key_ref_id") String publicKeyRefId,
      @FormDataParam("document") InputStream documentInputStream,
      @FormDataParam("document_password") String documentPassword
  ) {
    try {
      UploadDocumentRequestV6 uploadDocumentRequest = new UploadDocumentRequestV6Builder(
          encryptedSymmetricKey, publicKeyRefId, documentInputStream, documentPassword,
          documentType).build();
      MerchantUser merchantUser = MerchantUserUtils.getMerchantUserFromRequestContext();
      DocumentUploadResponse response = documentService.upload(merchantUser, applicationId,
          uploadDocumentRequest);
      return Response.status(Response.Status.OK).entity(response).build();
    } catch (IOException e) {
      log.error("traceId - {}, applicationId - {}, IOException while updating application - ", RequestContextThreadLocal.get().getTraceId(), applicationId, e);
      throw new PinakaWebAppException(Status.BAD_REQUEST, e.getMessage());
    } catch (PinakaException e) {
      log.error("traceId - {}, applicationId - {}, Internal server error while updating application - ", RequestContextThreadLocal.get().getTraceId(), applicationId, e);
      throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
    }
  }
}
