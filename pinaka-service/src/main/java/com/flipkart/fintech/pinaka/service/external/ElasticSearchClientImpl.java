package com.flipkart.fintech.pinaka.service.external;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;

import java.util.*;
import java.util.stream.Collectors;

@CustomLog
public class ElasticSearchClientImpl implements ElasticSearchClient {

    private final RestHighLevelClient restHighLevelClient;
    private final MetricRegistry metricsRegistry;

    @Inject
    public ElasticSearchClientImpl(@Named("elasticsearchClient") RestHighLevelClient restHighLevelClient, MetricRegistry metricsRegistry) {
        this.restHighLevelClient = restHighLevelClient;
        this.metricsRegistry = metricsRegistry;
    }

    @Override
    public SearchHits prefixSearch(String prefix, String index, String field, int size, String sortField) {

        QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchPhrasePrefixQuery(field, prefix));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(size);
        searchSourceBuilder.query(queryBuilder);
        if (StringUtils.isNotEmpty(sortField)) {
            searchSourceBuilder.sort(new FieldSortBuilder(sortField).order(SortOrder.ASC));
        }

        SearchRequest request = buildSearchRequest(searchSourceBuilder, index);
        SearchHits result = null;
        try (Timer.Context timer = metricsRegistry.timer("SM_ELASTICSEARCH_PREFIX_QUERY").time()){
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            result = response.getHits();
        } catch (Exception e) {
            metricsRegistry.meter("SM_ELASTICSEARCH_PREFIX_QUERY_EXCEPTION").mark();
            log.error("Error in getting suggestions for employers name for prefix {}", prefix, e);
        }
        return Optional.ofNullable(result).orElse(SearchHits.empty());
    }

    @Override
    public SearchHits prefixSearch(String prefix, String index, String field, int size) {
        return prefixSearch(prefix, index, field, size, "");
    }

    @Override
    public boolean matchString(String input, String index, String field) {
        QueryBuilder queryBuilder = QueryBuilders.matchPhraseQuery(field, input);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(10);

        SearchRequest request = buildSearchRequest(searchSourceBuilder, index);
        SearchHits result = null;
        try {
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            result = response.getHits();
        } catch (Exception e) {
            log.error("Error in getting employer name match", input, e);
        }
        if (Objects.nonNull(result) && Objects.nonNull(result.getHits()) && result.getHits().length > 0) {
            return Arrays.stream(result.getHits()).filter(hit -> Objects.nonNull(hit.getSourceAsMap()) && input.equalsIgnoreCase((String) hit.getSourceAsMap().getOrDefault(PinakaConstants.CompanyMasterData.EMPLOYER_NAME, StringUtils.EMPTY))).findAny().isPresent();
        } else {
            return false;
        }
    }

    @Override
    public Map<String, Object> fetchDocument(String value, String index, String field) {
        QueryBuilder queryBuilder = QueryBuilders.matchPhraseQuery(field, value);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(10);

        SearchRequest request = buildSearchRequest(searchSourceBuilder, index);
        SearchHits result = null;
        try (Timer.Context timer = metricsRegistry.timer("SM_ELASTICSEARCH_SEARCH_QUERY").time()){
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            result = response.getHits();
            return Arrays.stream(result.getHits()).findFirst().get().getSourceAsMap();
        } catch (NoSuchElementException e) {
            log.error("The value is not found for field in elasticsearch: {}", value);
            return null;
        } catch (Exception e) {
            metricsRegistry.meter("SM_ELASTICSEARCH_SEARCH_QUERY_EXCEPTION").mark();
            log.error("Error in getting employer name match", value, e);
        }
        return new HashMap<>();
    }

    @Override
    public Map<String, List<String>> getUniqueValuesForFields(List<String> fields, String index) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (Objects.nonNull(fields)) {
            fields.forEach(field -> searchSourceBuilder.aggregation(AggregationBuilders.terms(field).field(field).size(50)));
        }
        searchSourceBuilder.size(0);

        SearchRequest request = buildSearchRequest(searchSourceBuilder, index);
        Map<String, List<String>> resultMap = new HashMap<>();
        try {
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            Aggregations aggregations = response.getAggregations();
            aggregations.asList().stream().forEach(aggregation -> {
                Terms terms = (Terms) aggregation;
                List<String> uniqueTerms = terms.getBuckets().stream().map(bucket -> bucket.getKeyAsString()).collect(Collectors.toList());
                resultMap.put(aggregation.getName(), uniqueTerms);
            });

        } catch (Exception e) {
            log.error("Error in getting unique filed values", e);
        }
        return resultMap;
    }

    private SearchRequest buildSearchRequest(SearchSourceBuilder searchSourceBuilder, String index) {
        SearchRequest request = new SearchRequest();
        request.source(searchSourceBuilder);
        request.indices(index);
        return request;
    }
}
