package com.flipkart.fintech.pinaka.service.data.model;

import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 31/08/17.
 */
@Entity
@Table(name = "borrowers")
@Data
public class BorrowerEntity extends BaseEntity {

    @Column(name = "external_id")
    private String externalId;

    private String metadata;
    private boolean enabled;

    @NotNull
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "whitelist_id", nullable = false, foreignKey = @ForeignKey(name = "whitelist_id"))
    private WhitelistEntity whitelist;
}
