package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.fintech.offer.orchestrator.model.BlacklistedEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderPincodeServiceabilityEntity;
import com.flipkart.fintech.offer.orchestrator.model.temp.BorrowerEntityNew;
import com.flipkart.fintech.offer.orchestrator.model.temp.MerchantEntityNew;
import com.flipkart.fintech.offer.orchestrator.model.temp.WhitelistEntityNew;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.pinaka.service.application.PinakaConfiguration;
import com.flipkart.fintech.pinaka.service.data.model.*;
import com.flipkart.fintech.pinaka.service.data.model.dataprovider.ConfigurableParameter;
import com.flipkart.fintech.profile.model.*;
import io.dropwizard.db.DataSourceFactory;
import io.dropwizard.db.PooledDataSourceFactory;
import io.dropwizard.hibernate.HibernateBundle;
import org.hibernate.SessionFactory;
import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategy;
import org.hibernate.cfg.Configuration;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;

import java.util.Locale;

/**
 * Created by sujeetkumar.r on 09/08/17.
 */
public class HibernateUtils {

    private static HibernateUtils hibernateUtils = new HibernateUtils();
    private final HibernateBundle<PinakaConfiguration> hibernateBundle;
    private final HibernateBundle<PinakaConfiguration> hibernateSlaveBundle;

    private HibernateUtils(){
        hibernateBundle = new HibernateBundle<PinakaConfiguration>(
                BorrowerEntity.class,
                MerchantEntity.class,
                WhitelistEntity.class,
                SessionEntity.class,
                ConfigurableParameter.class,
                Lender.class,
                ContactDetails.class,
                LenderPincodeServiceabilityEntity.class,
                UserProfileCohortEntity.class,
                BorrowerEntityNew.class,
                WhitelistEntityNew.class,
                MerchantEntityNew.class,
                BlacklistedEntity.class,
                DisbursalsEntity.class
                ) {

            @Override
            public PooledDataSourceFactory getDataSourceFactory(PinakaConfiguration pinakaConfiguration) {
                DataSourceFactory dataSourceFactory = pinakaConfiguration.getDatabaseConfig();
                dataSourceFactory.setUser(pinakaConfiguration.getDatabaseConfig().getEncryptedUser());
                dataSourceFactory.setPassword(pinakaConfiguration.getDatabaseConfig().getEncryptedPassword());
                dataSourceFactory.setUrl(pinakaConfiguration.getDatabaseConfig().getEncryptedUrl());
                dataSourceFactory.setMaxSize(pinakaConfiguration.getDatabaseConfig().getMaxSize());
                return dataSourceFactory;
            }

            @Override
            public void configure(Configuration configuration) {
//                configuration.setInterceptor(new CompositeInterceptor(new BulkFdpIngestionHibernateInterceptor(), new BulkRhIngestionHibernateInterceptor()
//                , new BulkChangeEntityIngestionHibernateInterceptor()));
                configuration.setPhysicalNamingStrategy(new PhysicalNamingStrategy() {
                    @Override
                    public Identifier toPhysicalCatalogName(Identifier identifier, JdbcEnvironment jdbcEnvironment) {
                        return identifier;
                    }

                    @Override
                    public Identifier toPhysicalSchemaName(Identifier identifier, JdbcEnvironment jdbcEnvironment) {
                        return identifier;
                    }

                    @Override
                    public Identifier toPhysicalTableName(Identifier identifier, JdbcEnvironment jdbcEnvironment) {
                        return identifier;
                    }

                    @Override
                    public Identifier toPhysicalSequenceName(Identifier identifier, JdbcEnvironment jdbcEnvironment) {
                        return identifier;
                    }

                    @Override
                    public Identifier toPhysicalColumnName(Identifier identifier, JdbcEnvironment jdbcEnvironment) {
                        return new Identifier(addUnderscores(identifier.getText()), identifier.isQuoted());
                    }
                });
            }

            protected String addUnderscores(String name) {
                final StringBuilder buf = new StringBuilder( name.replace('.', '_') );
                int i = 1;
                while (i < buf.length() - 1) {
                    if (Character.isLowerCase(buf.charAt(i - 1)) && Character.isUpperCase(buf.charAt(i)) && Character.isLowerCase(buf.charAt(i + 1))) {
                        buf.insert(i++, '_');
                    }
                    i++;
                }
                return buf.toString().toLowerCase(Locale.ROOT);
            }
        };


        hibernateSlaveBundle = new HibernateBundle<PinakaConfiguration>(
                BorrowerEntity.class,
                MerchantEntity.class,
                WhitelistEntity.class,
                SessionEntity.class,
                ConfigurableParameter.class,
                Lender.class,
                BorrowerEntityNew.class,
                WhitelistEntityNew.class,
                MerchantEntityNew.class,
                BlacklistedEntity.class
        ) {

            @Override
            public PooledDataSourceFactory getDataSourceFactory(PinakaConfiguration pinakaConfiguration) {
                DataSourceFactory dataSourceFactory = pinakaConfiguration.getDatabaseSlaveConfig();
                dataSourceFactory.setUser(pinakaConfiguration.getDatabaseSlaveConfig().getSlaveEncryptedUser());
                dataSourceFactory.setPassword(pinakaConfiguration.getDatabaseSlaveConfig().getSlaveEncryptedPassword());
                dataSourceFactory.setUrl(pinakaConfiguration.getDatabaseSlaveConfig().getSlaveEncryptedUrl());
                return dataSourceFactory;
            }

            @Override
            public String name(){
                return "db_slave";
            }
        };
    }

    public HibernateBundle<PinakaConfiguration> getHibernateBundle() { return hibernateBundle; }

    public SessionFactory getSessionFactory() { return hibernateBundle.getSessionFactory(); }

    public static HibernateUtils getInstance(){
        return hibernateUtils;
    }

    public HibernateBundle<PinakaConfiguration> getHibernateSlaveBundle() { return hibernateSlaveBundle; }

    public SessionFactory getSlaveSessionFactory() { return hibernateSlaveBundle.getSessionFactory(); }
}