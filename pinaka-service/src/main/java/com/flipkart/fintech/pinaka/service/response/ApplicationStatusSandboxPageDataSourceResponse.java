package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 26/12/23
 */
@RequiredArgsConstructor
@Getter
@Setter
public class ApplicationStatusSandboxPageDataSourceResponse {
    private GetApplicationStatusResponse applicationStatusResponse;
    private String loanId;
    private EncryptionData encryptionData;
}
