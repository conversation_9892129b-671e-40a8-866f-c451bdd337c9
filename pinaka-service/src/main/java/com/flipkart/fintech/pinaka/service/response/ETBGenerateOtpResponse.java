package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ETBGenerateOtpResponse {
    private EncryptionData encryptionData;
    private Map<String, Object> queryParams;
    private List<String> phoneNumberList;
}
