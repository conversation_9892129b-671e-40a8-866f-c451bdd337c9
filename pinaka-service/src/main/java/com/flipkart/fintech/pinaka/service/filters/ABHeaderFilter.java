package com.flipkart.fintech.pinaka.service.filters;

import com.flipkart.abservice.models.response.HeaderResponse;
import com.flipkart.abservice.resources.ABUtils;
import com.flipkart.fintech.pinaka.service.ab.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;
import javax.ws.rs.container.ContainerResponseFilter;
import javax.ws.rs.ext.Provider;


@Provider
public class ABHeaderFilter implements ContainerResponseFilter {
    @Override
    public void filter(ContainerRequestContext requestContext, ContainerResponseContext responseContext) throws IOException {
        if (Objects.nonNull(ABThreadLocalContext.getAbIds())) {
            List<HeaderResponse> headerResponseList = ABUtils.getResponseHeader(new ArrayList<>(ABThreadLocalContext.getAbIds()));
            for (HeaderResponse headerResponse : headerResponseList) {
                responseContext.getHeaders().add(headerResponse.getKey(), headerResponse.getValue());
            }
        }
        ABThreadLocalContext.remove();
    }
}

