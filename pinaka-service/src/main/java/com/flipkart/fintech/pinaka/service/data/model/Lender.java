package com.flipkart.fintech.pinaka.service.data.model;

import com.flipkart.fintech.pinaka.api.enums.LenderState;
import com.flipkart.fintech.pinaka.api.enums.LenderType;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Represents the Lender table.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "Lender")
@NamedQueries({
                  @NamedQuery(name = "getLenderByMerchant", query = "select l from Lender l where l.merchantId = :merchantId and l.lenderId = :lenderId")})
public class Lender {
    /**
     * Unique Identifier for a lender
     */
    @Id
    @Column(name = "lender_id")
    private String lenderId;
    
    /**
     * Name of the lender
     */
    @Column(name = "lender_name")
    private String lenderName;
    
    /**
     * Merchant Identifier associated with the lender.
     */
    @Column(name = "merchant_id")
    private String merchantId;
    
    /**
     * Type of Lender as string
     * {@link #lenderType}
     */
    @Column(name = "lender_type")
    private String lenderTypeString;
    
    /**
     * Type of Lender
     */
    @Transient
    private LenderType lenderType;
    
    /**
     * Current state of the lender as string.
     * {@link #lenderState}
     */
    @Column(name = "state")
    private String lenderStateString;
    
    /**
     * Current state of the lender
     */
    @Transient
    private LenderState lenderState;
    
    /**
     * Timestamp when the entity was created.
     */
    @Column(name = "stamp_created")
    private Timestamp stampCreated;
    
    /**
     * Timestamp when the entity was last modified.
     */
    @Column(name = "stamp_modified")
    private Timestamp stampLastModified;
    
    /**
     * No of times the entity has been modified.
     */
    @Column(name = "mod_count")
    private int modCount;
    
    public String getLenderId() {
        return lenderId;
    }
    
    public void setLenderId(String lenderId) {
        this.lenderId = lenderId;
    }
    
    public String getLenderName() {
        return lenderName;
    }
    
    public void setLenderName(String lenderName) {
        this.lenderName = lenderName;
    }
    
    public String getMerchantId() {
        return merchantId;
    }
    
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
    
    public String getLenderTypeString() {
        return lenderTypeString;
    }
    
    public void setLenderTypeString(String lenderTypeString) {
        this.lenderTypeString = lenderTypeString;
        this.lenderType = LenderType.valueOf(lenderTypeString);
    }
    
    public LenderType getLenderType() {
        return lenderType;
    }
    
    public void setLenderType(LenderType lenderType) {
        this.lenderType = lenderType;
        this.lenderTypeString = this.lenderType.name();
    }
    
    private String getLenderStateString() {
        return lenderStateString;
    }
    
    public void setLenderStateString(String lenderStateString) {
        this.lenderStateString = lenderStateString;
        this.lenderState = LenderState.valueOf(lenderStateString);
    }
    
    public LenderState getLenderState() {
        return LenderState.valueOf(getLenderStateString());
    }
    
    public void setLenderState(LenderState lenderState) {
        this.lenderState = lenderState;
        this.lenderStateString = this.lenderState.name();
    }
    
    public Timestamp getStampCreated() {
        return stampCreated;
    }
    
    public void setStampCreated(Timestamp stampCreated) {
        this.stampCreated = stampCreated;
    }
    
    public Timestamp getStampLastModified() {
        return stampLastModified;
    }
    
    public void setStampLastModified(Timestamp stampLastModified) {
        this.stampLastModified = stampLastModified;
    }
    
    public int getModCount() {
        return modCount;
    }
    
    public void setModCount(int modCount) {
        this.modCount = modCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Lender lender = (Lender) o;

        if (!lenderId.equals(lender.lenderId)) return false;
        if (!lenderName.equals(lender.lenderName)) return false;
        if (!lenderTypeString.equals(lender.lenderTypeString)) return false;
        return lenderStateString.equals(lender.lenderStateString);
    }

    @Override
    public int hashCode() {
        int result = lenderId.hashCode();
        result = 31 * result + lenderName.hashCode();
        result = 31 * result + lenderTypeString.hashCode();
        result = 31 * result + lenderStateString.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "Lender{" +
                   "lenderId='" + lenderId + '\'' +
                   ", lenderName='" + lenderName + '\'' +
                   ", merchantId='" + merchantId + '\'' +
                   ", lenderTypeString='" + lenderTypeString + '\'' +
                   ", lenderType=" + lenderType +
                   ", lenderStateString='" + lenderStateString + '\'' +
                   ", lenderState=" + lenderState +
                   ", stampCreated=" + stampCreated +
                   ", stampLastModified=" + stampLastModified +
                   ", modCount=" + modCount +
                   '}';
    }
}
