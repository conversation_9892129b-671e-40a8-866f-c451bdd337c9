package com.flipkart.fintech.pinaka.service.adaptor.v6;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.ardour.api.models.EncryptionKeyData;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.service.adaptor.v6.formFields.FormFieldContent;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.impl.SecurityResourceHandler;
import com.flipkart.fintech.pinaka.service.enums.PLUserCohort;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.utils.v6.FormUtils;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.codahale.metrics.MetricRegistry;
import lombok.CustomLog;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.*;

@CustomLog
public class UniversalPersonalDetailsAdapter implements IPageAdapter {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final SecurityResourceHandler securityService;

    @Inject
    public UniversalPersonalDetailsAdapter(SecurityResourceHandler securityService) {
        this.securityService = securityService;
    }

    @Override
    public Map<String, VariableData> constructWorkflowData(LoanApplication loanApplication, Map<String, Object> formData,
                                                           MerchantUser merchantUser, String applicationId, String requestId) throws DataEnrichmentException {
        Map<String, VariableData> variableDataMap = new HashMap<>();

        FormField panFormField = FormUtils.getFormField(FormFieldContent.PAN_TEXT_BOX);
        FormField dobFormField = FormUtils.getFormField(FormFieldContent.DOB_TYPE);
        FormField genderFormField = FormUtils.getFormField(FormFieldContent.GENDER);
        FormField consentFormField = FormUtils.getFormField(FormFieldContent.CONSENT_PAN_DETAILS);
        FormField employmentTypeFieldValue = FormUtils.getFormField(FormFieldContent.EMPLOYMENT_TYPE);

        FormField pincodeField = FormUtils.getFormField(FormFieldContent.PINCODE);

        Map<String, Object> responseObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.ENCRYPTION_DATA);
        EncryptionKeyData encryptionKeyData = EncryptionKeyData.builder()
                .encKey((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY))
                .encKeyRef((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY_REF)).build();
        String pan;
        try {
            pan = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(panFormField.getName()));
        } catch (PinakaException e) {
            log.error("Error in decrypting the user details");
            throw new DataEnrichmentException(e);
        }
        if (!Objects.isNull(formData.get(pincodeField.getName())) && !formData.get(pincodeField.getName()).equals("")) {
            variableDataMap.put("pincode", new VariableData(false, formData.get(pincodeField.getName())));
        }

        variableDataMap.put("employment_type", new VariableData(false, formData.get(employmentTypeFieldValue.getName())));
        variableDataMap.put("pan_number", new VariableData(false, EncryptionUtil.encryptWithAes(pan, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
        variableDataMap.put("dob", new VariableData(false, formData.get(dobFormField.getName())));
        variableDataMap.put("gender", new VariableData(false, formData.get(genderFormField.getName())));
        variableDataMap.put("application_id", new VariableData(false, applicationId));
        variableDataMap.put("category_type", new VariableData(false, ProductType.PERSONAL_LOAN.name()));
        variableDataMap.put("account_id", new VariableData(false, merchantUser.getMerchantUserId()));
        variableDataMap.put("pan_consent_provided", new VariableData(false, formData.get(consentFormField.getName())));
        variableDataMap.put("request_id", new VariableData(false, requestId));


        return variableDataMap;
    }

    @Override
    public Map<String, Object> constructApplicationData(LoanApplication loanApplication, Map<String, Object> formData, MerchantUser merchantUser, String applicationId) throws DataEnrichmentException {
        UserDetails userDetails = Objects.isNull(loanApplication.getUserDetails()) ? new UserDetails() : loanApplication.getUserDetails();

        FormField panFormField = FormUtils.getFormField(FormFieldContent.PAN_TEXT_BOX);
        FormField dobFormField = FormUtils.getFormField(FormFieldContent.DOB_TYPE);
        FormField genderFormField = FormUtils.getFormField(FormFieldContent.GENDER);
        FormField consentFormField = FormUtils.getFormField(FormFieldContent.CONSENT_PAN_DETAILS);
        FormField employmentTypeFieldValue = FormUtils.getFormField(FormFieldContent.EMPLOYMENT_TYPE);
        FormField pincodeFormField = FormUtils.getFormField(FormFieldContent.PINCODE);

        Map<String, Object> responseObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.ENCRYPTION_DATA);
        EncryptionKeyData encryptionKeyData = EncryptionKeyData.builder()
                .encKey((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY))
                .encKeyRef((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY_REF)).build();
        String pan;
        try {
            pan = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(panFormField.getName()));
        } catch (PinakaException e) {
            log.error("Error in decrypting the pan {}", formData.get(panFormField.getName()));
            throw new DataEnrichmentException(e);
        }
        userDetails.setPan(EncryptionUtil.encryptWithAes(pan, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        userDetails.setDob(EncryptionUtil.encryptWithAes((String) formData.get(dobFormField.getName()), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        userDetails.setGender(EncryptionUtil.encryptWithAes((String) formData.get(genderFormField.getName()), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        if (!Objects.isNull(formData.get(pincodeFormField.getName()))) {
            userDetails.setPincode(formData.get(pincodeFormField.getName()).toString());
        }


        userDetails.setEmploymentType((EmploymentType) formData.get(employmentTypeFieldValue.getName()));
        userDetails.setPanConsentProvided((boolean) formData.get(consentFormField.getName()));

        Map<String, Object> consentDetailsObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.CONSENT_DETAILS);
        ConsentDetails consentDetails = objectMapper.convertValue(consentDetailsObj, new TypeReference<ConsentDetails>() {
        });
        List<ConsentDetails> consentDetailsList = Objects.isNull(loanApplication.getConsentDetailsList()) ? new ArrayList<>() : loanApplication.getConsentDetailsList();
        consentDetailsList.add(consentDetails);
        loanApplication.setConsentDetailsList(consentDetailsList);

        loanApplication.setUserDetails(userDetails);

        Map<String, Object> applicationDataMap = objectMapper.convertValue(loanApplication, Map.class);

        return applicationDataMap;
    }

    @Override
    public List<DataEnumResponse> constructPageResponse(List<DataEnum> dataEnumList, LoanApplication loanApplication,
                                                        MerchantUser merchantUser, FormType formType) {
        List<DataEnumResponse> dataEnumResponseList = new ArrayList<>();
        for (DataEnum dataEnum : dataEnumList) {
            DataEnumResponse dataEnumResponse = new DataEnumResponse();
            switch (dataEnum) {
                case BANNER:
                    dataEnumResponse = constructBannerWidget(PLUserCohort.NTB);
                    break;
                case FORM:
                    dataEnumResponse = constructFormDataWidget(loanApplication, formType);
                    break;
                case ANNOUNCEMENT:
                    dataEnumResponse = constructAnnouncementWidget();
                    break;
                case HELP:
                    dataEnumResponse = PageConstant.constructHelpWidget();
                    break;
            }
            dataEnumResponseList.add(dataEnumResponse);
        }
        return dataEnumResponseList;
    }

    private BannerDataEnumResponse constructBannerWidget(PLUserCohort userCohort) {
        BannerDataEnumResponse bannerDataEnumResponse =
                BannerDataEnumResponse.builder().
                        dataEnum(DataEnum.BANNER)
                        .url(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.headerBannerUrls.get(userCohort))
                        .build();

        return bannerDataEnumResponse;
    }

    private AnnouncementDataEnumResponse constructAnnouncementWidget() {
        AnnouncementDataEnumResponse announcementDataEnumResponse
                = AnnouncementDataEnumResponse.builder()
                .dataEnum(DataEnum.ANNOUNCEMENT)
                .url(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.announcementImageURL)
                .aspectRatio(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.announcementImageAspectRatio)
                .build();

        return announcementDataEnumResponse;
    }

    private FormDataEnumResponse constructFormDataWidget(LoanApplication loanApplication, FormType formType) {

        FormField headerImageValue = FormUtils.getFormField(FormFieldContent.HEADER_IMAGE_VALUE);
        headerImageValue.setUrl(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.headerImageURL);

        FormField panFormField = FormUtils.getFormField(FormFieldContent.PAN_TEXT_BOX);
        panFormField.setRegex(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.panRegex);
        panFormField.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.panPlaceholder);

        FormField dobFormField = FormUtils.getFormField(FormFieldContent.DOB_TYPE);
        LocalDateTime min = LocalDateTime.now();
        LocalDateTime max = LocalDateTime.now();
        max = max.minusYears(21).minusDays(1);
        min = min.minusYears(60).minusDays(1);

        dobFormField.setMinValue(min.toString());
        dobFormField.setMaxValue(max.toString());

        FormField genderFormField = FormUtils.getFormField(FormFieldContent.GENDER);
        genderFormField.setViewType(ViewType.PILL);
        genderFormField.setOptions(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.genderOptions);
        genderFormField.setDefaultValue("M");

        FormField checkBoxFormField = FormUtils.getFormField(FormFieldContent.CONSENT_PAN_DETAILS);
        checkBoxFormField.setTitle(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.consentText);

        FormField submitButtonFormField = FormUtils.getFormField(FormFieldContent.SUBMIT_DETAILS);
        submitButtonFormField.setActionType(Type.CALM_SUBMIT_BUTTON);
        submitButtonFormField.setTitle(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.buttonText);

        if (Status.RETRY_WITHOUT_EDIT.equals(loanApplication.getCode())) {
            panFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getPan(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            panFormField.setDisabled(true);
            dobFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getDob(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            dobFormField.setDisabled(true);
            genderFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getGender(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
            genderFormField.setDisabled(true);
            checkBoxFormField.setDefaultValue("true");
            checkBoxFormField.setDisabled(true);
        }

        List<FormField> formFields = new ArrayList<>();
        formFields.add(headerImageValue);
        formFields.add(panFormField);
        formFields.add(dobFormField);
        formFields.add(genderFormField);


        Form form = new Form();
        form.setFormType(FormType.valueOf(formType.name()));


        if (formType.equals(FormType.PL_CUSTOMER_ID_ADDR_FORM)) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(UniversalPersonalDetailsAdapter.class, "UserInputAddress", "IsEmpty"));

            FormField pincode = FormUtils.getFormField(FormFieldContent.PINCODE);
            pincode.setRegex(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.pincodeRegex);
            pincode.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.pincodePlaceholder);
            pincode.setUrl(PageConstant.PincodeExistenceUrl);
            pincode.setActionType(Type.FETCH_DATA);

            FormField houseNumber = FormUtils.getFormField(FormFieldContent.HOUSE_NUMBER);
            houseNumber.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.housePlaceholder);

            FormField area = FormUtils.getFormField(FormFieldContent.AREA);
            area.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.areaPlaceholder);

            FormField city = FormUtils.getFormField(FormFieldContent.CITY);
            city.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.cityPlaceholder);

            FormField state = FormUtils.getFormField(FormFieldContent.STATE);
            state.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.statePlaceholder);

            formFields.add(pincode);
            formFields.add(houseNumber);
            formFields.add(area);
            formFields.add(city);
            formFields.add(state);
        }

        formFields.add(checkBoxFormField);
        formFields.add(submitButtonFormField);

        form.setFormFields(formFields);

        return FormDataEnumResponse.builder()
                .dataEnum(DataEnum.FORM)
                .form(form)
                .formType(FormType.valueOf(formType.name()))
                .build();
    }
}
