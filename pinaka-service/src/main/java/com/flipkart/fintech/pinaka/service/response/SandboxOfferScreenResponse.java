package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
public class SandboxOfferScreenResponse {

  private Lender lender;

  private GetOfferResponse getOfferResponse;

  private EncryptionData encryptionData;

  private Map<String, Object> queryParams;

}
