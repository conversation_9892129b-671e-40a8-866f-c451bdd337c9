package com.flipkart.fintech.pinaka.service.helper;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataEncryption;
import com.flipkart.fintech.pinaka.service.exception.InvalidInputException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.schema.PinakaService.LeadV3Events;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Map;
import java.util.List;

@CustomLog
public class UserActionSubmitRequestHelper {

    public static final String FULL_NAME = "fullName";
    public static final String FIRST_NAME = "firstName";
    public static final String LAST_NAME = "lastName";
    public static final String BUSINESS_NAME = "businessName";
    public static final String ORGANIZATION = "organization";
    public static final String LEAD_V4_LANDING_PAGE = "leadV4LandingPage";
    public static final String LEAD_V4_PAGE_1 = "leadV4Page1";
    public static final String LEAD_V4_PAGE_2 = "leadV4Page2";
    public static final String PHONE_NUMBER = "phoneNumber";
    public static final List<String> ORGANIZATION_FIELDS = new ArrayList<>();
    public static final List<String> PINCODE_FIELDS = new ArrayList<>();
    public static final String LV4_HALF_FILLED_SUBMIT = "LV4_HALF_FILLED_SUBMIT";
    public static final String LV4_ALL_FILLED_SUBMIT = "LV4_ALL_FILLED_SUBMIT";
    public static final String PAN_NUMBER = "panNumber";
    public static final String DOB = "dob";
    public static final String GENDER = "gender";
    public static final String AREA = "area";
    public static final String HOUSE_NUMBER = "houseNumber";
    public static final String PINCODE_DETAILS = "pincodeDetails";
    public static final String EMAIL = "email";
    public static final String LOAN_PURPOSE = "loanPurpose";
    public static final String EMPLOYMENT_TYPE = "employmentType";
    public static final String INCOME = "income";
    public static final String BONUS_INCOME = "bonusIncome";
    public static final String INCOME_SOURCE = "incomeSource";
    public static final String LV4_REVIEW_PAGE_1_INVALID_STATE = "LV4_REVIEW_PAGE_1_INVALID_STATE";
    public static final String LV4_WORK_DETAILS_SUBMIT = "LV4_WORK_DETAILS_SUBMIT";
    public static final String LV4_INVALID_STATE = "LV4_INVALID_STATE";
    public static final String LV4_LANDING_PAGE_INVALID_STATE = "LV4_LANDING_PAGE_INVALID_STATE";
    public static final String NAME_PAGE_SUBMIT = "LV4_NAME_PAGE_SUBMIT";
    public static final String EMPTY_SUBMIT = "LV4_EMPTY_SUBMIT";

    static {
        // organization map fields
        ORGANIZATION_FIELDS.add("id");
        ORGANIZATION_FIELDS.add("title");

        // pincode map fields
        PINCODE_FIELDS.add("pincode");
        PINCODE_FIELDS.add("city");
        PINCODE_FIELDS.add("state");
    }

    // Adding a private constructor to prevent instantiation
    private UserActionSubmitRequestHelper() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static void processFormDataFields(UserActionRequest submitRequest, FormDataEncryption formDataEncryption, FormDataDecryption formDataDecryption) throws PinakaException {
        adjustFullName(submitRequest, formDataEncryption, formDataDecryption);
        adjustOrganizationName(submitRequest);
        adjustAnnualIncome(submitRequest);
    }

    public static void adjustAnnualIncome(UserActionRequest submitRequest) throws PinakaException {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();
        if (formData.containsKey(INCOME) && formData.getOrDefault(EMPLOYMENT_TYPE, "").equals("SelfEmployed")) {
            Object incomeValue = formData.get(INCOME);
            if (incomeValue instanceof String) {
                String incomeString = (String) incomeValue;
                if (StringUtils.isNotBlank(incomeString)) {
                    try {
                        Integer v = Integer.parseInt(incomeString);
                        Integer annualIncome = v * 12;
                        formData.put("annualTurnOver", annualIncome);
                    } catch (NumberFormatException e) {
                        log.error("Invalid income value: {}", incomeString, e);
                        throw new PinakaException("Invalid income value provided.", e);
                    }
                }
            } else {
                log.error("Income field is not a string: {}", incomeValue);
                throw new PinakaException("Invalid income value provided. Not a string.");
            }
        }
    }

    private static void adjustFullName(UserActionRequest submitRequest, FormDataEncryption formDataEncryption, FormDataDecryption formDataDecryption) throws PinakaException {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();

        if (formData == null || !formData.containsKey(FULL_NAME)) {
            return;
        }
        String fullNameValue = (String) formData.getOrDefault(FULL_NAME, "");
        if (StringUtils.isBlank(fullNameValue)) {
            throw new PinakaException(String.format("Empty name of user, userID: %s", submitRequest.getSmUserId()));
        }

        String fullName = formDataDecryption.getDecryptedPlainTextString(fullNameValue).trim();
        int lastSpaceIndex = fullName.lastIndexOf(' ');

        String firstName, lastName;
        if (lastSpaceIndex == -1) {
            firstName = lastName = fullName;
        } else {
            firstName = fullName.substring(0, lastSpaceIndex);
            lastName = fullName.substring(lastSpaceIndex + 1);
        }
        formData.remove(FULL_NAME);
        formData.put(FIRST_NAME, formDataEncryption.encryptString(firstName));
        formData.put(LAST_NAME, formDataEncryption.encryptString(lastName));
    }

    private static void adjustOrganizationName(UserActionRequest submitRequest) {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();
        if (formData == null || !formData.containsKey(BUSINESS_NAME)) {
            return;
        }
        formData.put(ORGANIZATION, formData.get(BUSINESS_NAME));
        formData.remove(BUSINESS_NAME);
    }

    @NotNull
    public static LeadV3Events getLeadEvents(ApplicationDataResponse applicationDataResponse, UserActionRequest submitRequest) {
        String state = getSubmitEventState(submitRequest);
        return LeadV3Events.newBuilder()
                .setApplicationId(applicationDataResponse.getApplicationId())
                .setEventId(String.format("%s_%s_%s_%s", "V4", applicationDataResponse.getSmUserId(), state, applicationDataResponse.getApplicationId()))
                .setEventTimestamp(Instant.now())
                .setPageState(state)
                .setApplicationState(applicationDataResponse.getApplicationState())
                .build();
    }

    @NotNull
    static String getSubmitEventState(UserActionRequest submitRequest) {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();
        String taskKey = formSubmitRequest.getTaskKey();

        if (formData == null) {
            // this should never happen
            return EMPTY_SUBMIT;
        }
        switch (taskKey) {
            case LEAD_V4_LANDING_PAGE: {
                if(formData.containsKey(FULL_NAME) || formData.containsKey(FIRST_NAME)) {
                    return NAME_PAGE_SUBMIT;
                }
                // this should never happen
                return LV4_LANDING_PAGE_INVALID_STATE;
            }
            case LEAD_V4_PAGE_1: {
                if(formData.containsKey(AREA) && formData.containsKey(PINCODE_DETAILS) && formData.containsKey(EMAIL)) {
                    return LV4_ALL_FILLED_SUBMIT;
                }
                if(formData.containsKey(AREA) && formData.containsKey(PINCODE_DETAILS) && !formData.containsKey(EMAIL)) {
                    return LV4_HALF_FILLED_SUBMIT;
                }
                // this should never happen
                return LV4_REVIEW_PAGE_1_INVALID_STATE;
            }
            case LEAD_V4_PAGE_2: {
                return LV4_WORK_DETAILS_SUBMIT;
            }
            // this should never happen
            default: return LV4_INVALID_STATE;
        }
    }

    public static boolean validateRequest(UserActionRequest submitRequest) throws PinakaException {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();
        if (formData == null) {
            // this should never happen
            throw new InvalidInputException("Invalid submit request for V4 journey, invalid formData");
        }
        String taskKey = formSubmitRequest.getTaskKey();
        switch (taskKey) {
            case LEAD_V4_LANDING_PAGE: {
                return validStringFieldInMap(formData, FULL_NAME) && validStringFieldInMap(formData, PHONE_NUMBER);
            }
            case LEAD_V4_PAGE_1: {
                String state = getSubmitEventState(submitRequest);
                if(LV4_HALF_FILLED_SUBMIT.equals(state)) {
                    return validStringFieldInMap(formData, PAN_NUMBER) &&
                            validStringFieldInMap(formData, DOB) &&
                            validStringFieldInMap(formData, GENDER) &&
                            validStringFieldInMap(formData, AREA) &&
                            validStringFieldInMap(formData, HOUSE_NUMBER) &&
                            validSubMapFieldsInMap(formData, PINCODE_DETAILS, PINCODE_FIELDS);
                } else if(LV4_ALL_FILLED_SUBMIT.equals(state)) {
                    return validStringFieldInMap(formData, PAN_NUMBER) &&
                            validStringFieldInMap(formData, DOB) &&
                            validStringFieldInMap(formData, GENDER) &&
                            validStringFieldInMap(formData, AREA) &&
                            validStringFieldInMap(formData, HOUSE_NUMBER) &&
                            validSubMapFieldsInMap(formData, PINCODE_DETAILS, PINCODE_FIELDS) &&
                            validStringFieldInMap(formData, EMAIL) &&
                            validStringFieldInMap(formData, LOAN_PURPOSE) &&
                            validStringFieldInMap(formData, EMPLOYMENT_TYPE) &&
                            validStringFieldInMap(formData, INCOME) &&
                            validStringFieldInMap(formData, INCOME_SOURCE) &&
                            validateOrganizationField(formData);
                }
                throw new InvalidInputException("Invalid submit request for V4 journey for Review Page 1 formData: " + formData);
            }
            case LEAD_V4_PAGE_2: {
                return validStringFieldInMap(formData, EMAIL) &&
                        validStringFieldInMap(formData, LOAN_PURPOSE) &&
                        validStringFieldInMap(formData, EMPLOYMENT_TYPE) &&
                        validStringFieldInMap(formData, INCOME) &&
                        validStringFieldInMap(formData, INCOME_SOURCE) &&
                        validateOrganizationField(formData);
            }
            default: throw new InvalidInputException("Invalid submit request for V4 journey, invalid task key");
        }
    }

    static boolean validStringFieldInMap(Map<String, Object> map, String fieldName) throws PinakaException {
        if(StringUtils.isBlank((String)map.getOrDefault(fieldName, ""))){
            throw new InvalidInputException(String.format("Invalid submit request for V4 journey, missing %s", fieldName));
        }
        return true;
    }

    static boolean validSubMapFieldsInMap(Map<String, Object> formData, String fieldName, List<String> subFieldNames) throws PinakaException {
        if (formData.containsKey(fieldName) && formData.get(fieldName) != null && formData.get(fieldName) instanceof Map) {
            Map<String, Object> subMap = (Map<String, Object>) formData.get(fieldName);
            for (String key : subFieldNames) {
                validStringFieldInMap(subMap, key);
            }
            return true;
        }
        throw new InvalidInputException(String.format("Invalid submit request for V4 journey, missing/invalid %s", "Pincode Details"));
    }

    static boolean validateOrganizationField(Map<String, Object> formData) throws PinakaException {
        if(formData.containsKey(ORGANIZATION) && formData.get(ORGANIZATION) != null && formData.get(ORGANIZATION) instanceof Map) {
            return validSubMapFieldsInMap(formData, ORGANIZATION, ORGANIZATION_FIELDS);
        } else if(formData.containsKey(BUSINESS_NAME)) {
            return validStringFieldInMap(formData, BUSINESS_NAME);
        }
        throw new InvalidInputException("Invalid submit request for V4 journey, missing businessName or organization");
    }
}
