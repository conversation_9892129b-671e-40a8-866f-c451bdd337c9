package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3;

import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataJsonParser;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.BannerWidgetTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.GroupedFormWidgetTransformer;
import com.flipkart.fintech.profile.service.BureauDataManager;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormGroupDataValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import org.apache.commons.text.StringSubstitutor;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.*;

@CustomLog
public class LV3ReviewPage1FormTransformer implements GroupedFormWidgetTransformer {

    private static final String ALL_FILLED_REVIEW_SCREEN_TEMPLATE_LV3;
    private static final String PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE_LV3;
    private static final String NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE_LV3;

    // V4 templates
    private static final String ALL_FILLED_REVIEW_SCREEN_TEMPLATE_V4;
    private static final String PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE_V4;
    private static final String NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE_V4;

    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    private final FormWidgetDataFetcher formWidgetDataFetcher;
    private final FormWidgetDataJsonParser formWidgetDataJsonParser;
    private final LocationRequestHandler locationRequestHandler;
    private final BqIngestionHelper bqIngestionHelper;
    private final BureauDataManager bureauDataManager;

    static {
        ALL_FILLED_REVIEW_SCREEN_TEMPLATE_LV3 = TransformerUtils.readFileasString("template/lead/V3/AllFilledReviewScreen.json");
        PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE_LV3 = TransformerUtils.readFileasString("template/lead/V3/Personal-Details-Prefilled-Without-Work-Address-Details.json");
        NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE_LV3 = TransformerUtils.readFileasString("template/lead/V3/Without-Personal-Without-Address.json");
        ALL_FILLED_REVIEW_SCREEN_TEMPLATE_V4 = TransformerUtils.readFileasString("template/lead/V4/ReviewPage1-AllFilled.json");
        PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE_V4 = TransformerUtils.readFileasString("template/lead/V4/ReviewPage1-HalfFilled.json");
        NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE_V4 = TransformerUtils.readFileasString("template/lead/V4/ReviewPage1-NothingFilled.json");
    }

    static public class LV3ReviewPageBannerFormTransformer implements BannerWidgetTransformer {

        private static final String NAME_PAGE_BANNER;

        static {
            NAME_PAGE_BANNER = TransformerUtils.readFileasString("template/lead/V3/ReviewScreenBanner.json");
        }

        @Override
        public BannerWidgetData buildBannerWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
            BannerWidgetData bannerWidgetData;
            try {
                bannerWidgetData = ObjectMapperUtil.get().readValue(NAME_PAGE_BANNER, BannerWidgetData.class);
            } catch (Exception e) {
                throw new PinakaException("Error while building banner widget data for LV3 Review Page 1 for userId: "+ applicationDataResponse.getSmUserId(), e);
            }
            return bannerWidgetData;
        }
    }

    public LV3ReviewPage1FormTransformer(Decrypter decrypter, DynamicBucket dynamicBucket,
                                         FormWidgetDataPrefillUtils formWidgetDataPrefillUtils,
                                         FormWidgetDataFetcher formWidgetDataFetcher,
                                         FormWidgetDataJsonParser formWidgetDataJsonParser, LocationRequestHandler locationRequestHandler,
                                         BqIngestionHelper bqIngestionHelper, BureauDataManager bureauDataManager) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
        this.formWidgetDataFetcher = formWidgetDataFetcher;
        this.formWidgetDataJsonParser = formWidgetDataJsonParser;
        this.locationRequestHandler = locationRequestHandler;
        this.bqIngestionHelper = bqIngestionHelper;
        this.bureauDataManager = bureauDataManager;
    }

    @Override
    public GroupedFormWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse, LeadDetails.LeadState leadState) throws PinakaException {
        GroupedFormWidgetData groupedFormWidgetData;
        String pageState = "ALL_FILLED";
        try {
            groupedFormWidgetData = ObjectMapperUtil.get().readValue(getFormJson(getAllFilledReviewScreenTemplate(leadState), applicationDataResponse), GroupedFormWidgetData.class);
            Map<String, FormFieldValue> formFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
            Map<String, FormGroupDataValue> groupFieldValueMapToPrefill = this.formWidgetDataJsonParser
                    .getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
            Map<String, SubmitButtonValue> formFieldSubmitButtons = this.formWidgetDataJsonParser
                    .getFormFieldSubmitButtons(groupedFormWidgetData.getFormGroups());

            // Try to get cached user data first, fallback to external API calls ONLY if cache miss
            Map<String, Object> userData = LV4Util.getCachedUserDataOrFallback(
                    applicationDataResponse, decrypter, locationRequestHandler, this.formWidgetDataFetcher, this.bureauDataManager);

            boolean shouldReRead = false;
            if (personalDetailsNotFound(userData)) {
                groupedFormWidgetData = ObjectMapperUtil.get()
                        .readValue(getFormJson(getNothingPrefilledReviewPage2TemplateLv3(leadState), applicationDataResponse), GroupedFormWidgetData.class);
                pageState = "NOTHING_PREFILLED";
                shouldReRead = true;
            } else if (addressNotFound(userData) || workDetailsNotFound(userData)) {
                groupedFormWidgetData = ObjectMapperUtil.get()
                        .readValue(getFormJson(getPersonalDetailsPrefilledOnlyTemplate(leadState), applicationDataResponse), GroupedFormWidgetData.class);
                pageState = "PERSONAL_DETAILS_PREFILLED";
                shouldReRead = true;
            }
            if (shouldReRead) {
                formFieldValueMapToPrefill = this.formWidgetDataJsonParser.getFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
                groupFieldValueMapToPrefill = this.formWidgetDataJsonParser.getGroupFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups());
                formFieldSubmitButtons = this.formWidgetDataJsonParser.getFormFieldSubmitButtons(groupedFormWidgetData.getFormGroups());
            }

            // Create reviewUserDataSourceResponse only when needed for submit button updates
            PageDataSource<ReviewUserDataSourceResponse> reviewDataSource = new InitialUserReviewDataSource();
            ReviewUserDataSourceResponse reviewUserDataSourceResponse = reviewDataSource.getData(applicationDataResponse);
            updateGroupedWidgetSubmitButton(groupedFormWidgetData.getSubmitButton(), reviewUserDataSourceResponse);
            updateSubmitButtons(formFieldSubmitButtons, reviewUserDataSourceResponse);
            formWidgetDataPrefillUtils.prefillGroupedFormFieldValues(groupFieldValueMapToPrefill, userData);
            formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);
            this.formWidgetDataJsonParser.updateFormFieldValueMapToPrefill(groupedFormWidgetData.getFormGroups(), formFieldValueMapToPrefill, formFieldSubmitButtons);
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, pageState, applicationDataResponse.getApplicationState(), getVersion(leadState)));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), getVersion(leadState)));
            throw new PinakaException(String.format("Error while building widget Group Data for state %s, userId: %s", leadState, applicationDataResponse.getSmUserId()), e);
        }
        return groupedFormWidgetData;
    }

    String whichScreenToLoad(Map<String, Object> userData) {
        if (personalDetailsNotFound(userData)) {
            return LEAD_V4_NOTHING_PREFILLED_SCREEN;
        } else if (addressNotFound(userData) || workDetailsNotFound(userData)) {
            return LEAD_V4_HALF_PREFILLED_SCREEN;
        }
        return LEAD_V4_ALL_PREFILLED_SCREEN;
    }

    boolean addressNotFound(Map<String, Object> userData) {
        String addressLineOne = (String) userData.getOrDefault(HOUSE_NUMBER_STRING, "");
        PincodeDetailsResponse pinCode = (PincodeDetailsResponse) userData.getOrDefault(PINCODE_DETAILS_STRING, null);
        List<CAISHolderAddressDetails> addressOptions = (List<CAISHolderAddressDetails>) userData.getOrDefault(ADDRESSES_STRING, Collections.emptyList());
        boolean defaultAddressPresent = StringUtils.isNotBlank(addressLineOne) && pinCode != null && StringUtils.isNotBlank(pinCode.getPincode());
        boolean atLeastOneValidAddressFound = atLeastOneValidAddressPresent(addressOptions);
        return !(defaultAddressPresent || atLeastOneValidAddressFound);
    }

    boolean atLeastOneValidAddressPresent(List<CAISHolderAddressDetails> addressOptions) {
        if (addressOptions == null || addressOptions.isEmpty()) {
            return false;
        }
        for (CAISHolderAddressDetails address : addressOptions) {
            if (StringUtils.isNotBlank(address.getFirstLineOfAddress()) && StringUtils.isNotBlank(address.getZipPostalCodeOfAddress())) {
                return true;
            }
        }
        return false;
    }

    boolean workDetailsNotFound(Map<String, Object> userData) {
        String companyName = (String) userData.getOrDefault(COMPANY_NAME_STRING, "");
        String income = (String) userData.getOrDefault(MONTHLY_INCOME_STRING, "");
        String email = (String) userData.getOrDefault(EMAIL_STRING, "");
        String incomeSource = (String) userData.getOrDefault(INCOME_SOURCE_STRING, "");
        String employmentType = (String) userData.getOrDefault(EMPLOYMENT_TYPE_STRING, "");
        return StringUtils.isEmpty(companyName) || StringUtils.isEmpty(income) || StringUtils.isEmpty(email) || StringUtils.isEmpty(incomeSource) || StringUtils.isEmpty(employmentType);
    }

    boolean personalDetailsNotFound(Map<String, Object> userData) {
        return !isPanNumberPresent(userData) || !isDateOfBirthPresent(userData) || !isGenderStringPresent(userData);
    }

    boolean isDateOfBirthPresent(Map<String, Object> userData) {
        return userData.containsKey("dob") && Objects.nonNull(userData.get("dob")) && StringUtils.isNotBlank(userData.get("dob").toString());
    }

    boolean isPanNumberPresent(Map<String, Object> userData) {
        return userData.containsKey("panNumber") && Objects.nonNull(userData.get("panNumber")) && StringUtils.isNotBlank(userData.get("panNumber").toString());
    }

    boolean isGenderStringPresent(Map<String, Object> userData) {
        boolean isFirstStringPresent = userData.containsKey(GENDER_STRING)
                && Objects.nonNull(userData.get(GENDER_STRING)) &&
                StringUtils.isNotBlank(userData.get(GENDER_STRING).toString());
        boolean isSecondStringPresent = userData.containsKey(GENDER_STRING_2)
                && Objects.nonNull(userData.get(GENDER_STRING_2)) &&
                StringUtils.isNotBlank(userData.get(GENDER_STRING_2).toString());
        return isFirstStringPresent || isSecondStringPresent;
    }

    private String getFormJson(String template, ApplicationDataResponse applicationDataResponse) {
        FormConfig formConfig = new FormConfig("18", "90", PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
        Map<String, Object> formConfigMap = formConfig.getFormConfigMapForPage3(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }

    String getAllFilledReviewScreenTemplate(LeadDetails.LeadState leadState) throws PinakaException {
        if(leadState == LeadDetails.LeadState.LEAD_V3_PAGE_1) {
            return ALL_FILLED_REVIEW_SCREEN_TEMPLATE_LV3;
        } else if(leadState == LeadDetails.LeadState.LEAD_V4_PAGE_1) {
            return ALL_FILLED_REVIEW_SCREEN_TEMPLATE_V4;
        }
        throw new PinakaException("Unknown lead state: " + leadState);
    }

    String getPersonalDetailsPrefilledOnlyTemplate(LeadDetails.LeadState leadState) throws PinakaException {
        if(leadState == LeadDetails.LeadState.LEAD_V3_PAGE_1) {
            return PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE_LV3;
        } else if(leadState == LeadDetails.LeadState.LEAD_V4_PAGE_1) {
            return PERSONAL_DETAILS_PREFILLED_ONLY_TEMPLATE_V4;
        }
        throw new PinakaException("Unknown lead state: " + leadState);
    }

    String getNothingPrefilledReviewPage2TemplateLv3(LeadDetails.LeadState leadState) throws PinakaException {
        if(leadState == LeadDetails.LeadState.LEAD_V3_PAGE_1) {
            return NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE_LV3;
        } else if(leadState == LeadDetails.LeadState.LEAD_V4_PAGE_1) {
            return NOTHING_PREFILLED_REVIEW_PAGE_2_TEMPLATE_V4;
        }
        throw new PinakaException("Unknown lead state: " + leadState);
    }



}
