package com.flipkart.fintech.pinaka.service.arsenal.selector;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import java.util.List;
import java.util.Optional;
import java.util.Random;

public class RandomBorrowerBorrowerSelector implements BorrowerSelector {

  private final Random rand;

  public RandomBorrowerBorrowerSelector() {
    this.rand = new Random();
  }

  @Override
  public Optional<BorrowerEntity> select(MerchantUser merchantUser, List<BorrowerEntity> list) {
    if (list.isEmpty()) return Optional.empty();
    if (list.size() == 1) return Optional.of(list.get(0));
    int index = rand.nextInt(list.size());
    return Optional.of(list.get(index));
  }
}
