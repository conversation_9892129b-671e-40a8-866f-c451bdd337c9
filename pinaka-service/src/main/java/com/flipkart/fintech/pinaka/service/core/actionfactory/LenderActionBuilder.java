package com.flipkart.fintech.pinaka.service.core.actionfactory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.StatusRequest;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.StatusResponse;
import com.flipkart.fintech.pandora.client.PlOnboardingClient;
import com.flipkart.fintech.pinaka.api.request.v6.LenderDetails;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.pinaka.service.factory.ObjectMapperFactory;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.inject.Inject;
import lombok.CustomLog;

@CustomLog
public class LenderActionBuilder {
  private final PlOnboardingClient plOnboardingClient;
  private final ApplicationService applicationService;
  private final StatusActionBuilder statusActionBuilder;
  private final ObjectMapper objectMapper;

  @Inject
  public LenderActionBuilder(PlOnboardingClient plOnboardingClient, ApplicationService applicationService,
      StatusActionBuilder statusActionBuilder) {
    this.plOnboardingClient = plOnboardingClient;
    this.applicationService = applicationService;
    this.statusActionBuilder = statusActionBuilder;
    this.objectMapper = ObjectMapperFactory.getObjectMapper();
  }

  public Action getAction(String requestId, MerchantUser merchantUser,
      ApplicationDataResponse applicationDataResponse) {

    String redirectionUrl = null;
    String applicationId = applicationDataResponse.getApplicationId();
    try {
      StatusResponse statusResponse = getStatusFromLender(applicationId, requestId, merchantUser);

      String state = String.valueOf(statusResponse.getStatus());
      redirectionUrl = statusResponse.getRedirectionUri();

      if (PinakaConstants.PLConstants.PL_TERMINAL_STATES.contains(state)) {
        Map<String, Object> plApplicationData = new HashMap<>();
        LoanApplication loanApplication  = objectMapper.convertValue(applicationDataResponse.getApplicationData(), LoanApplication.class);

        LenderDetails lenderDetails = Objects.isNull(loanApplication.getLenderDetails()) ? new LenderDetails() : loanApplication.getLenderDetails();
        lenderDetails.setApplicationSubmittedTimestamp(String.valueOf(System.currentTimeMillis()));
        plApplicationData.put(PinakaConstants.PLWorkflowVariable.LENDER_DETAILS, lenderDetails);

        Map<String, VariableData> plWorkflowData = new HashMap<>();
        plWorkflowData.put(PinakaConstants.PLWorkflowVariable.LENDER_STATUS, new VariableData(false, state));

        ResumeApplicationRequest resumeApplicationRequest = ResumeApplicationRequest.builder()
            .applicationData(plApplicationData)
            .workflowData(plWorkflowData)
            .smUserId(merchantUser.getSmUserId())
            .pendingTask(applicationDataResponse.getPendingTask().get(0)).build();

        applicationService.resumeApplication(merchantUser, applicationId, resumeApplicationRequest);
        return statusActionBuilder.getAction(applicationDataResponse, merchantUser);
      }
    } catch (Exception ex) {
      log.error("Error while calling get status Request: {}", ex.getMessage());
    }

    return new Action(redirectionUrl, ActionType.OPEN_URL_EXTERNAL);
  }

  @HystrixCommand(
      threadPoolKey = "PL_PANDORA_API_POOL",
      groupKey = "PLPandoraClient",
      commandKey = "STATUS_PL"
  )
  public StatusResponse getStatusFromLender(String applicationId, String requestId, MerchantUser merchantUser) {
    StatusRequest statusRequest = new StatusRequest();
    statusRequest.setLspApplicationId(applicationId);
    statusRequest.setLenders(Lender.AXIS);

    StatusResponse statusResponse = null;
    try {
      statusResponse = plOnboardingClient.getStatusFromLender(statusRequest, requestId, merchantUser.getMerchantKey());
    } catch (Exception ex) {
      log.error("Error while calling get status Request for applicationId: {}: {}", applicationId, ex.getMessage());
    }

    return statusResponse;
  }
}