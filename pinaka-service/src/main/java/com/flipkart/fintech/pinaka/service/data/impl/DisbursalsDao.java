package com.flipkart.fintech.pinaka.service.data.impl;

import com.flipkart.fintech.pinaka.service.data.model.DisbursalsEntity;
import com.google.inject.Inject;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Session;
import org.hibernate.SessionFactory;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 21/11/24
 */
public class DisbursalsDao extends AbstractDAO<DisbursalsEntity> {

    public static final String SM_USER_ID = "smUserId";

    @Inject
    public DisbursalsDao(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    public Optional<DisbursalsEntity> getByApplicationId(String applicationId){
        return Optional.ofNullable(get(applicationId));
    }

    public List<DisbursalsEntity> getBySmUserId(String smUserId){
        Session session = currentSession();
        CriteriaBuilder cb = session.getCriteriaBuilder();
        CriteriaQuery<DisbursalsEntity> cq = cb.createQuery(DisbursalsEntity.class);
        Root<DisbursalsEntity> root = cq.from(DisbursalsEntity.class);
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.equal(root.get(SM_USER_ID), smUserId));
        cq.where(predicates.toArray(new Predicate[0]));
        return session.createQuery(cq).getResultList();
    }

    public void saveOrUpdate(Session session, DisbursalsEntity entity) {
        session.saveOrUpdate(entity);
    }

}
