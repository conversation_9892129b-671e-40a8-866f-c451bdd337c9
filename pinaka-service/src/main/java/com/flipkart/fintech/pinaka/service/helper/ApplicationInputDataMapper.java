package com.flipkart.fintech.pinaka.service.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;

import java.util.HashMap;
import java.util.Map;


public class ApplicationInputDataMapper {

    public static final String BASIC_DETAILS = "basicDetails";
    public static final String REVIEW_PAGE_2 = "reviewPage2";
    public static final String LEAD_V3_PAGE_2 = "leadV3Page2";
    public static final String LEAD_V3_PAGE_1 = "leadV3Page1";
    public static final String REVIEW_PAGE_1 = "reviewPage1";
    public static final String NAME_PAGE = "namePage";
    public static final String LEAD_V3_NAME_PAGE = "leadV3NamePage";
    public static final String LEAD_V4_LANDING_PAGE = "leadV4LandingPage";
    public static final String LEAD_V4_REVIEW_PAGE_1 = "leadV4Page1";
    public static final String LEAD_V4_REVIEW_PAGE_2 = "leadV4Page2";

    private ApplicationInputDataMapper() {
        throw new IllegalStateException("Utility class");
    }


    public static Map<String, Object> getIncomeDetails(Map<String, Object> applicationDataResponse) {
        Map<String, Object> incomeDetails = new HashMap<>();
        if (applicationDataResponse.containsKey(BASIC_DETAILS)) {
            incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(BASIC_DETAILS), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(REVIEW_PAGE_2)) {
            incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(REVIEW_PAGE_2), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(LEAD_V3_PAGE_2)) {
            incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V3_PAGE_2), new TypeReference<Map<String, Object>>() {});
        } else if(applicationDataResponse.containsKey(LEAD_V3_PAGE_1)) {
            incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V3_PAGE_1), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(LEAD_V4_REVIEW_PAGE_2)) {
            incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V4_REVIEW_PAGE_2), new TypeReference<Map<String, Object>>() {});
        } else if(applicationDataResponse.containsKey(LEAD_V4_REVIEW_PAGE_1)) {
            incomeDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V4_REVIEW_PAGE_1), new TypeReference<Map<String, Object>>() {});
        }
        return incomeDetails;
    }

    public static Map<String, Object> getBasicDetails(Map<String, Object> applicationDataResponse) {
        Map<String, Object> basicDetails = new HashMap<>();
        if (applicationDataResponse.containsKey(BASIC_DETAILS)) {
            basicDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(BASIC_DETAILS), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(REVIEW_PAGE_1)) {
            basicDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(REVIEW_PAGE_1), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(LEAD_V3_PAGE_1)) {
            basicDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V3_PAGE_1), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(LEAD_V4_REVIEW_PAGE_1)) {
            basicDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V4_REVIEW_PAGE_1), new TypeReference<Map<String, Object>>() {});
        }
        return basicDetails;
    }

    public static Map<String, Object> getNameDetails(Map<String, Object> applicationDataResponse) {
        Map<String, Object> nameDetails = new HashMap<>();
        if (applicationDataResponse.containsKey(BASIC_DETAILS)) {
            nameDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(BASIC_DETAILS), new TypeReference<Map<String, Object>>() {});
        } else if (applicationDataResponse.containsKey(NAME_PAGE)) {
            nameDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(NAME_PAGE), new TypeReference<Map<String, Object>>() {});
        } else if(applicationDataResponse.containsKey(LEAD_V3_NAME_PAGE)) {
            nameDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V3_NAME_PAGE), new TypeReference<Map<String, Object>>() {});
        }  else if(applicationDataResponse.containsKey(LEAD_V4_LANDING_PAGE)) {
            nameDetails = ObjectMapperUtil.get().convertValue(applicationDataResponse.get(LEAD_V4_LANDING_PAGE), new TypeReference<Map<String, Object>>() {});
        }
        return nameDetails;
    }
}
