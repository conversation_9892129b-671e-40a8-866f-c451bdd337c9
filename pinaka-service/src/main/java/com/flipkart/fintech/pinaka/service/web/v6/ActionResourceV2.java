package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.ActionHandlerV2;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.Inject;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/")
public class ActionResourceV2 {

    private final ActionHandlerV2 actionHandler;
    @Inject
    public ActionResourceV2(ActionHandlerV2 actionHandler){
        this.actionHandler = actionHandler;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("UserAction Submit")
    @Path("/submit")
    @UnitOfWork
    @UnitOfWork(value="profile_service")
    public PageActionResponse bureauSubmit(@Valid FormSubmitRequestV2 submitRequest,
                                           @HeaderParam("X-User-Agent") String userAgent,
                                           @NotNull @HeaderParam("X-Request-Id") String requestId) {
        PageActionResponse pageActionResponse = new PageActionResponse();
        try {
            pageActionResponse = actionHandler.submit(submitRequest, requestId,userAgent);
        } catch (PinakaException ex) {
            log.error("Error while submitting the page for formSubmitRequest: {}: {}", submitRequest.toString(),
                     ex.getMessage());
            pageActionResponse.setActionSuccess(false);
            ErrorOperation errorOperation = new ErrorOperation();
            errorOperation.setMessage(Status.RETRY_WITHOUT_EDIT + PinakaConstants.PIPE_SEPARATOR + PinakaConstants.PLConstants.RETRY_WITHOUT_EDIT_MESSAGE);
            pageActionResponse.setError(errorOperation);
            PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_ActionResourceV2_RETRY_WITHOUT_EDIT").mark();
        }
        log.info("returning following response: {} for account_id:{}",pageActionResponse,submitRequest.toString());
        return pageActionResponse;
    }


}
