package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.*;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;

import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class LeadPageDataSourceResponse extends Value{
    private Map<String, Object> queryParams;
    private EncryptionData encryptionData;
    private ProfileDetailedResponse profile;
    private Boolean isNameEncrypted = true;
}
