package com.flipkart.fintech.pinaka.service.arsenal;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;
import java.util.Optional;

@ImplementedBy(ArsenalServiceImpl.class)
public interface ArsenalService {
  Optional<BorrowerEntity> getBorrower(MerchantUser merchantUser, ProductType productType, String requestId) throws PinakaException;

}
