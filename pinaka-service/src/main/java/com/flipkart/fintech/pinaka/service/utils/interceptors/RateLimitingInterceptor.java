package com.flipkart.fintech.pinaka.service.utils.interceptors;

import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.pinaka.service.annotations.RateLimit;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.configuration.RateLimitConfig;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import com.flipkart.kloud.config.BaseBucketUpdateListener;
import com.flipkart.kloud.config.Bucket;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.kloud.config.error.ConfigServiceException;
import com.google.common.util.concurrent.RateLimiter;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import lombok.CustomLog;

import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.flipkart.fintech.pinaka.service.application.Constants.*;
import static org.eclipse.jetty.http.HttpStatus.TOO_MANY_REQUESTS_429;

@CustomLog
public class RateLimitingInterceptor implements MethodInterceptor {

    private Map<String, RateLimiter> rateLimiterMap;
    private Map<String, Long> timeout;
    private final DynamicBucket dynamicBucket;

    public RateLimitingInterceptor(List<RateLimitConfig> rateLimitConfig, DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
        rateLimiterMap = new HashMap<>();
        timeout = new HashMap<>();
        initializeRateLimiters(rateLimitConfig);
        addDynamicBucketListener(rateLimitConfig);
    }

    private void updateRatelimit(double rate, String key){
        rateLimiterMap.get(key).setRate(rate);
    }

    private String getRateLimitKey(String limiterKey) {
        return "RATE_LIMIT." + limiterKey;
    }

    private void initializeRateLimiters(List<RateLimitConfig> rateLimitConfig) {
        for (RateLimitConfig rlc : rateLimitConfig) {
            String limiterKey = getRateLimitKey(rlc.getLimiterKey());
            Double rateLimit = this.dynamicBucket.getDouble(limiterKey);
            if (rateLimit !=null && rateLimit>0) {
                rateLimiterMap.put(rlc.getLimiterKey(), RateLimiter.create(rateLimit));
                timeout.put(rlc.getLimiterKey(), rlc.getTimeoutInMs());
            }
        }
    }

    private void addDynamicBucketListener(List<RateLimitConfig> rateLimitConfig) {
        try {
            this.dynamicBucket.addListener(new BaseBucketUpdateListener() {
                public void updated(Bucket oldBucket, Bucket newBucket) {
                    for (RateLimitConfig rlc : rateLimitConfig) {
                        String limiterKey = getRateLimitKey(rlc.getLimiterKey());
                        Double newValue = newBucket.getDouble(limiterKey);
                        Double oldValue = oldBucket.getDouble(limiterKey);
                        if (newValue != null && newValue>0 && !newValue.equals(oldValue)) {
                            updateRatelimit(newValue, rlc.getLimiterKey());
                        }
                    }
                }
            });
        } catch (ConfigServiceException e) {
            log.error("Error in adding dynamic bucket listener for rate limiting.");
        }
    }

    @Override
    public Object invoke(MethodInvocation methodInvocation) throws Throwable {
        String limiterKey = methodInvocation.getMethod().getAnnotation(RateLimit.class).limiterKey();
        if (!rateLimiterMap.containsKey(limiterKey)) {
            log.error("Rate limit key is missing for {}.", limiterKey);
            throw new ServiceException(new ServiceErrorResponse(Response.Status.INTERNAL_SERVER_ERROR,
                    Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), RATE_LIMIT_KEY_MISSING));
        }
        RateLimiter qpsLimiter = rateLimiterMap.get(limiterKey);
        if (!qpsLimiter.tryAcquire(timeout.get(limiterKey), TimeUnit.MILLISECONDS)) {// throws exception if lock cannot be acquired
            PinakaMetricRegistry.getMetricRegistry().meter(String.format(RATE_LIMITING_METRIC, limiterKey)).mark();
            throw new PinakaWebAppException(TOO_MANY_REQUESTS_429, RATE_LIMITING_MESSAGE);
        }
        return methodInvocation.proceed();
    }
}