package com.flipkart.fintech.pinaka.service.pagedatasource;

import com.flipkart.fintech.pandora.api.model.pl.response.AadhaarValidateOtpResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.fintech.pinaka.service.response.KycDetailsPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.pinaka.service.utils.v5.ConfigUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;
import org.apache.http.NameValuePair;

import static com.flipkart.fintech.pinaka.service.application.Constants.CREDIT_LINE_LTFS;

public class KycDetailsPageDataSource implements PageDataSource<KycDetailsPageDataSourceResponse>{

    @Inject
    private static ConfigUtils configUtils;
    private boolean isEkyc;
    @Override
    public KycDetailsPageDataSourceResponse getData(ApplicationDataResponse applicationDataResponse) {
        KycDetailsPageDataSourceResponse kycDetailsPageDataSourceResponse = new KycDetailsPageDataSourceResponse();
        if(CREDIT_LINE_LTFS.equals(applicationDataResponse.getApplicationType())){
            DigilockerAadhaarResponse searchCkycResponse = ObjectMapperUtil.get()
                    .convertValue(applicationDataResponse.getApplicationData().get("fetchDigilockerXml"),
                            DigilockerAadhaarResponse.class);
            kycDetailsPageDataSourceResponse.setDigilockerAadhaarResponse(searchCkycResponse);
        }
        else if (isEkyc) {
            AadhaarValidateOtpResponse aadhaarValidateOtpResponse = ObjectMapperUtil.get()
                    .convertValue(applicationDataResponse.getApplicationData().get("getAadhaarDetailsEkyc"),
                            AadhaarValidateOtpResponse.class);
            kycDetailsPageDataSourceResponse.setAadhaarValidateOtpResponse(aadhaarValidateOtpResponse);
        } else {
            SearchCkycResponse searchCkycResponse = ObjectMapperUtil.get()
                    .convertValue(applicationDataResponse.getApplicationData().get("searchDownloadCkyc"),
                            SearchCkycResponse.class);
            kycDetailsPageDataSourceResponse.setSearchCkycResponse(searchCkycResponse);
        }
        List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
        kycDetailsPageDataSourceResponse.setQueryParams(QueryParamUtils.getQueryParams(queryParams));
        Optional<EncryptionData> encryption = configUtils.getEncryptionData();
        kycDetailsPageDataSourceResponse.setEncryptionData(encryption.orElse(null));
        return kycDetailsPageDataSourceResponse;
    }
    public void setKyc(boolean isEkyc)
    {
        this.isEkyc = isEkyc;
    }

}