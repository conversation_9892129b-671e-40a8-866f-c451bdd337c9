package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanUtilityResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class ApplicationStatusPageDataSourceResponse{

    private AutoDisbursalResponse autoDisbursalResponse;
    private LoanUtilityResponse loanUtilityResponse;
    private Map<String, Object> queryParams;
    private EncryptionData encryptionData;
}
