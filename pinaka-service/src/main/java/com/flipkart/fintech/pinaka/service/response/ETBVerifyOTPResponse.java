package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class ETBVerifyOTPResponse {
    private EncryptionData encryptionData;
    private Map<String, Object> queryParams;
    private Map<String,Object> resendOtpformData;

    public Map<String,Object> getResendOtpActionParams() {
        Map<String,Object> resendActionParams = new HashMap<>();
        resendActionParams.putAll(queryParams);
        resendActionParams.put("formData",resendOtpformData);
        return resendActionParams;
    }
}
