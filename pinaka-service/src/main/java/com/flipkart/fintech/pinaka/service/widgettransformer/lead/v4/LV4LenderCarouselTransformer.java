package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.LenderCarouselTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ListFormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardCarouselWidgetDataV0;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import org.apache.commons.lang3.StringUtils;
import lombok.CustomLog;
import com.google.inject.Inject;


@CustomLog
public class LV4LenderCarouselTransformer implements ListFormWidgetTransformer, LenderCarouselTransformer {

    private static final String LANDING_PAGE_CAROUSEL_TEMPLATE;
    private static final String LENDER_CAROUSEL_TEMPLATE;

    private final Decrypter decrypter;

    @Inject
    public LV4LenderCarouselTransformer(Decrypter decrypter) {
        this.decrypter = decrypter;
    }

    static {
        LANDING_PAGE_CAROUSEL_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageVerticalCarousel.json");
        LENDER_CAROUSEL_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageLenderCarousel.json");
    }

    @Override
    public CardSummaryListWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException, JsonProcessingException {
        CardSummaryListWidgetData listWidgetData;
        listWidgetData = ObjectMapperUtil.get().readValue(LANDING_PAGE_CAROUSEL_TEMPLATE, CardSummaryListWidgetData.class);
        return listWidgetData;
    }

    @Override
    public CardCarouselWidgetDataV0 buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException, JsonProcessingException {
        CardCarouselWidgetDataV0 cardCarouselWidgetData;
        cardCarouselWidgetData = ObjectMapperUtil.get().readValue(LENDER_CAROUSEL_TEMPLATE, CardCarouselWidgetDataV0.class);
        if (!hasName(applicationDataResponse)) cardCarouselWidgetData.setContainerStyle(null);

        return cardCarouselWidgetData;
    }

    private boolean hasName(ApplicationDataResponse applicationDataResponse) throws PinakaException {

        if (!LV4Util.hasNameInApplicationData(applicationDataResponse)) {
            PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();
            LeadPageDataSourceResponse leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
            String fullName = LV4Util.getDecryptedFullNameWithFallback(applicationDataResponse, leadPageDataSourceResponse, decrypter);
            log.debug("Fetched leadPageDataSourceResponse for fallback. Name available: {}", StringUtils.isNotBlank(fullName));
            return StringUtils.isNotBlank(fullName);
        } else {
            log.debug("Name available in application data");
            return true;
        }
    }
}
