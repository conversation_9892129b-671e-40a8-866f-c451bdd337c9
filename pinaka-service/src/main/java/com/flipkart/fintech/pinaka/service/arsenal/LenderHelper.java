package com.flipkart.fintech.pinaka.service.arsenal;

import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.service.application.Constants;
import com.flipkart.mobile.parser.UserAgentParserSingleton;
import com.flipkart.mobile.parser.reponseobject.UserAgentResponse;
import lombok.CustomLog;
import org.apache.maven.artifact.versioning.ComparableVersion;

@CustomLog
public class LenderHelper {

  /** IDFC changes were merged in app
   * <a href="https://github.fkinternal.com/Flipkart/fk_android_app/releases/tag/7.75.1820000">...</a>
   */
  private final static ComparableVersion androidVersion = new ComparableVersion("1820000");

  public static boolean isIdfcSupported() {
    UserAgentResponse userAgentResponse = getUserAgentResponse();
    return isValidUserAgent(userAgentResponse);
  }

  private static boolean isValidUserAgent(UserAgentResponse userAgentResponse) {
    if (UserAgentHelper.isMsite(userAgentResponse))
      return true;
    if (UserAgentHelper.isAndroidPlatform(userAgentResponse)) {
      return isIDFCSupportedVersion(userAgentResponse);
    }
    return false;
  }

  private static UserAgentResponse getUserAgentResponse() {
    String userAgent = RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(Constants.X_USER_AGENT);
    log.info("User Agent Response :{}", userAgent);
    try {
      return UserAgentParserSingleton.getInstance().parse(userAgent);
    } catch (Exception e) {
      log.error("Unable to parse userAgent. Returning default", e);
      return new UserAgentResponse();
    }
  }

  public static UserAgentResponse GetUserAgentResponse( String userAgent){
    log.info("User Agent Response :{}", userAgent);
    try {
      return UserAgentParserSingleton.getInstance().parse(userAgent);
    } catch (Exception e) {
      log.error("Unable to parse userAgent. Returning default", e);
      return new UserAgentResponse();
    }
  }

  private static boolean isIDFCSupportedVersion(UserAgentResponse userAgentResponse) {
    ComparableVersion incomingVersion = new ComparableVersion(userAgentResponse.fkApp.version);
    return incomingVersion.compareTo(androidVersion) >=0 ;
  }
}
