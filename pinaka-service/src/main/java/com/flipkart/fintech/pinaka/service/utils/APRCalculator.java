package com.flipkart.fintech.pinaka.service.utils;

public class APRCalculator {
    /**
     * Calculates the Annualised Percentage Rate (APR)
     *
     * @param loanAmount    Total sanctioned loan amount (principal)
     * @param emi           Monthly EMI
     * @param tenureMonths  Total number of EMIs
     * @param processingFee One-time upfront fee deducted from disbursal
     * @return APR as a percentage (rounded to 2 decimal places)
     */
    public static double calculateAPR(double loanAmount, double emi, int tenureMonths, double processingFee) {
        // Net disbursed amount to customer
        double netDisbursed = loanAmount - processingFee;
        // Use binary search to approximate monthly rate
        double low = 0.0;
        double high = 1.0;
        double epsilon = 1e-10;
        double guessRate = 0.0;
        while (high - low > epsilon) {
            guessRate = (low + high) / 2.0;
            double calculatedEMI = computeEMI(netDisbursed, guessRate, tenureMonths);
            if (calculatedEMI > emi) {
                high = guessRate;
            } else {
                low = guessRate;
            }
        }
        double annualRate = guessRate * 12 * 100;
        return Math.round(annualRate * 100.0) / 100.0;
    }
    /**
     * Helper method to compute EMI from principal, rate, and tenure
     */
    public static double computeEMI(double principal, double monthlyRate, int months) {
        if (monthlyRate == 0) return principal / months;
        double numerator = principal * monthlyRate * Math.pow(1 + monthlyRate, months);
        double denominator = Math.pow(1 + monthlyRate, months) - 1;
        return numerator / denominator;
    }
}