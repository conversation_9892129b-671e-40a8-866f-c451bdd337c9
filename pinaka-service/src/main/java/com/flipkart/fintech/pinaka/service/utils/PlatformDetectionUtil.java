package com.flipkart.fintech.pinaka.service.utils;

import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

/**
 * Utility class for detecting platform from user agent strings
 * Classifies requests into android/ios/web platforms based on user agent headers
 */
@CustomLog
public class PlatformDetectionUtil {

    private PlatformDetectionUtil() {
        throw new IllegalStateException("Utility class");
    }

    // Platform constants
    public static final String PLATFORM_ANDROID = "android";
    public static final String PLATFORM_IOS = "ios";
    public static final String PLATFORM_WEB = "web";
    
    // User agent patterns for platform detection
    private static final String FKUA_PATTERN = "FKUA";
    private static final String ANDROID_PATTERN = "Android";
    private static final String IOS_PATTERN = "iPhone|iPad|iPod";
    private static final String MSITE_PATTERN = "msite";

    /**
     * Detects platform from user agent string and classifies into android/ios/web
     * @param userAgent User agent string from request headers
     * @return Platform string: "android", "ios", or "web"
     */
    public static String detectPlatform(String userAgent) {
        if (StringUtils.isBlank(userAgent)) {
            log.debug("User agent is blank, defaulting to web platform");
            return PLATFORM_WEB;
        }

        String lowerUserAgent = userAgent.toLowerCase();

        // Check for FKUA patterns first (Flipkart app specific)
        if (lowerUserAgent.contains(FKUA_PATTERN.toLowerCase())) {
            // Check for msite in FKUA first (highest priority for web)
            if (lowerUserAgent.contains(MSITE_PATTERN)) {
                return PLATFORM_WEB;
            }
            // Check for Android in FKUA
            if (lowerUserAgent.contains(PLATFORM_ANDROID)) {
                return PLATFORM_ANDROID;
            }
            // Check for iOS patterns in FKUA
            if (lowerUserAgent.matches(".*(" + IOS_PATTERN.toLowerCase() + ").*")) {
                return PLATFORM_IOS;
            }
        }

        // Fallback to general user agent patterns
        if (lowerUserAgent.contains(ANDROID_PATTERN.toLowerCase())) {
            return PLATFORM_ANDROID;
        }

        if (lowerUserAgent.matches(".*(" + IOS_PATTERN.toLowerCase() + ").*")) {
            return PLATFORM_IOS;
        }

        // Default to web for all other cases
        return PLATFORM_WEB;
    }

    /**
     * Checks if the platform is Android
     * @param userAgent User agent string
     * @return true if platform is Android, false otherwise
     */
    public static boolean isAndroid(String userAgent) {
        return PLATFORM_ANDROID.equals(detectPlatform(userAgent));
    }

    /**
     * Checks if the platform is iOS
     * @param userAgent User agent string
     * @return true if platform is iOS, false otherwise
     */
    public static boolean isIOS(String userAgent) {
        return PLATFORM_IOS.equals(detectPlatform(userAgent));
    }

    /**
     * Checks if the platform is Web
     * @param userAgent User agent string
     * @return true if platform is Web, false otherwise
     */
    public static boolean isWeb(String userAgent) {
        return PLATFORM_WEB.equals(detectPlatform(userAgent));
    }
}
