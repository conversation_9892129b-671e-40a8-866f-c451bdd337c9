package com.flipkart.fintech.pinaka.service.ams;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.api.request.v6.SubmitRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.service.adaptor.v6.IPageAdapter;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.factory.v6.PageAdapterFactory;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import java.util.Map;
import javax.inject.Inject;

public class AxisResumeApplicationBuilder {
    private final PageAdapterFactory pageAdapterFactory;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Inject
    public AxisResumeApplicationBuilder(PageAdapterFactory pageAdapterFactory) {
        this.pageAdapterFactory = pageAdapterFactory;
    }

    public ResumeApplicationRequest build(SubmitRequest submitRequest, ApplicationDataResponse applicationDataResponse,
                                          MerchantUser merchantUser, String requestId)
            throws DataEnrichmentException, InvalidMerchantException {
        Map<String, Object> currentApplicationData = applicationDataResponse.getApplicationData();
        LoanApplication loanApplication = objectMapper.convertValue(currentApplicationData, LoanApplication.class);
        Status status = loanApplication.getCode();
        ResumeApplicationRequest resumeApplicationRequest = new ResumeApplicationRequest();

        String applicationId = submitRequest.getApplicationId();
        String pageName = submitRequest.getPageName();
        Map<String, Object> formData = submitRequest.getFormData();
        IPageAdapter iPageAdapter = pageAdapterFactory.getAdapterByPage(pageName);
        Map<String, Object> applicationData = iPageAdapter.constructApplicationData(loanApplication, formData, merchantUser, applicationId);
        Map<String, VariableData> workflowData = iPageAdapter.constructWorkflowData(loanApplication, formData, merchantUser, applicationId, requestId);

        resumeApplicationRequest.setPendingTask(applicationDataResponse.getPendingTask().get(0));

        // Add application data only when previous error is not RETRY_WITHOUT_EDIT.
        // This ensures that AMS doesn't save application data again.
        if (!Status.RETRY_WITHOUT_EDIT.equals(status)) {
            resumeApplicationRequest.setApplicationData(applicationData);
        }

        resumeApplicationRequest.setWorkflowData(workflowData);
        resumeApplicationRequest.setSmUserId(merchantUser.getSmUserId());

        return resumeApplicationRequest;
    }
}
