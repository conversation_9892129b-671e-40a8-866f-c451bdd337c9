package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 30/03/21.
 */
public class LenderValidationUtils {

    private final DynamicBucket dynamicBucket;

    @Inject
    public LenderValidationUtils(DynamicBucket dynamicBucket)
    {
        this.dynamicBucket = dynamicBucket;
    }

    public boolean isValidLender(String lender)
    {
        List<String> activeLenders = dynamicBucket.getStringArray("activeLenders");
        if(Objects.nonNull(activeLenders) && !activeLenders.isEmpty())
            return activeLenders.contains(lender);
        return false;
    }
}
