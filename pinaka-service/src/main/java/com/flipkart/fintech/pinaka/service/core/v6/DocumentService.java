package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v4.kyc.UploadDocumentRequest;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentUploadResponse;
import com.flipkart.fintech.pinaka.service.core.v6.document.DocumentServiceImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;

@ImplementedBy(DocumentServiceImpl.class)
public interface DocumentService {


  void upload(MerchantUser merchantUser, String applicationRefId, UploadDocumentRequest uploadDocumentRequest)
      throws PinakaException;

  DocumentUploadResponse upload(MerchantUser merchantUser, String applicationRefId, UploadDocumentRequestV6 uploadDocumentRequest)
      throws PinakaException;

    DocumentDownloadResponse downloadDocument(String documentId) throws Exception;

}
