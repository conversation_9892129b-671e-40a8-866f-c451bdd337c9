package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.pinaka.api.model.PaOffer;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.PageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.utils.v6.FormConfig;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataPrefillUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.WidgetTransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ListFormWidgetTransformer;
import com.flipkart.fintech.pinaka.service.utils.FormUtils.QueryParamUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.UrlUtil;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.rome.datatypes.response.fintech.onboarding.FormFieldValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.cards.PrimitiveCard;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.ListWidgetData;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.commons.text.WordUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.NameValuePair;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.LEAD_V4_LANDING_PAGE;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.PA_OFFER;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4SubmitButtonWidgetTransformer.*;
import static com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4Util.getOfferAmount;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.FULL_NAME_STRING;
import static com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher.PHONE_NUMBER_STRING;

/**
 * Transformer for Lead V4 Landing Page
 * Handles dynamic content based on user name and pre-approved offer availability
 * Supports 4 content scenarios: personalized with/without offer, generic with/without offer
 * Implements proper data prefilling similar to LV3NamePageFormTransformer
 */
@CustomLog
public class LV4LandingPageTransformer implements ListFormWidgetTransformer {

    // Constants for JSON field names and structure navigation
    private static final String JSON_KEY_PAGE_RESPONSE = "pageResponse";
    private static final String JSON_KEY_SLOTS = "slots";
    private static final String JSON_KEY_WIDGET = "widget";
    private static final String JSON_KEY_DATA = "data";
    private static final String JSON_KEY_RENDERABLE_COMPONENTS = "renderableComponents";
    private static final String JSON_KEY_VALUE = "value";
    private static final String JSON_KEY_NAME = "name";
    private static final String JSON_KEY_ACCOUNT_ID = "accountId";
    private static final String JSON_KEY_APPLICATION_ID = "applicationId";
    private static final String JSON_KEY_TRACKING_CONTEXT = "trackingContext";
    private static final String JSON_KEY_TRACKING = "tracking";

    private static final String LANDING_PAGE_WITH_NAME;
    private static final String LANDING_PAGE_WITHOUT_NAME;

    private static final Integer FNAME_ARRAY_INDEX = 0;
    private static final Integer AMOUNT_ARRAY_INDEX = 1;

    static {
        LANDING_PAGE_WITH_NAME = TransformerUtils.readFileasString("template/lead/V4/LandingPageSummaryListWithName.json");
        LANDING_PAGE_WITHOUT_NAME = TransformerUtils.readFileasString("template/lead/V4/LandingPageSummaryListWithoutName.json");
    }

    private final Decrypter decrypter;
    private final DynamicBucket dynamicBucket;
    private final FormWidgetDataPrefillUtils formWidgetDataPrefillUtils;
    private final BqIngestionHelper bqIngestionHelper;
    private final FormConfig formConfig = new FormConfig("18", "90", PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.merchantSandBoxConsentMap, PageConstant.PL_BASIC_DETAILS_SCREEN_CONSENTS.consentIdSandBoxMap);
    private static final String NAME_COLOUR_KEY = "coloredName";
    private static final String AMOUNT_COLOUR_KEY = "coloredAmount";

    @Inject
    public LV4LandingPageTransformer(Decrypter decrypter, DynamicBucket dynamicBucket, FormWidgetDataPrefillUtils formWidgetDataPrefillUtils, BqIngestionHelper bqIngestionHelper) {
        this.decrypter = decrypter;
        this.dynamicBucket = dynamicBucket;
        this.formWidgetDataPrefillUtils = formWidgetDataPrefillUtils;
        this.bqIngestionHelper = bqIngestionHelper;
    }

    @Override
    public CardSummaryListWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        CardSummaryListWidgetData listWidgetData;
        try {
            //TODO: move name and phone fetch to a common method

            // Check if name and phone number are available in application data
            boolean hasNameInAppData = LV4Util.hasNameInApplicationData(applicationDataResponse);
            boolean hasPhoneInAppData = LV4Util.hasPhoneNumberInApplicationData(applicationDataResponse);

            // Only fetch leadPageDataSourceResponse if we need fallback data
            LeadPageDataSourceResponse leadPageDataSourceResponse = null;
            if (!hasNameInAppData || !hasPhoneInAppData) {
                PageDataSource<LeadPageDataSourceResponse> leadPageDataSource = new LeadPageDataSource();
                leadPageDataSourceResponse = leadPageDataSource.getData(applicationDataResponse);
                log.debug("Fetched leadPageDataSourceResponse for fallback - hasName: {}, hasPhone: {}", hasNameInAppData, hasPhoneInAppData);
            } else {
                log.debug("Skipping leadPageDataSourceResponse fetch - both name and phone available in application data");
            }

            // Get user data for prefilling using new approach with fallback
            Map<String, Object> userData = new HashMap<>();
            // Add name data with fallback
            String fullName = LV4Util.getDecryptedFullNameWithFallback(applicationDataResponse, leadPageDataSourceResponse, decrypter);
            if (StringUtils.isNotBlank(fullName)) {
                userData.put(FULL_NAME_STRING, fullName);
            }
            // Add phone number data with fallback
            String phoneNumber = LV4Util.getPhoneNumberWithFallback(applicationDataResponse, leadPageDataSourceResponse);
            if (StringUtils.isNotBlank(phoneNumber)) {
                userData.put(PHONE_NUMBER_STRING, phoneNumber);
            }

            // Derive content scenario from user name - don't store it anywhere
            String templateToUse = deriveTemplate((String) userData.get(FULL_NAME_STRING));

            // Use the same approach as LV3 - simple and straightforward
            listWidgetData = ObjectMapperUtil.get().readValue(getFormJson(templateToUse, applicationDataResponse), CardSummaryListWidgetData.class);

            // Get query params directly from applicationDataResponse (taskKey, processInstanceId, etc.)
            List<NameValuePair> queryParams = UrlUtil.getQueryParams(applicationDataResponse);
            Map<String, Object> queryParamsMap = QueryParamUtils.getQueryParams(queryParams);

            // Customize the template based on content scenario and user data (includes form field prefilling)
            customizeTemplateWithUserData(listWidgetData, userData, applicationDataResponse);

            // Set up tracking data for renderable components
            setupTrackingDataForRenderableComponents(listWidgetData, userData);

            // Update action params with query params (taskKey, processInstanceId, etc.)
            updateActionParamsWithQueryParams(listWidgetData, queryParamsMap);

            // Update tracking data with correct accountId and applicationId
            updateTrackingData(listWidgetData, applicationDataResponse);

            // Log BQ events
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, LEAD_V4_LANDING_PAGE, applicationDataResponse.getApplicationState(), "V4"));
        } catch (Exception e) {
            this.bqIngestionHelper.insertLeadEvents(LV3Util.getLeadEvents(applicationDataResponse, "ERROR_WHILE_BUILDING_PAGE", applicationDataResponse.getApplicationState(), "V4"));
            throw new PinakaException("Error while building widget Group Data for LV4 Landing Page for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        // Cast ListWidgetData to GroupedFormWidgetData since they share the same interface
        return listWidgetData;
    }

    private String deriveTemplate(String firstName) {
        // Determine scenario based on name and offer availability
        if (LV4Util.isNameEmpty(firstName)) {
            return LANDING_PAGE_WITHOUT_NAME;
        } else {
            return LANDING_PAGE_WITH_NAME;
        }
    }

    /**
     * Customize template with actual user data and scenario-specific content
     * This method updates the JSON structure directly with prefilled data and form fields
     */
    private void customizeTemplateWithUserData(ListWidgetData<PrimitiveCard> listWidgetData, Map<String, Object> userData, ApplicationDataResponse applicationDataResponse) {
        try {
            // Update user name in the template if available
            String fullName = (String) userData.get(FULL_NAME_STRING);
            String firstName = getFirstName(fullName);
            if (firstName != null && !firstName.trim().isEmpty()) {
                updateUserNameInTemplate(listWidgetData, firstName);

                // Only prefill form fields when name is available
                prefillFormFieldsInTemplate(listWidgetData, userData);


            }

            // Update offer amount if available (regardless of name availability)
            updateOfferAmountInTemplate(listWidgetData, applicationDataResponse);

            log.info("Successfully customized template for user: {}", applicationDataResponse.getSmUserId());
        } catch (Exception e) {
            log.error("Error customizing template for user: {}", applicationDataResponse.getSmUserId(), e);
            // Don't throw exception, just log and continue with default template
        }
    }

    /**
     * Update user name in the template structure
     * For now, just log the user name - template modification will be implemented later
     */
    private void updateUserNameInTemplate(ListWidgetData<PrimitiveCard> listWidgetData, String firstName) {
        log.info("User name to display in template: {}", firstName);

        if (canUpdateUserNameInTemplate(listWidgetData, firstName)) {
            listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle().getValue().setText("Congrats " + firstName + ",");

            if (hasSpecialTextsMapper(listWidgetData, FNAME_ARRAY_INDEX, NAME_COLOUR_KEY)) {
                TextStyle colour = listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle().getValue().getSpecialTextsMapper().get(NAME_COLOUR_KEY);
                listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle().getValue().getSpecialTextsMapper().put(firstName + ",", colour);
                listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle().getValue().getSpecialTextsMapper().remove(NAME_COLOUR_KEY);
            }
        }
    }

    /**
     * Check if the user name can be updated in the template
     * Combines all validation checks for better testability
     */
    boolean canUpdateUserNameInTemplate(ListWidgetData<PrimitiveCard> listWidgetData, String firstName) {
        return isValidFirstName(firstName) && hasRenderableComponents(listWidgetData);
    }

    /**
     * Check if the first name is valid (not blank)
     */
    boolean isValidFirstName(String firstName) {
        return StringUtils.isNotBlank(firstName);
    }

    /**
     * Check if the widget data has renderable components that are not empty
     */
    boolean hasRenderableComponents(ListWidgetData<PrimitiveCard> listWidgetData) {
        return listWidgetData.getRenderableComponents() != null &&
               !listWidgetData.getRenderableComponents().isEmpty();
    }

    /**
     * Check if the special texts mapper exists and contains the NAME_COLOUR_KEY
     */
    boolean hasSpecialTextsMapper(ListWidgetData<PrimitiveCard> listWidgetData, int index, String key) {
        try {
            return listWidgetData.getRenderableComponents().get(index).getValue().getTitle().getValue().getSpecialTextsMapper() != null &&
                   listWidgetData.getRenderableComponents().get(index).getValue().getTitle().getValue().getSpecialTextsMapper().containsKey(key);
        } catch (IndexOutOfBoundsException | NullPointerException e) {
            return false;
        }
    }

    private void updateOfferAmountInTemplate(ListWidgetData<PrimitiveCard> listWidgetData, ApplicationDataResponse applicationDataResponse) {
       // Get PA offer from application data
       PaOffer paOffer = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get(PA_OFFER), PaOffer.class);
       LV4Util.ApprovedAmount result = getOfferAmount(applicationDataResponse, paOffer);
       String formattedAmount = "₹" + LV4Util.formatNumber(result.getAmount());
       log.info("PA offer amount to display in template: {}", formattedAmount);

       if (canUpdateOfferAmount(listWidgetData)) {
           listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue().setText(formattedAmount + (result.isAppendUpto() ? " *" : ""));

           if (hasSpecialTextsMapper(listWidgetData, AMOUNT_ARRAY_INDEX, AMOUNT_COLOUR_KEY)) {
               TextStyle colour = listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue().getSpecialTextsMapper().get(AMOUNT_COLOUR_KEY);
               listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue().getSpecialTextsMapper().put(formattedAmount, colour);
               listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue().getSpecialTextsMapper().remove(AMOUNT_COLOUR_KEY);
           }
       }

       if (canUpdateDescription(listWidgetData)) {
           String subtitleText = listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription().getValue().getText();
           if (!result.isAppendUpto() && subtitleText != null) {
               subtitleText = subtitleText.replace(" upto", "");
           }
           listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription().getValue().setText(subtitleText);
       }
    }

    /**
     * Check if the offer amount can be updated in the template
     * Validates the amount component structure at AMOUNT_ARRAY_INDEX
     */
    private boolean canUpdateOfferAmount(ListWidgetData<PrimitiveCard> listWidgetData) {
        return hasRenderableComponents(listWidgetData) &&
               hasValidAmountComponent(listWidgetData);
    }

    /**
     * Check if the amount component at AMOUNT_ARRAY_INDEX has valid structure
     */
    boolean hasValidAmountComponent(ListWidgetData<PrimitiveCard> listWidgetData) {
        try {
            return listWidgetData.getRenderableComponents().size() > AMOUNT_ARRAY_INDEX &&
                   listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue() != null &&
                   listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle().getValue() != null;
        } catch (IndexOutOfBoundsException e) {
            return false;
        }
    }

    /**
     * Check if the description can be updated in the template
     * Validates the description component structure at FNAME_ARRAY_INDEX
     */
    private boolean canUpdateDescription(ListWidgetData<PrimitiveCard> listWidgetData) {
        return hasRenderableComponents(listWidgetData) &&
               hasValidDescriptionComponent(listWidgetData);
    }

    /**
     * Check if the description component at FNAME_ARRAY_INDEX has valid structure
     */
    private boolean hasValidDescriptionComponent(ListWidgetData<PrimitiveCard> listWidgetData) {
        try {
            return listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue() != null &&
                   listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription() != null &&
                   listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getDescription().getValue() != null;
        } catch (IndexOutOfBoundsException e) {
            return false;
        }
    }

    /**
     * Set up tracking data for renderable components
     * Similar to setupPrefillValueAndTrackingDataForGroupedFormField but adapted for LV4 structure
     */
    private void setupTrackingDataForRenderableComponents(ListWidgetData<PrimitiveCard> listWidgetData, Map<String, Object> userData) {
        try {
            if (!hasRenderableComponents(listWidgetData)) {
                log.debug("No renderable components found for tracking data setup");
                return;
            }

            // Set tracking data for name component (first component)
            setupTrackingDataForNameComponent(listWidgetData, userData);

            // Set tracking data for amount component (second component)
            setupTrackingDataForAmountComponent(listWidgetData);

            log.info("Successfully set up tracking data for renderable components");
        } catch (Exception e) {
            log.error("Error setting up tracking data for renderable components", e);
            // Don't throw exception, just log and continue
        }
    }

    /**
     * Set up tracking data for the name component (first renderable component)
     */
    private void setupTrackingDataForNameComponent(ListWidgetData<PrimitiveCard> listWidgetData, Map<String, Object> userData) {
        try {
            String firstName = getFirstName((String) userData.get(FULL_NAME_STRING));
            String prefilledValue = firstName != null ? firstName : "";

            // Set tracking data for the title
            listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX).getValue().getTitle()
                    .setTracking(WidgetTransformerUtils.getTrackingMetadataForGroupFields("userName", prefilledValue));
            log.debug("Set tracking data for name component with value: {}", prefilledValue);
        } catch (Exception e) {
            log.error("Error setting tracking data for name component", e);
        }
    }

    private String getFirstName(String s) {
        return WordUtils.capitalizeFully(s.trim().split("\\s+")[0].toLowerCase());
    }

    /**
     * Set up tracking data for the amount component (second renderable component)
     */
    private void setupTrackingDataForAmountComponent(ListWidgetData<PrimitiveCard> listWidgetData) {
        try {
            if (hasValidAmountComponent(listWidgetData)) {
                // Set tracking data for the amount title
                listWidgetData.getRenderableComponents().get(AMOUNT_ARRAY_INDEX).getValue().getTitle()
                        .setTracking(WidgetTransformerUtils.getTrackingMetadataForGroupFields("offerAmount", ""));
                log.debug("Set tracking data for amount component");
            }
        } catch (Exception e) {
            log.error("Error setting tracking data for amount component", e);
        }
    }

    /**
     * Prefill form fields in the nested JSON structure similar to LV4SubmitButtonWidgetTransformer
     * This method extracts form fields from the template, prefills them, and updates them back
     */
    private void prefillFormFieldsInTemplate(ListWidgetData<PrimitiveCard> listWidgetData, Map<String, Object> userData) {
        try {
            // Extract form field maps from the nested JSON structure
            Map<String, Map<String, Object>> formFieldValueMapRaw = extractFormFieldValueMapFromTemplate(listWidgetData);

            if (formFieldValueMapRaw.isEmpty()) {
                log.debug("No form fields found in template for prefilling");
                return;
            }

            // Convert raw map to FormFieldValue map for LV3 utilities
            Map<String, FormFieldValue> formFieldValueMapToPrefill = convertToFormFieldValueMap(formFieldValueMapRaw);

            // Use LV3 utilities to prefill form fields
            this.formWidgetDataPrefillUtils.prefillFormFieldValues(formFieldValueMapToPrefill, userData);

            // Update the form fields back in the template with prefilled values
            updateFormFieldsInTemplate(listWidgetData, formFieldValueMapToPrefill, formFieldValueMapRaw);

            // Set up tracking data for form fields
            setupTrackingDataForFormFieldsInTemplate(listWidgetData, formFieldValueMapToPrefill);

            log.info("Successfully prefilled form fields in template");
        } catch (Exception e) {
            log.error("Error prefilling form fields in template", e);
            // Don't throw exception, just log and continue
        }
    }

    /**
     * Extract form field value map from the nested JSON structure in the template
     * Similar to extractLV4FormFieldValueMap in LV4SubmitButtonWidgetTransformer
     */
    private Map<String, Map<String, Object>> extractFormFieldValueMapFromTemplate(ListWidgetData<PrimitiveCard> listWidgetData) {
        Map<String, Map<String, Object>> formFieldValueMap = new HashMap<>();

        try {
            // Navigate through the nested structure to find form fields
            // The structure is: listWidgetData -> renderableComponents[0] -> value -> title -> value -> suffix -> action -> params -> pageResponse
            if (hasRenderableComponents(listWidgetData)) {
                Map<String, Object> params = getActionParamsFromFirstComponent(listWidgetData);
                if (params != null) {
                    Map<String, Object> pageResponse = (Map<String, Object>) params.get(JSON_KEY_PAGE_RESPONSE);
                    if (pageResponse != null) {
                        extractFormFieldsFromPageResponse(pageResponse, formFieldValueMap);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting form fields from template", e);
        }

        return formFieldValueMap;
    }

    /**
     * Get action params from the first renderable component
     */
    private Map<String, Object> getActionParamsFromFirstComponent(ListWidgetData<PrimitiveCard> listWidgetData) {
        try {
            return listWidgetData.getRenderableComponents().get(FNAME_ARRAY_INDEX)
                    .getValue().getTitle().getValue().getSuffix().getAction().getParams();
        } catch (Exception e) {
            log.debug("Could not extract action params from first component", e);
            return null;
        }
    }

    /**
     * Extract form fields from page response structure
     * Same logic as in LV4SubmitButtonWidgetTransformer
     */
    private void extractFormFieldsFromPageResponse(Map<String, Object> pageResponse,
                                                 Map<String, Map<String, Object>> formFieldValueMap) {
        List<Map<String, Object>> slots = (List<Map<String, Object>>) pageResponse.get(JSON_KEY_SLOTS);

        if (slots != null) {
            for (Map<String, Object> slot : slots) {
                Map<String, Object> widget = (Map<String, Object>) slot.get(JSON_KEY_WIDGET);
                if (widget != null) {
                    Map<String, Object> data = (Map<String, Object>) widget.get(JSON_KEY_DATA);
                    if (data != null) {
                        extractRenderableComponentFields(data, formFieldValueMap);
                    }
                }
            }
        }
    }

    /**
     * Extract renderable component fields for enum-based prefilling
     * Same logic as in LV4SubmitButtonWidgetTransformer
     */
    // TODO: move this out of transformer
    private void extractRenderableComponentFields(Map<String, Object> data,
                                                Map<String, Map<String, Object>> formFieldValueMap) {
        List<Map<String, Object>> renderableComponents = (List<Map<String, Object>>) data.get(JSON_KEY_RENDERABLE_COMPONENTS);

        if (renderableComponents != null && !renderableComponents.isEmpty()) {
            for (Map<String, Object> renderableComponent : renderableComponents) {
                Map<String, Object> value = (Map<String, Object>) renderableComponent.get(JSON_KEY_VALUE);

                if (value != null && value.get(JSON_KEY_NAME) != null) {
                    String fieldName = (String) value.get(JSON_KEY_NAME);
                    formFieldValueMap.put(fieldName, value);
                }
            }
        }
    }

    /**
     * Convert raw form field map to FormFieldValue map for LV3 utilities
     * Same logic as in LV4SubmitButtonWidgetTransformer
     */
    private Map<String, FormFieldValue> convertToFormFieldValueMap(Map<String, Map<String, Object>> formFieldValueMapRaw) {
        Map<String, FormFieldValue> formFieldValueMap = new HashMap<>();

        for (Map.Entry<String, Map<String, Object>> entry : formFieldValueMapRaw.entrySet()) {
            String fieldName = entry.getKey();
            Map<String, Object> valueMap = entry.getValue();

            try {
                // Create a FormFieldValue from the raw map
                FormFieldValue formFieldValue = ObjectMapperUtil.get().convertValue(valueMap, FormFieldValue.class);
                formFieldValueMap.put(fieldName, formFieldValue);
            } catch (Exception e) {
                log.warn("Failed to convert field {} to FormFieldValue: {}", fieldName, e.getMessage());
            }
        }

        return formFieldValueMap;
    }

    /**
     * Update form fields in template with prefilled values
     * Similar to updateLV4FormFieldsWithPrefilled in LV4SubmitButtonWidgetTransformer
     */
    private void updateFormFieldsInTemplate(ListWidgetData<PrimitiveCard> listWidgetData,
                                          Map<String, FormFieldValue> formFieldValueMapToPrefill,
                                          Map<String, Map<String, Object>> formFieldValueMapRaw) {
        try {
            // Update the raw map with prefilled values
            for (Map.Entry<String, FormFieldValue> entry : formFieldValueMapToPrefill.entrySet()) {
                String fieldName = entry.getKey();
                FormFieldValue prefilledValue = entry.getValue();

                if (formFieldValueMapRaw.containsKey(fieldName) && prefilledValue.getValue() != null) {
                    Map<String, Object> rawValueMap = formFieldValueMapRaw.get(fieldName);
                    rawValueMap.put(JSON_KEY_VALUE, prefilledValue.getValue());
                    log.debug("Updated template field {} with prefilled value: {}", fieldName, prefilledValue.getValue());
                }
            }

            log.info("Successfully updated template form fields with prefilled values");
        } catch (Exception e) {
            log.error("Error updating template form fields with prefilled values", e);
        }
    }

    /**
     * Set up tracking data for form fields in template
     * Updates tracking data directly in the raw template structure
     */
    private void setupTrackingDataForFormFieldsInTemplate(ListWidgetData<PrimitiveCard> listWidgetData,
                                                         Map<String, FormFieldValue> formFieldValueMapToPrefill) {
        try {
            // Extract the raw form field map from template structure
            Map<String, Map<String, Object>> formFieldValueMapRaw = extractFormFieldValueMapFromTemplate(listWidgetData);

            // Set tracking data for each form field directly in the raw structure
            for (Map.Entry<String, FormFieldValue> entry : formFieldValueMapToPrefill.entrySet()) {
                String fieldName = entry.getKey();
                FormFieldValue formFieldValue = entry.getValue();

                String prefilledValue = formFieldValue.getValue() != null ? formFieldValue.getValue().toString() : "";

                // Update tracking data in the raw template structure
                if (formFieldValueMapRaw.containsKey(fieldName)) {
                    Map<String, Object> rawValueMap = formFieldValueMapRaw.get(fieldName);
                    setupTrackingDataForFormField(rawValueMap, fieldName, prefilledValue);
                    log.debug("Set tracking data for template field: {} with prefilled value: {}", fieldName, prefilledValue);
                }
            }

            log.info("Successfully set up tracking data for form fields in template");
        } catch (Exception e) {
            log.error("Error setting up tracking data for form fields in template", e);
        }
    }

    /**
     * Set up tracking data for a single form field in the raw template structure
     * Similar to LV4SubmitButtonWidgetTransformer approach
     */
    private void setupTrackingDataForFormField(Map<String, Object> valueMap, String fieldName, String prefilledValue) {
        try {
            // Set tracking data using WidgetTransformerUtils
            Map<String, Object> trackingData = WidgetTransformerUtils.getTrackingMetadata(fieldName, prefilledValue);
            valueMap.put("tracking", trackingData);

            log.debug("Set tracking data for field: {} with prefilled value: {}", fieldName, prefilledValue);
        } catch (Exception e) {
            log.error("Error setting tracking data for field: {}", fieldName, e);
        }
    }







    /**
     * Get form configuration for template substitution
     * Uses the same FormConfig as LV3 for consistency
     */
    private String getFormJson(String template, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> formConfigMap = formConfig.getFormConfigMap(applicationDataResponse.getMerchantId(), dynamicBucket);
        StringSubstitutor sub = new StringSubstitutor(formConfigMap);
        return sub.replace(template);
    }

    /**
     * Updates action params with query params (taskKey, processInstanceId, etc.)
     * Similar to LV4SubmitButtonWidgetTransformer's approach of merging query params
     */
    private void updateActionParamsWithQueryParams(ListWidgetData<PrimitiveCard> listWidgetData,
                                                  Map<String, Object> queryParamsMap) {
        try {
            if (!hasRenderableComponents(listWidgetData) || queryParamsMap == null) {
                log.debug("Cannot update action params - missing components or query params");
                return;
            }

            // Update top-level action params
            Map<String, Object> params = getActionParamsFromFirstComponent(listWidgetData);
            if (params != null && !queryParamsMap.isEmpty()) {
                // Merge query params with existing params without overwriting pageResponse
                params.putAll(queryParamsMap);
                log.info("Successfully updated top-level action params with query params");

                // Also update nested submit button params in pageResponse.slots[0].widget.data.submitButton.button.action.params
                updateNestedSubmitButtonParams(params, queryParamsMap);
            } else {
                log.warn("Could not update action params - params is null or queryParams is empty");
            }
        } catch (Exception e) {
            log.error("Error updating action params with query params", e);
            // Don't throw exception, just log and continue
        }
    }

    /**
     * Updates nested submit button params in pageResponse.slots[0].widget.data.submitButton.button.action.params
     */
    private void updateNestedSubmitButtonParams(Map<String, Object> topLevelParams, Map<String, Object> queryParamsMap) {
        try {
            Object pageResponse = topLevelParams.get(JSON_KEY_PAGE_RESPONSE);
            if (pageResponse instanceof Map) {
                Map<String, Object> pageResponseMap = (Map<String, Object>) pageResponse;
                Object slots = pageResponseMap.get(JSON_KEY_SLOTS);
                if (slots instanceof List) {
                    List<Object> slotsList = (List<Object>) slots;
                    if (!slotsList.isEmpty() && slotsList.get(0) instanceof Map) {
                        Map<String, Object> firstSlot = (Map<String, Object>) slotsList.get(0);
                        Object widget = firstSlot.get(JSON_KEY_WIDGET);
                        if (widget instanceof Map) {
                            Map<String, Object> widgetMap = (Map<String, Object>) widget;
                            Object data = widgetMap.get(JSON_KEY_DATA);
                            if (data instanceof Map) {
                                Map<String, Object> dataMap = (Map<String, Object>) data;
                                Object submitButton = dataMap.get(JSON_KEY_SUBMIT_BUTTON);
                                if (submitButton instanceof Map) {
                                    Map<String, Object> submitButtonMap = (Map<String, Object>) submitButton;
                                    Object button = submitButtonMap.get(JSON_KEY_BUTTON);
                                    if (button instanceof Map) {
                                        Map<String, Object> buttonMap = (Map<String, Object>) button;
                                        Object action = buttonMap.get(JSON_KEY_ACTION);
                                        if (action instanceof Map) {
                                            Map<String, Object> actionMap = (Map<String, Object>) action;
                                            Object params = actionMap.get(JSON_KEY_PARAMS);
                                            if (params instanceof Map) {
                                                Map<String, Object> submitButtonParams = (Map<String, Object>) params;
                                                // Merge query params with existing submit button params
                                                submitButtonParams.putAll(queryParamsMap);
                                                log.info("Successfully updated nested submit button params with query params - taskKey: {}, processInstanceId: {}, applicationId: {}",
                                                        queryParamsMap.get("taskKey"), queryParamsMap.get("processInstanceId"), queryParamsMap.get("applicationId"));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error updating nested submit button params", e);
            // Don't throw exception, just log and continue
        }
    }

    /**
     * Updates tracking data with correct accountId and applicationId
     * Similar to LV4SubmitButtonWidgetTransformer's updateTrackingData method
     */
    private void updateTrackingData(ListWidgetData<PrimitiveCard> listWidgetData,
                                   ApplicationDataResponse applicationDataResponse) {
        try {
            // Update tracking data in action params (if present in the first component)
            if (hasRenderableComponents(listWidgetData)) {
                Map<String, Object> params = getActionParamsFromFirstComponent(listWidgetData);
                if (params != null) {
                    updateTrackingInParams(params, applicationDataResponse);

                    // Update tracking data in page response structure
                    Map<String, Object> pageResponse = (Map<String, Object>) params.get(JSON_KEY_PAGE_RESPONSE);
                    if (pageResponse != null) {
                        updateTrackingInPageResponse(pageResponse, applicationDataResponse);
                    }
                }
            }

            log.info("Successfully updated tracking data for LV4 Landing Page - accountId: {}, applicationId: {}",
                    applicationDataResponse.getExternalUserId(),
                    applicationDataResponse.getApplicationId());
        } catch (Exception e) {
            log.error("Error updating tracking data for LV4 Landing Page", e);
            // Don't throw exception, just log and continue
        }
    }

    /**
     * Updates tracking data in action params
     * Similar to LV4SubmitButtonWidgetTransformer's updateTrackingInParams method
     */
    private void updateTrackingInParams(Map<String, Object> params, ApplicationDataResponse applicationDataResponse) {
        // Update applicationId in params if it exists
        if (params.containsKey(JSON_KEY_APPLICATION_ID)) {
            params.put(JSON_KEY_APPLICATION_ID, applicationDataResponse.getApplicationId());
            log.debug("Updated applicationId in params: {}", applicationDataResponse.getApplicationId());
        }
    }

    /**
     * Updates tracking data in page response structure
     * Similar to LV4SubmitButtonWidgetTransformer's updateTrackingInPageResponse method
     */
    private void updateTrackingInPageResponse(Map<String, Object> pageResponse, ApplicationDataResponse applicationDataResponse) {
        Map<String, Object> pageData = (Map<String, Object>) pageResponse.get("pageData");
        if (pageData != null) {
            Map<String, Object> trackingContext = (Map<String, Object>) pageData.get(JSON_KEY_TRACKING_CONTEXT);
            if (trackingContext != null) {
                Map<String, Object> tracking = (Map<String, Object>) trackingContext.get(JSON_KEY_TRACKING);
                if (tracking != null) {
                    // Update accountId and applicationId in tracking
                    tracking.put(JSON_KEY_ACCOUNT_ID, applicationDataResponse.getExternalUserId());
                    tracking.put(JSON_KEY_APPLICATION_ID, applicationDataResponse.getApplicationId());
                    log.debug("Updated tracking data in pageResponse - accountId: {}, applicationId: {}",
                            applicationDataResponse.getExternalUserId(),
                            applicationDataResponse.getApplicationId());
                } else {
                    log.warn("Tracking object not found in trackingContext");
                }
            } else {
                log.warn("TrackingContext not found in pageData");
            }
        } else {
            log.warn("PageData not found in pageResponse");
        }
    }
}
