package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.KfsRequest;
import com.flipkart.fintech.pinaka.api.response.v6.KfsResponse;
import com.flipkart.fintech.pinaka.api.response.v6.StatusResponse;
import com.flipkart.fintech.pinaka.api.response.v6.ApplicationStatusResponse;
import com.flipkart.fintech.pinaka.service.core.v6.BulkDataRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.v6.MerchantUserUtils;
import io.swagger.annotations.ApiOperation;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl")
public class BulkDataResource {

    private final BulkDataRequestHandler bulkDataRequestHandler;

    @Inject
    public BulkDataResource(BulkDataRequestHandler bulkDataRequestHandler) {
        this.bulkDataRequestHandler = bulkDataRequestHandler;
    }

    @POST
    @ApiOperation(value = "Fetch Kfs details", response = KfsResponse.class)
    @Timed
    @ExceptionMetered
    @Path("/fetch-kfs")
    public KfsResponse getKfsDetails(@Valid KfsRequest kfsRequest,
                                     @NotNull @HeaderParam("X-Request-Id") String requestId,
                                     @NotNull @HeaderParam("X-Merchant-Id") String merchantId) throws PinakaException {

        log.info("kfs Request for application Id- {}", kfsRequest.getLspApplicationId());
        KfsResponse kfsResponse = bulkDataRequestHandler.getKfsDetails(kfsRequest, merchantId, requestId);
        log.info("kfs Response - {}", kfsResponse);

        return kfsResponse;
    }

    @GET
    @ApiOperation(value = "Get Status details", response = StatusResponse.class)
    @Timed
    @ExceptionMetered
    @Path("/fetch-status/application-id/{applicationId}")
    public StatusResponse getStatus(@PathParam("applicationId") String applicationId,
                                    @NotNull @HeaderParam("X-Request-Id") String requestId,
                                    @NotNull @HeaderParam("X-Account-Id") String accountId) throws Exception {
        log.info("Get Status Request for application Id- {}, account Id- {}", applicationId, accountId);
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUserFromRequestContext();
        StatusResponse statusResponse = bulkDataRequestHandler.
                getStatus(applicationId, requestId, merchantUser);
        log.info("Get Status Response - {}", statusResponse);

        return statusResponse;
    }

    @GET
    @ApiOperation(value = "Get Status details", response = StatusResponse.class)
    @Timed
    @ExceptionMetered
    @Path("/get-application-status/application-id/{applicationId}")
    public ApplicationStatusResponse getStatusAllLenders(@PathParam("applicationId") String applicationId,
                                    @NotNull @HeaderParam("X-Request-Id") String requestId,
                                    @NotNull @HeaderParam("X-Account-Id") String accountId) throws Exception {
        log.info("Get Status Request for application Id- {}, account Id- {}", applicationId, accountId);
        MerchantUser merchantUser = MerchantUserUtils.getMerchantUserFromRequestContext();
        ApplicationStatusResponse statusResponse = bulkDataRequestHandler.
                getApplicationStatus(applicationId, requestId, merchantUser);
        log.info("Get Status Response - {}", statusResponse);

        return statusResponse;
    }
}
