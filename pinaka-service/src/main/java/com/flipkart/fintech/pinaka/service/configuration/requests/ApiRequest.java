package com.flipkart.fintech.pinaka.service.configuration.requests;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.rome.datatypes.request.page.action.v1.ActionRequestContext;
import lombok.Builder;
import com.sumo.bff.models.common.UserRequestContext;
import lombok.Getter;

@Getter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiRequest {
    private String dataApiName;
    private ActionRequestContext actionRequestContext;
    private UserRequestContext userRequestContext;
    private Object payload;
}