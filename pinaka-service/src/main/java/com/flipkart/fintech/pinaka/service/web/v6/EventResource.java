package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;
import com.flipkart.fintech.pinaka.api.request.v6.AlmDiscardRequest;
import com.flipkart.fintech.pinaka.api.request.v6.AxisWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.MigrateApplicationsRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SandboxWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.StateChangeRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SubmitRequest;
import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.SearchResponse;
import com.flipkart.fintech.pinaka.api.response.v6.StateChangeResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Status;
import com.flipkart.fintech.pinaka.api.response.v6.WebhooksResponse;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.EventHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;
import com.google.inject.Inject;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.CustomLog;
import org.apache.http.HttpStatus;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl")
public class EventResource {

    private final EventHandler eventHandler;

    @Inject
    public EventResource(EventHandler eventHandler) {
        this.eventHandler = eventHandler;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Personal Loan Page Submit")
    @Path("/submit-event")
    @UnitOfWork
    public PageActionResponse submitUserAction(@Valid SubmitRequest submitRequest,
                                               @NotNull @HeaderParam("X-Request-Id") String requestId,
                                               @NotNull @HeaderParam("X-Merchant-Id") String merchantId) {
        PageActionResponse pageActionResponse = new PageActionResponse();
        try {
            log.info("Submit User Action Request received : {}", ObjectMapperUtil.get().writeValueAsString(submitRequest));
            log.info("Submit User Action Request received for applicationId: {}, accountId: {}, taskId: {}",
                    submitRequest.getApplicationId(), submitRequest.getAccountId(), submitRequest.getTaskId());
            pageActionResponse = eventHandler.submitUserAction(submitRequest, merchantId, requestId);
        } catch (PinakaException ex) {
            log.error("Error while submitting the page for applicationId: {}, accountId: {}: ", submitRequest.getAccountId(),
                    submitRequest.getTaskId(), ex);
            PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_submitUserAction_RETRY_WITHOUT_EDIT").mark();
            pageActionResponse.setActionSuccess(false);
            ErrorOperation errorOperation = new ErrorOperation();
            errorOperation.setMessage(Status.RETRY_WITHOUT_EDIT + PinakaConstants.PIPE_SEPARATOR + PinakaConstants.PLConstants.RETRY_WITHOUT_EDIT_MESSAGE);
            pageActionResponse.setError(errorOperation);
        } catch (JsonProcessingException e) {
            log.error("Error while serializing the Submit User Action Request ", e);
            pageActionResponse.setActionSuccess(false);
            ErrorOperation errorOperation = new ErrorOperation();
            errorOperation.setMessage(Status.RETRY_WITHOUT_EDIT + PinakaConstants.PIPE_SEPARATOR + PinakaConstants.PLConstants.RETRY_WITHOUT_EDIT_MESSAGE);
            pageActionResponse.setError(errorOperation);
            PinakaMetricRegistry.getMetricRegistry().meter(this.getClass().getName()+"_submitUserAction_RETRY_WITHOUT_EDIT_JsonProcessingException").mark();
        }

        return pageActionResponse;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Personal Loan Webhooks")
    @Path("/submit-lender-event")
    public WebhooksResponse submitLenderEvent(@Valid AxisWebhooksRequest axisWebhooksRequest,
                                              @NotNull @HeaderParam("X-Request-Id") String requestId) {
        log.info("Submit Lender Event Request received for webhooksRequest: {}",
                axisWebhooksRequest);

        return eventHandler.submitLenderEvent(axisWebhooksRequest, requestId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Personal Loan webhooks")
    @Path("/submit-sandbox-lender-event")
    public WebhooksResponse submitSandboxLenderEvent(@Valid SandboxWebhooksRequest sandboxWebhooksRequest) {
        log.info("Submit Lender Event Request received for webhooksRequest: {}",
                sandboxWebhooksRequest);
        return eventHandler.submitSandboxLenderEvent(sandboxWebhooksRequest);
    }


    @GET
    @Path("/get-pl-employer-suggestions")
    @ApiOperation(value = "Get Employer Name Suggestions for PL")
    @ApiResponses({
            @ApiResponse(code = HttpStatus.SC_OK, message = "success")
    })
    @Timed
    @ExceptionMetered
    public Response getPlEmployerSuggestions(@NotNull @QueryParam("prefix") String prefix) {
        try {
            SearchResponse searchResponse = eventHandler.getEmpSuggestions(prefix);
            log.info("getEmployerNameSuggestions() | response {} ", searchResponse);

            return Response.status(Response.Status.OK).entity(searchResponse).build();
        } catch (Exception e) {
            log.error("getEmployerNameSuggestions() | prefix {} | Exception {} ", prefix, e.getMessage());
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    @GET
    @Path("/employer-suggestions")
    @ApiOperation(value = "Get Employer Name Suggestions")
    @ApiResponses({
            @ApiResponse(code = HttpStatus.SC_OK, message = "success")
    })
    @Timed
    @ExceptionMetered
    public Response getEmployerSuggestions(@NotNull @QueryParam("prefix") String prefix) {
        try {
            SearchResponse searchResponse = eventHandler.getEmpSuggestionV2(prefix);
            log.info("getEmployerSuggestions() | response {} ", searchResponse);
            return Response.status(Response.Status.OK).entity(searchResponse).build();
        } catch (Exception e) {
            log.error("getEmployerSuggestions() | prefix {} | Exception {} ", prefix, e.getMessage());
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    @POST
    @Path("/has-state-changed")
    @ApiOperation(value = "State changed check API")
    @ApiResponses({
            @ApiResponse(code = HttpStatus.SC_OK, message = "success")
    })
    @Timed
    @ExceptionMetered
    public Response hasStateChanged(@Valid StateChangeRequest request,
                                    @NotNull @HeaderParam("X-Request-Id") String requestId) {
        try {
            StateChangeResponse response = eventHandler.hasStateChanged(request);
            log.debug("hasStateChanged(), response {} ", response);
            return Response.status(Response.Status.OK).entity(response).build();
        } catch (Exception e) {
            log.error("hasStateChanged(), Exception {} ", e.getMessage(), e);
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    @POST
    @Path("/ifsc-search")
    @ApiOperation(value = "Get IFSC response for PL")
    @ApiResponses({
            @ApiResponse(code = HttpStatus.SC_OK, message = "success")
    })
    @Timed
    @ExceptionMetered
    public Response searchIfsc(@Valid IfscSearchRequest request,
                               @NotNull @HeaderParam("X-Request-Id") String requestId) {
        try {
            IfscSearchResponse response = eventHandler.searchIfsc(request);
            log.debug("searchIfsc(), response {} ", response);
            return Response.status(Response.Status.OK).entity(response).build();
        } catch (Exception e) {
            log.error("searchIfsc(), Exception {} ", e.getMessage(), e);
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    @POST
    @Path("/migrate")
    @UnitOfWork(value="profile_service")
    @ApiOperation(value = "migrate applications")
    @ApiResponses(value = {@ApiResponse(code = HttpStatus.SC_OK, message = "migrate applications", response = Response.class)})
    @Timed
    @ExceptionMetered
    public Response migrateApplications(@ApiParam(value = "X-Tenant-Id", required = true)
                                        @NotNull @HeaderParam("X-Tenant-Id") String tenantId,
                                        MigrateApplicationsRequest migrateApplicationsRequest) {
        return Response.ok().build();
    }

    @POST
    @Path("/alm-discard")
    @ApiOperation(value = "alm discard applications")
    @ApiResponses(value = {@ApiResponse(code = HttpStatus.SC_OK, message = "alm discard applications", response = Response.class)})
    @Timed
    @ExceptionMetered
    public Response almDiscard(@ApiParam(value = "X-Tenant-Id", required = true)
    @NotNull @HeaderParam("X-Tenant-Id") String tenantId,
                                        AlmDiscardRequest almDiscardRequest) {
        try {
            eventHandler.almDiscard(almDiscardRequest);
        } catch (PinakaException e) {
            log.error("Error in migrating applications for tenant: {}, error: {}", tenantId, e.getMessage());
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return Response.ok().build();
    }
}
