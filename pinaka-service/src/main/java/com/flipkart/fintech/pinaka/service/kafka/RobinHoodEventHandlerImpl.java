package com.flipkart.fintech.pinaka.service.kafka;


import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.robinhood.api.model.personalLoan.Cohort;
import com.flipkart.affordability.robinhood.api.model.personalLoan.JourneyStatus;
import com.flipkart.affordability.robinhood.api.model.personalLoan.Lender;
import com.flipkart.affordability.robinhood.api.model.personalLoan.PersonalLoanUserDetailsV2;
import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;

import com.flipkart.fintech.pinaka.api.request.v6.LenderDetails;
import com.flipkart.fintech.pinaka.api.request.v6.LoanApplication;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.client.VaradhiClentConfig;

import com.flipkart.fintech.pinaka.common.varadhi.VaradhiClient;
import com.flipkart.fintech.pinaka.common.varadhi.VaradhiClientImpl;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.common.varadhi.RobinhoodEventRequest;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import com.google.inject.Inject;

import javax.ws.rs.client.Client;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

@CustomLog
public class RobinHoodEventHandlerImpl implements RobinHoodEventHandler {

    private ObjectMapper objectMapper;
    private VaradhiClient varadhiClient;
    private RobinhoodEventUtils robinhoodEventUtils;

    private static final HashMap<String, JourneyStatus> applicationStatusMap = new HashMap<>();

    @Inject
    public RobinHoodEventHandlerImpl(ObjectMapper objectMapper, Client client, VaradhiClentConfig varadhiClentConfig) {
        this.objectMapper = objectMapper;
        this.varadhiClient = new VaradhiClientImpl(varadhiClentConfig, client);
        this.robinhoodEventUtils = new RobinhoodEventUtils();
    }

    static {
        applicationStatusMap.put("OFFER_DETAILS", JourneyStatus.OFFERED);
        applicationStatusMap.put("OFFER_SCREEN", JourneyStatus.OFFERED);
        applicationStatusMap.put("REJECTED", JourneyStatus.REJECTED);
        applicationStatusMap.put("SUCCESS", JourneyStatus.APPROVED);
        applicationStatusMap.put("APPLICATION_COMPLETED", JourneyStatus.APPROVED);
        applicationStatusMap.put("CI_DETAILS", JourneyStatus.IN_PROGRESS);
        applicationStatusMap.put("ADDITIONAL_DETAILS", JourneyStatus.IN_PROGRESS);

    }

    @Override
    public void updateApplicationState(ApplicationEvent applicationEvent) {

        String currentApplicationState = applicationEvent.getApplicationState();
        String previousApplicationState = applicationEvent.getOldState();
        LoanApplication plLoanApplicationData = null;
        try {
            plLoanApplicationData = objectMapper.convertValue(applicationEvent.getApplicationData(), LoanApplication.class);
        } catch (IllegalArgumentException e) {
            log.error("Error while deserializing application event : {} with error : {}", applicationEvent.toString(), e.getMessage());
            throw new RuntimeException(e);
        }
        if (Objects.isNull(plLoanApplicationData))
            return;
        if (StringUtils.isNotBlank(currentApplicationState) && !currentApplicationState.equals(previousApplicationState) &&
                applicationEvent.getApplicationData() != null) {
            String applicationId = applicationEvent.getApplicationId();
            String financialProvider = getFinancialProvider(plLoanApplicationData);
            if (robinhoodEventUtils.checkIgnoreState(currentApplicationState, financialProvider)) {
                log.info("ignoring event for PersonalLoan app: {} as state is: {}", applicationId, currentApplicationState);
                return;
            }
            LenderDetails lenderDetails = plLoanApplicationData.getLenderDetails();
            Double eligibleAmount = null;
            try {
                eligibleAmount = robinhoodEventUtils.getEligibleAmount(currentApplicationState, lenderDetails, applicationId, plLoanApplicationData.getFinancialProvider());
            } catch (Exception e) {
                log.error("Error while fetching eligible amount with error :{}", e.getMessage());
                return;
            }
            PersonalLoanUserDetailsV2 personalLoanUserDetailsV2 = new PersonalLoanUserDetailsV2();
            personalLoanUserDetailsV2.setCohort(getCohort(plLoanApplicationData));
            personalLoanUserDetailsV2.setAccountId(applicationEvent.getExternalUserId());
            personalLoanUserDetailsV2.setLender(Lender.valueOf(financialProvider));
            personalLoanUserDetailsV2.setEligibleLoanAmount(eligibleAmount);
            personalLoanUserDetailsV2.setJourneyStatus(extractJourneyStatus(currentApplicationState));
            personalLoanUserDetailsV2.setUpdatedAt(new Date().getTime());
            processEventForRhUpdate(new RobinhoodEventRequest(personalLoanUserDetailsV2));
        }


    }

    private static String getFinancialProvider(LoanApplication plLoanApplicationData) {
        if (StringUtils.isNotEmpty(plLoanApplicationData.getFinancialProvider()) && (!plLoanApplicationData.getFinancialProvider().toLowerCase().equals("axis") && !plLoanApplicationData.getFinancialProvider().toLowerCase().equals("idfc"))) {
            return "UNALLOCATED";
        }
        return StringUtils.isNotEmpty(plLoanApplicationData.getFinancialProvider()) ? plLoanApplicationData.getFinancialProvider() : "UNALLOCATED";
    }

    private Cohort getCohort(LoanApplication plLoanApplicationData) {
        if (Objects.nonNull(plLoanApplicationData.getUserDetails()) && Objects.nonNull(plLoanApplicationData.getUserDetails().getSegment())) {
            return Cohort.valueOf(plLoanApplicationData.getUserDetails().getSegment());
        }
        return Cohort.NTB;
    }

    private JourneyStatus extractJourneyStatus(String applicationState) {
        try {
            if (!applicationStatusMap.containsKey(applicationState)) {
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(RobinHoodEventHandlerImpl.class, "applicationState", applicationState));
            }
            return applicationStatusMap.getOrDefault(applicationState, JourneyStatus.IN_PROGRESS);
        } catch (Exception e) {
            log.error("Invalid application state:{}", applicationState);
            throw new RuntimeException(e);
        }
    }


    private void processEventForRhUpdate(RobinhoodEventRequest robinhoodEventRequest) {
        try {
            log.info("Pushing event to varadhi");
            this.varadhiClient.pushEvent(robinhoodEventRequest);
        } catch (PinakaClientException e) {
            log.error("Error while pushing to varadhi : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
