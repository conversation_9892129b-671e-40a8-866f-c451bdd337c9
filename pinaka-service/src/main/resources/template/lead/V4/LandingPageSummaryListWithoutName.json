{"subWidget": null, "clientPowered": false, "formId": "LV4_LANDING_PAGE_SUMMARY_LIST_WITHOUT_NAME", "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/11/07/2025/4515bf35-ca43-4595-bcff-0ac697f47ce9.png?q={@quality}", "height": 132, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/11/07/2025/4515bf35-ca43-4595-bcff-0ac697f47ce9.png?q={@quality}", "width": 105}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Wohoo!", "specialTextsMapper": null, "showClipboard": false, "textColor": "#1D2939", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Instant loan of upto", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "textContainerStyle": {"display": "flex", "flexDirection": "column", "alignItems": "flex-start"}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingBottom": 0, "alignItems": "flex-start"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "₹1,00,000 *", "showClipboard": false, "textColor": "#4D43FE", "style": {"fontSize": 52, "lineHeight": 60, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold"}, "specialTextsMapper": {"coloredAmount": {"color": "#4D43FE"}, "*": {"color": "#C7CDFF"}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"value": {"type": "RichTextValue", "text": "is available for you!", "showClipboard": false, "textColor": "#98A2B3", "style": {"paddingTop": 12, "color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal"}}}, "textContainerStyle": {}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingTop": 0, "alignItems": "baseline"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null}