{"CACHE_INVALIDATION_TTL": "0", "META_INFO": {"abContext": {"abWrapper": []}, "appConfigHash": null, "clientConfigHashMap": null, "dcInfo": null, "omnitureInfo": null}, "REQUEST": null, "REQUEST-ID": "f8e04034-2cb8-4822-a07e-2ebb4105409d", "RESPONSE": {"actionSuccess": false, "marketPlaceTrackingDataMap": {}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"background": "linear-gradient(135deg, #f8e6fa 0%, #f9fafb 60%, #e6f0fa 100%)", "orientation": "", "theme": "light", "inBottomBar": true}, "pageHash": "-*********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "superCash", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACC1AA9317789384AB8A54DCC2F4029031A9", "pageType": "DYNAMIC", "pageId": "offer-dynam-dd62d", "applicationId": "APP2502241712254137621721185267668452213", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "f8e04034-2cb8-4822-a07e-2ebb4105409d", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "height": 132, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "width": 105}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Wohoo!", "specialTextsMapper": null, "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "An instant cash of", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "textContainerStyle": {"display": "flex", "flexDirection": "column", "alignItems": "flex-start"}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingBottom": 0, "alignItems": "flex-start"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "₹1,00,000 *", "showClipboard": false, "textColor": "#4D43FE", "style": {"fontSize": 56, "lineHeight": 60, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold"}, "specialTextsMapper": {"₹1,00,000": {"color": "#4D43FE"}, "*": {"color": "#C7CDFF"}}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"value": {"type": "RichTextValue", "text": "is reserved for you!", "showClipboard": false, "textColor": "#98A2B3", "style": {"paddingTop": 12, "color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal"}}}, "textContainerStyle": {}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingTop": 0, "alignItems": "baseline"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,0", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"orientation": "VERTICAL_CARD_ANIMATION", "renderableComponents": [{"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": 4, "borderRadius": 12, "backgroundImage": "linear-gradient(to right, #E5E8FD, #F9FAFB)", "padding": 16}, "subStatusText": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Quick cash in 2 minutes", "textColor": "#344054"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Fast and instant disbursal", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": 4, "borderRadius": 12, "background": "linear-gradient(to right, #F3DFFF, #F9FAFB)", "padding": 16}, "subStatusText": null, "tagValue": null, "title": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "3-10 months of easy EMI's", "textColor": "#344054"}}, "description": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "repay as per your comfort", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": 4, "borderRadius": 12, "backgroundImage": "linear-gradient(to right, #DDFAE8, #F9FAFB)", "padding": 16}, "subStatusText": null, "tagValue": null, "title": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "No paperwork", "textColor": "#344054"}}, "description": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "100% hassle-free process", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}]}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,24", "widgetWidth": 12}, "widget": {"type": "CARD_CAROUSEL", "data": {"renderableComponents": [{"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/7229ac68-95d2-4e3b-a1ad-158949b08f65.png?q={@quality}", "height": 28, "width": 31}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/e8512fe0-801f-4d0e-af63-32ac537f70ea.png?q={@quality}", "height": 25, "width": 28}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/7885680c-7968-4bbd-be59-284974778df7.png?q={@quality}", "height": 28, "width": 34}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/bea7652b-a853-479a-b782-62f3f2b2af10.png?q={@quality}", "height": 28, "width": 37}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/16f3f972-8a83-4b11-a414-2d069491dc7f.png?q={@quality}", "height": 28, "width": 43}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/e14cc817-ee87-42b9-9b35-999dc587dc07.png?q={@quality}", "height": 24, "width": 37}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}], "autoPlay": true, "speed": 22000}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,24", "widgetHeight": 150, "widgetWidth": 12, "inBottomBar": false}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "SUBMIT_BUTTON_WIDGET", "data": {"subWidget": null, "clientPowered": false, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "bold", "title": "Unlock offer now"}, "action": {"type": "CLIENT__INLINE_NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "6/pl/apply-now", "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F9FAFB", "orientation": "", "theme": "light", "appBarBehavior": "hidden"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "", "pageSubtitle": "", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACC14029865817626988", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2506302335334659794866923682112872581", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "25b04894-2a9b-4b55-b6aa-6343b6654005", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 0, "widgetWidth": 12}, "dataId": "-********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LV4_LANDING_PAGE", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": " ", "icon": {"type": "ImageValue", "alternateText": "Sample PAN image", "url": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "height": 98, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/02/07/2025/00d3c9eb-4e80-4c6b-95f7-6d9c9fdbbcad.png?q={@quality}", "width": 312}}, "subTitle": {"text": "Make sure your name is as per PAN", "style": {"fontFamily": "<PERSON><PERSON>", "color": "#4D43FE", "fontSize": 20, "fontWeight": "bold", "width": 100}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "fullName", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name can only have letters", "interactionType": null, "regex": "^[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name must be of 3 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{3,}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name exceeds 26 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{0,26}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Remove extra spaces in your name", "interactionType": null, "regex": "^(?!.*\\s{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Re-check your name – repeated letters may be a mistake", "interactionType": null, "regex": "^(?!.*(.)\\1{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name cannot start or end with spaces", "interactionType": null, "regex": "^[A-Za-z].*[A-Za-z]$|^[A-Za-z]$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}}, "value": "VARDAAN AGARWAL", "tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": true, "prefilledValue": "VARDAAN AGARWAL", "sendInlineError": true}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "**********", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "subText": "To change/edit your number you need to call the bank", "textboxStyles": {"captionStyle": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}, "containerStyle": {"background-color": "#EAECF0"}, "inputBoxStyle": {"background-color": "#EAECF0"}, "inputStyle": {"background-color": "#EAECF0", "border": "none", "border-radius": 8, "box-shadow": "none", "color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal"}, "labelStyle": {"color": "#98A2B3", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal"}}, "tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": true, "prefilledValue": "**********", "sendInlineError": true}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "gdgX5hiCwb_22XSm", "taskKey": "leadV4LandingPage", "applicationId": "APP2506302335334659794866923682112872581", "taskId": "6ry0SumWj7oj2gYk", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmC6Czi4MA8MmspHKIUEVOUCA288R9Mp9upvxYHbUpY2HB7R3aOdDowZhjv6QHiAVIn5obubPtDGO+iHGjm1bCnL"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Done", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "bold"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I hereby grant express consent and authorize Scapic Innovations Pvt. Ltd (SIPL) to collect, process, record my personal data including personal information and sensitive personal information such as PAN, DOB, gender, contact details, income source ('Personal Data') during loan journey on the platform/application and share Personal Data with SIPL’s Partners to complete Know Your Customer (KYC) requirements as required under applicable laws and for the purpose of processing my loan application ('Purpose'). I further consent to and authorize the SIPL’s Partners to further share my Personal Data with their service providers and/or third parties for the Purpose including Tele-calling, SMS or WhatsApp. I also understand and authorize the SIPL’s Partners to pull my credit report separately from any credit information companies' basis my Personal Data shared for the Purpose. I hereby consent to and expressly authorize SIPL to pull credit reports from credit information companies (Read Experian Specific terms here), to evaluate my credit worthiness/eligibility/identity for financial services and products such as personal loans, credit cards, buy now pay later /or for my loan application respectively (End Use Purpose). SIPL does not share credit information received from credit information companies with third parties without your express consent. I hereby also confirm that I am an Indian citizen and accept that I have read the terms and conditions and privacy policy of Scapic Innovations Pvt. Ltd.."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": {"background": "#FFFFFF", "padding": 24, "width": "calc(100% - 48px)"}, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": "NO_SHADOW"}}, "tracking": {}}}]}}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": {"publicKey": null, "keyId": null}, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": null, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": null}}}}]}}, "SESSION": {"accountId": "ACC1AA9317789384AB8A54DCC2F4029031A9", "asn": null, "at": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjNhNzdlZTgxLTRjNWYtNGU5Ni04ZmRlLWM3YWMyYjVlOTA1NSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TIBCwh3NNsnKeMWWHP1LC_1gWu7ltD3-mlVlMHFWRs4", "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "flipkartFirstUser": false, "isLoggedIn": true, "isUPISessionActive": false, "kaction": null, "lastName": "<PERSON><PERSON><PERSON>", "mobileNumber": null, "nsid": "3.VIDD76D52AD5394327B85381B5FC3D24F7.*************.VIDD76D52AD5394327B85381B5FC3D24F7", "rt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhlM2ZhMGE3LTJmZDMtNGNiMi05MWRjLTZlNTMxOGU1YTkxZiJ9.****************************************************************************************************************************************************************************************************************************************************************************************.T5Nv1ax-S-GuRtxQxVam-bLUYNCGVGVOGABuc_cQczE", "secureToken": "VIDD76D52AD5394327B85381B5FC3D24F7:VIDD76D52AD5394327B85381B5FC3D24F7", "sn": "VIDD76D52AD5394327B85381B5FC3D24F7.TOK70307EB53DB34C9E9F62E66233D0C265.1740399775.LI", "tracking": null, "ts": 0, "twoFa": false, "upiSessionPolicy": null, "vid": "VIDD76D52AD5394327B85381B5FC3D24F7"}, "STATUS_CODE": 200}