{"shouldHideConsent": true, "shouldHideFields": true, "clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "LV4_LANDING_PAGE_NAME_PAGE", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "fullName", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name cannot include numbers or special characters", "interactionType": null, "regex": "^[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name must be of 3 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{3,}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Name exceeds 26 characters", "interactionType": null, "regex": "^[A-Za-z\\s]{0,26}$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Remove extra spaces in your name", "interactionType": null, "regex": "^(?!.*\\s{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}, {"errorMessage": "Re-check your name – repeated letters may be a mistake", "interactionType": null, "regex": "^(?!.*(.)\\1{2,})[A-Za-z\\s]+$", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}}, "value": "", "tracking": {"sendImpression": true, "fieldName": "fullName", "sendEdits": true, "prefill": false, "prefilledValue": "", "sendInlineError": true}}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "textboxStyles": {"captionStyle": {"color": "#475467", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal"}, "containerStyle": {"background-color": "#EAECF0"}, "inputBoxStyle": {"background-color": "#EAECF0"}, "inputStyle": {"background-color": "#EAECF0", "border": "none", "border-radius": 8, "box-shadow": "none", "color": "#667085", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal"}, "labelStyle": {"color": "#98A2B3", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal"}}, "tracking": {"sendImpression": true, "fieldName": "phoneNumber", "sendEdits": true, "prefill": false, "prefilledValue": "", "sendInlineError": true}}}], "resultStoreKey": null, "subTitle": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_HIDDEN_FORM_SUBMIT_BUTTON_CLICK"}, "constraints": null, "customTrackingEvents": null, "encryption": {"keyId": "sumo-end-key", "publicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmCrV0vRs4d+9pufICdn/QVYfmu0+4ZHqh+qRxLXxPqOUwCMJnVq2yT27JYoEOAe8xPXyQVIR6NM8+XojcO/rKZxPOV7iVfB+mcoZ06EaMp/S4NPJs+TjbEnSoAMJ7OL5PPQKmFGiKwAYQLrcAj08Fu0AlLDBUXrtg2i/W/occxZQnJTq0dUPoRCRb6YYD7DwezWGrHaGZUffzzYvDaBB1e0tBgpPF8HZ7K359xT6w6haUuYw9bnDu4FqW3rv82upU3dexi11txOUbuE4Xwg/4uVb0kmsgCDRVwafKx090cjJWbuHJ3ZcMzekJc/ueuqUr1dx7PJX1ClMhDhcMnRs4QIDAQAB"}, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "-RsPa5_sMrCj0E6Q", "taskKey": "namePage", "applicationId": "APP2502041932335592866489436724399367995", "taskId": "WscddwhCpApvGnma", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCCyfyZEwSXJ45Pc2h9AZ52JmAI9isC9V/qJcIGHXfrJLqsWQPS7T5sKIyfxxIR2NJqBicz3dPc+RQnPQMZ9DAX"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": {"fieldName": "LV4_LANDING_PAGE_NAME_PAGE_HIDDEN_FORM_SUBMIT_BUTTON"}, "trackingData": null, "value": {"type": "RichTextButtonValue", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000000", "buttonTextWeight": "medium", "buttonTextSize": 18, "title": "Unlock offer now"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "${consent}"}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "${consentFor}", "consentId": "${consentId}", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": null}, "title": null}