package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.PageTitleWidgetData;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.utils.WidgetUtils;

public class PageTitleWidgetBuilderMapper {


    public static WidgetBuilder<PageTitleWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = WidgetUtils.getPageTypeContext(widgetInfo);
        switch (context) {
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
