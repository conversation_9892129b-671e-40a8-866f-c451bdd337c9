package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.CardWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.CARD_WIDGET)
public class CardWidgetAdapter extends WidgetAdapter<CardWidgetData> {
    public CardWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, CardWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
