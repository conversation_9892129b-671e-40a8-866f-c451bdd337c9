package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CreditScoreWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.CreditScoreWidgetMapper;

@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.CREDIT_SCORE)
public class CreditScoreWidgetAdapter extends WidgetAdapter<CreditScoreWidgetData> {
    public CreditScoreWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, CreditScoreWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
