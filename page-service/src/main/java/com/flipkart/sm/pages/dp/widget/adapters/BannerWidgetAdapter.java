
package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.BannerWidgetBuilderMapper;

@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.BANNER)
public class BannerWidgetAdapter extends WidgetAdapter<BannerWidgetData> {

  public BannerWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, BannerWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
  }
}
