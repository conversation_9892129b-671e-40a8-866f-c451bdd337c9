package com.flipkart.sm.pages.dp.apis;

import com.flipkart.aapi.multiwidget.annotations.DataAPI;
import com.flipkart.aapi.multiwidget.apis.AbstractDataAPI;
import com.flipkart.aapi.multiwidget.models.request.DataAPIRequest;
import com.flipkart.rome.datatypes.response.fintech.supermoney.nudge.NudgeType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.nudge.OverlayNudgeData;
import com.flipkart.sm.pages.dp.constants.PageServiceDataAPIType;
import com.flipkart.sm.pages.enums.OverlayNudgeType;


@DataAPI(PageServiceDataAPIType.GET_OVERLAY_NUDGE)
public class GetOverlayNudgeDataAPI extends AbstractDataAPI {
    private static final String NUDGE_ID_FORMATTER = "nudge-%s";

    public GetOverlayNudgeDataAPI(DataAPIRequest request) {
        super(request);
    }

    @Override
    public OverlayNudgeData process() throws Exception {
        if (pageLevelOverlayNudgeEnabled(request) && !request.isRefetched()) {
            return buildOverlayNudgeData(request);
        }
        return null;
    }

    public static boolean pageLevelOverlayNudgeEnabled(DataAPIRequest request) {
        return request.getQueryParams().containsKey("showOverlayNudge") && request.getQueryParams().get("showOverlayNudge").equalsIgnoreCase("true");
    }

    public static String createNudgeIdFromRequestId(DataAPIRequest request) {
        return String.format(NUDGE_ID_FORMATTER, request.getContext().getRequestId());
    }

    public static OverlayNudgeData buildOverlayNudgeData(DataAPIRequest request) {
        OverlayNudgeType overlayNudgeType = OverlayNudgeType.valueOf(request.getQueryParams().get("overlayNudgeType"));
        return buildOverlayNudgeDataForOverlayNudgeType(overlayNudgeType, request);
    }

    public static OverlayNudgeData buildOverlayNudgeDataForOverlayNudgeType(OverlayNudgeType overlayNudgeType, DataAPIRequest dataAPIRequest) {
        OverlayNudgeWidgetBuilder overlayNudgeWidgetBuilder;

        switch (overlayNudgeType) {
            default: {
                return null;
            }
        }
    }

    private static OverlayNudgeData buildOverlayNudgeDataFromBuilder(DataAPIRequest dataAPIRequest, OverlayNudgeWidgetBuilder overlayNudgeWidgetBuilder) {
        OverlayNudgeData overlayNudgeData = new OverlayNudgeData();
        overlayNudgeData.setNudgeType(NudgeType.OVERLAY_NUDGE);
        overlayNudgeData.setNudgeId(createNudgeIdFromRequestId(dataAPIRequest));
        overlayNudgeData.setWidget(overlayNudgeWidgetBuilder.getWidget(dataAPIRequest));
        return overlayNudgeData;
    }
}
