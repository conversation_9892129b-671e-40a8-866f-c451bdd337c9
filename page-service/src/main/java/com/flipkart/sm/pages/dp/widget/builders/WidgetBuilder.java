package com.flipkart.sm.pages.dp.widget.builders;

import com.flipkart.aapi.multiwidget.constants.Constant;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.FooterValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.sm.pages.dp.constants.TrackingConstants;

import java.util.HashMap;
import java.util.Map;


public abstract class WidgetBuilder<W extends WidgetData> {
  protected WidgetInfo widgetInfo;

  public WidgetBuilder(WidgetInfo widgetInfo) {
    this.widgetInfo = widgetInfo;
  }

  public abstract W getWidgetData();

  public RenderableComponent<HeaderValue> getRenderableHeaders(){
    return null;
  }

  public RenderableComponent<FooterValue> getRenderableFooters(){
    return null;
  }

  protected Map<String, String> getBaseTracking() {
    Map<String, String> tracking = new HashMap<>();
    tracking.put(TrackingConstants.KEY_WIDGET_NAME, widgetInfo.getWidgetType().toString());
    tracking.put(TrackingConstants.KEY_IMPRESSION_ID, widgetInfo.getContext().getRequestId());
    if (widgetInfo.getPageParams() != null) {
      String pageId = widgetInfo.getPageParams().get(Constant.PAGE_ID).toString();
      String pageName = widgetInfo.getPageParams().get(Constant.PAGE_NAME).toString();
      tracking.put(TrackingConstants.KEY_PAGE_ID, pageId);
      tracking.put(TrackingConstants.KEY_PAGE_NAME, pageName);
    }
    return tracking;
  }

  public Map<String, String> getTrackingParamsForWidget() {
    return this.getBaseTracking();
  }
}
