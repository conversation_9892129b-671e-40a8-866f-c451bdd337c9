
package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.KeyValueWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.KeyValueWidgetBuilderMapper;

@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.KEY_VALUE)
public class KeyValueWidgetAdapter extends com.flipkart.sm.pages.dp.widget.adapters.WidgetAdapter<KeyValueWidgetData> {

  public KeyValueWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, KeyValueWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
  }
}
