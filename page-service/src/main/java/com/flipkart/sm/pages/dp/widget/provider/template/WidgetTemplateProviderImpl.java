package com.flipkart.sm.pages.dp.widget.provider.template;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.sm.pages.config.PageServiceConfig;
import com.flipkart.sm.pages.utils.FileUtils;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.Map;

@CustomLog
@Singleton
public class WidgetTemplateProviderImpl implements WidgetTemplateProvider {

    private final Map<String, JsonNode> widgetTemplates;

    @Inject
    public WidgetTemplateProviderImpl(PageServiceConfig pageServiceConfig){
        this.widgetTemplates = new HashMap<>();
        pageServiceConfig.getWidgetTemplateMap().forEach((key, path) -> {
            try {
                this.widgetTemplates.put(key, ObjectMapperUtil.get().readTree(FileUtils.readFileasString(path)));
            } catch (Exception e) {
                log.error("[WidgetTemplateProviderImpl] Error occurred while parsing template key: {}, path: {} error {}", key, path, e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public <W> W getWidgetTemplate(String templateKey, TypeReference<W> typeReference) {
        try {
            return ObjectMapperUtil.get().convertValue(widgetTemplates.get(templateKey), typeReference);
        }catch (Exception e) {
            log.error("Not able to parse the widgetTemplates  ",e);
            throw new RuntimeException(e);
        }
    }
}
