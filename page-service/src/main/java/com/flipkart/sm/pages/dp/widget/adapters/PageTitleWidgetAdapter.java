package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.PageTitleWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.PageTitleWidgetBuilderMapper;

@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.PAGE_TITLE)
public class PageTitleWidgetAdapter extends WidgetAdapter<PageTitleWidgetData> {

  public PageTitleWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, PageTitleWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
  }
}
