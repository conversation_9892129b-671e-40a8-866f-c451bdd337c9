package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.NavigationBarWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;


public class SuperCashNavBarWidgetBuilder extends WidgetBuilder<NavigationBarWidgetData> {
    private static final String WIDGET_IDENTIFIER = "SUPERCASH_NAV_BAR";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final boolean showInfoIcon ;

    public SuperCashNavBarWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        showInfoIcon = (boolean) widgetInfo.getSlotParams().getOrDefault("INFO_ICON",false);
    }

    @Override
    public NavigationBarWidgetData getWidgetData() {
        NavigationBarWidgetData superCashNavBar = widgetTemplateProvider.getWidgetTemplate( WIDGET_IDENTIFIER,
                new TypeReference<NavigationBarWidgetData>() {}
        );
        if(!showInfoIcon){
            superCashNavBar.setActions(null);
        }
        return superCashNavBar;
    }
}
