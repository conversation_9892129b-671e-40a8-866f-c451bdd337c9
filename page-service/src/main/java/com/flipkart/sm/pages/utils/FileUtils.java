package com.flipkart.sm.pages.utils;

import lombok.CustomLog;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;


@CustomLog
public class FileUtils {

    private FileUtils() {
    }

    public static String readFileasString(String fileName) {
        try (InputStream inputStream = FileUtils.class.getClassLoader().getResourceAsStream(fileName)){
            return  IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        }
        catch(Exception e){
            log.error("Failed to read file" +  fileName + " with error : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

}