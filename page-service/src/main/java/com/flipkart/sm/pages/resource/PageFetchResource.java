package com.flipkart.sm.pages.resource;


import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.dataprovider.models.controller.exceptions.PageFetchControllerException;
import com.flipkart.dataprovider.models.controller.request.PageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.request.SingleSourcePageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.response.ControllerViewResponse;
import com.flipkart.sm.pages.core.PinakaPageFetchService;
import com.flipkart.sm.pages.dp.constants.CustomMediaType;
import com.flipkart.sm.pages.utils.ObjectMapperProvider;
import com.flipkart.sm.pages.utils.UrlUtils;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import io.swagger.v3.oas.annotations.Operation;
import lombok.CustomLog;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Produces(CustomMediaType.APPLICATION_JSON_WITH_ENCODING)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/gateway/v1/page")
@CustomLog
public class PageFetchResource {

    private final String pageFetchMetricBaseName = MetricRegistry.name(this.getClass().getName(), "-pageFetch-");
    private final PinakaPageFetchService pageFetchController;
    private final MetricRegistry metricRegistry;

    @Inject
    public PageFetchResource(
            PinakaPageFetchService pageFetchController,
            @Named("pageServiceMetric") MetricRegistry metricRegistry
    ) {
        this.pageFetchController = pageFetchController;
        this.metricRegistry = metricRegistry;
    }

    @POST
    @Path("/fetch")
    @ExceptionMetered
    @Timed
    @Operation(description = "Generate page fetch response")
    public Response fetch(@NotNull @Valid JsonNode requestNode) throws PageFetchControllerException {
        PageFetchControllerRequest pageFetchControllerRequest = ObjectMapperProvider.get().convertValue(requestNode, PageFetchControllerRequest.class);
        String metricName = MetricRegistry.name(pageFetchMetricBaseName, pageFetchControllerRequest.getPageName());

        try(Timer.Context timerContext = metricRegistry.timer(metricName).time()){
            ControllerViewResponse controllerViewResponse =
                    pageFetchController.fetchPage(pageFetchControllerRequest);
            return Response.ok().entity(controllerViewResponse).build();
        }
        catch (Exception e) {
            log.error("Error page fetch for pageId:{} and requestContext:{}", pageFetchControllerRequest.getPageId(), pageFetchControllerRequest.getRequestContext(), e);
            throw e;
        }
    }

    @POST
    @Path("/single-source/fetch")
    @ExceptionMetered
    @Timed
    @Operation(description = "Generate page fetch response")
    public Response singleSourceFetch(@NotNull @Valid JsonNode requestJson) throws PageFetchControllerException {
        SingleSourcePageFetchControllerRequest request = ObjectMapperProvider.get().convertValue(requestJson, SingleSourcePageFetchControllerRequest.class);
        String metricName = MetricRegistry.name(pageFetchMetricBaseName, UrlUtils.getUrlPath(request.getPageUri()));
        try(Timer.Context timerContext = metricRegistry.timer(metricName).time()){
            ControllerViewResponse controllerViewResponse = pageFetchController.fetchPageFromSingleSourceRequest(request);
            return Response.ok().entity(controllerViewResponse).build();
        }
        catch (Exception e) {
            log.error("Error page fetch for pageUri:{} and requestContext:{}", request.getPageUri(), request.getRequestContext(), e);
            throw e;
        }
    }
}

