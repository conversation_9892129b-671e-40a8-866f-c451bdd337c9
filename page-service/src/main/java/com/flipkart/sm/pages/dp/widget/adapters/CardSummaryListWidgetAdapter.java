package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.CardSummaryListWidgetMapper;

@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.CARD_SUMMARY_LIST)
public class CardSummaryListWidgetAdapter extends WidgetAdapter<CardSummaryListWidgetData> {
  public CardSummaryListWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, CardSummaryListWidgetMapper.getWidgetBuilderByType(widgetInfo));
  }
}
