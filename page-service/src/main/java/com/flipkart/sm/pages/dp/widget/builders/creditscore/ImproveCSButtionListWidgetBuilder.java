package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.ButtonListWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;


public class ImproveCSButtionListWidgetBuilder extends WidgetBuilder<SubmitButtonWidgetData> {
    private static final String WIDGET_IDENTIFIER = "IMPROVE_CREDIT_SCORE";

    private final WidgetTemplateProvider widgetTemplateProvider;


    public ImproveCSButtionListWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public SubmitButtonWidgetData getWidgetData() {
        SubmitButtonWidgetData buttonWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER,
                new TypeReference<SubmitButtonWidgetData>() {
                });
        return buttonWidgetData;
    }

}
