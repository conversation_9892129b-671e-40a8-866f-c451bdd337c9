package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.KeyValueWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets.CreditMetricCreditScoreKeyValueWidgetBuilder;

public class KeyValueWidgetBuilderMapper {

    public static WidgetBuilder<KeyValueWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case CREDIT_METRIC_CREDIT_SCORE_KEY_VALUE:
                return new CreditMetricCreditScoreKeyValueWidgetBuilder(widgetInfo);
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
