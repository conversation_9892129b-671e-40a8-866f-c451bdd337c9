package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;


public class CheckCreditScoreBannerWidgetBuilder extends WidgetBuilder<BannerWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CHECK_CREDIT_SCORE_BANNER";


    private final WidgetTemplateProvider widgetTemplateProvider;

    public CheckCreditScoreBannerWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public BannerWidgetData getWidgetData() {
        BannerWidgetData bannerWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<BannerWidgetData>() {});
        return bannerWidgetData;
    }

}