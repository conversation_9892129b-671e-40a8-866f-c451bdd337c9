package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.sm.pages.dp.constants.TrackingConstants;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;

import java.util.Map;
import java.util.Objects;

public class CheckCreditScorePLBannerWidgetBuilder extends WidgetBuilder<BannerWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CHECK_CREDIT_SCORE_PL_BANNER";
    private final WidgetTemplateProvider widgetTemplateProvider;

    public CheckCreditScorePLBannerWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);

    }

    @Override
    public BannerWidgetData getWidgetData() {
        BannerWidgetData bannerWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<BannerWidgetData>() {});
        addClickEvents(bannerWidgetData);
        return bannerWidgetData;
    }
    private void addClickEvents(BannerWidgetData bannerWidgetData) {
        Map<String,String> baseTracking  = super.getBaseTracking();
        bannerWidgetData.getRenderableComponents().forEach(banner->{
            if(Objects.nonNull(banner) && Objects.nonNull(banner.getAction())){
                banner.getAction().getTracking().putAll(baseTracking);
                banner.getAction().getTracking().put(TrackingConstants.KEY_CONTENT_VALUE, widgetInfo.getContext().getAccountId());
            }
        });
    }
}