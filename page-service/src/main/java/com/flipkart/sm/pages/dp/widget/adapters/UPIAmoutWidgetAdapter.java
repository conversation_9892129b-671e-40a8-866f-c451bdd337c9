package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.UPIAmoutWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.UPIAmoutWidgetBuilderMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.UPI_AMOUNT_WIDGET)
public class UPIAmoutWidgetAdapter extends WidgetAdapter<UPIAmoutWidgetData> {
    public UPIAmoutWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, UPIAmoutWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
    }
}