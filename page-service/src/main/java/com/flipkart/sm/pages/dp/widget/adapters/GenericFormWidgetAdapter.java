package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GenericFormWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.GenericFormWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.FORM_V4)
public class GenericFormWidgetAdapter extends WidgetAdapter<GenericFormWidgetData> {
    public GenericFormWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, GenericFormWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
