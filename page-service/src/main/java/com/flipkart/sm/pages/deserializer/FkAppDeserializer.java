package com.flipkart.sm.pages.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.mobile.parser.vo.FkApp;

import java.io.IOException;

public class FkAppDeserializer extends JsonDeserializer<FkApp> {

  @Override
  public FkApp deserialize(JsonParser jp, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jp.getCodec();
    JsonNode node = oc.readTree(jp);


    final String type = node.get("type").asText();
    final String version = node.get("version").asText();
    final String platform = node.get("platform").asText();
    final String device = node.get("device").asText();
    final String versionName = node.get("versionName").asText();

    return new FkApp(type, version, platform, device, versionName);
  }
}
