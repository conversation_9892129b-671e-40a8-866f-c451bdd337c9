package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.KeyValueWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.sm.pages.utils.WidgetUtils;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

import static com.flipkart.sm.pages.dp.constants.Constants.UNDER_THOUSAND_DECIMAL_PATTERN;


public class CreditMetricCreditScoreKeyValueWidgetBuilder extends WidgetBuilder<KeyValueWidgetData> {

    private static final String WIDGET_IDENTIFIER = "CREDIT_METRIC_KEY_VALUE";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final BureauDataResponse bureauDataResponse;
    private final String creditMetric;
    private static final Map<String, String> creditMetricTitleMapping = getCreditMetricTitleMapping();
    private static final Map<String, String> creditMetricImpactMapping = getCreditMetricImpactMapping();

    public CreditMetricCreditScoreKeyValueWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
        this.creditMetric = (String) widgetInfo.getSlotParams().get(Constants.CREDIT_METRIC);
    }

    @Override
    public KeyValueWidgetData getWidgetData() {
        KeyValueWidgetData keyValueWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<KeyValueWidgetData>() {});
        keyValueWidgetData.getKeyValuePairs().get(0).getRichTextKey().setText(creditMetricTitleMapping.get(creditMetric));
        keyValueWidgetData.getKeyValuePairs().get(0).getRichTextValue().setText(getValueForCreditMetric(creditMetric, bureauDataResponse));
        keyValueWidgetData.getKeyValuePairs().get(1).getRichTextKey().setText(creditMetricImpactMapping.get(creditMetric));

        return keyValueWidgetData;
    }

    private static Map<String, String> getCreditMetricTitleMapping() {
        Map<String, String> creditMetricTitleMapping = new HashMap<>();
        creditMetricTitleMapping.put("ON_TIME_PAYMENTS", "On-time payments");
        creditMetricTitleMapping.put("CREDIT_UTILISATION", "Credit Utilisation");
        creditMetricTitleMapping.put("CREDIT_AGE", "Credit Age");
        creditMetricTitleMapping.put("CREDIT_MIX", "Credit Mix");
        creditMetricTitleMapping.put("CREDIT_ENQUIRES", "Credit Enquires");
        return creditMetricTitleMapping;
    }

    private static Map<String, String> getCreditMetricImpactMapping() {
        Map<String, String> creditMetricImpactMapping = new HashMap<>();
        creditMetricImpactMapping.put("ON_TIME_PAYMENTS", "High Impact");
        creditMetricImpactMapping.put("CREDIT_UTILISATION", "High Impact");
        creditMetricImpactMapping.put("CREDIT_AGE", "Medium Impact");
        creditMetricImpactMapping.put("CREDIT_MIX", "Medium Impact");
        creditMetricImpactMapping.put("CREDIT_ENQUIRES", "Low Impact");
        return creditMetricImpactMapping;
    }

    private static String getValueForCreditMetric(String creditMetric, BureauDataResponse bureauDataResponse) {
        switch (creditMetric) {
            case "ON_TIME_PAYMENTS":
                String onTimePayment = formatNumber(UNDER_THOUSAND_DECIMAL_PATTERN,bureauDataResponse.getOnTimePaymentPer());
                return onTimePayment + "%";
            case "CREDIT_UTILISATION":
                long creditUsage = Math.round(bureauDataResponse.getCreditUsage());
                return creditUsage + "%";
            case "CREDIT_AGE":
                return bureauDataResponse.getCreditAge();
            case "CREDIT_MIX":
                return bureauDataResponse.getCreditMix();
            case "CREDIT_ENQUIRES":
                return String.valueOf(bureauDataResponse.getLast180DaysCreditEnquiries());
            default:
                return null;
        }
    }
    private static String formatNumber(String pattern, Object value) {
        return new DecimalFormat(pattern).format(value);
    }

}
