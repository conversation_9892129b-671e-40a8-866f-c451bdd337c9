package com.flipkart.sm.pages.dp.constants.style;

public enum Colors {
  PrimaryHeaderThemeColor("#4D43FE"),
  PrimaryButtonThemeColor("#B5EF85"),
  Secondary500("#4D43FE"),
  Primary500("#B5EF85"),
  Primary100("#F1FFE5"),
  Primary600("#9BD869"),
  <PERSON><PERSON><PERSON>("#12B76A"),
  Grey<PERSON>0("#344054"),
  <PERSON><PERSON><PERSON>("#1D2939"),
  Grey400("#98A2B3"),
  <PERSON><PERSON>("#667085"),
  Grey<PERSON>0("#475467"),
  <PERSON><PERSON>("#F2F4F7"),
  Grey300("#D0D5DD"),
  <PERSON>20<PERSON>("#101828"),
  <PERSON><PERSON><PERSON><PERSON>0("#D92D20"),
  <PERSON><PERSON><PERSON><PERSON>("#F04438"),
  <PERSON>n<PERSON>("#F79009"),
  Warn700("#B54708"),
  <PERSON><PERSON><PERSON>("#FFFAEB"),
  Warn600("#FF9900"),
  <PERSON><PERSON>r700("#B42318"),
  <PERSON>("#FFFFFF"),
  <PERSON><PERSON><PERSON><PERSON>("#C7CDFF"),
  <PERSON><PERSON><PERSON><PERSON>("#4D43FE"),
  SuperB<PERSON>200("#B3BAFF"),
  SuperGray0("#FFFFFF"),
  SuperGray200("#EAECF0"),
  SuperGreen600("#9BD869"),
  SuperGreen200("#E0FFC7"),
  SuperGray400("#98A2B3"),
  SuperGray800("#1D2939"),
  SuperGray600("#475467"),
  SystemYellow500("#FF9900"),
  Red500("#FA2617"),

  SystemYellow200("#FEDF89");



  private final String color;

  Colors(String color) {
    this.color = color;
  }

  @Override
  public String toString() {
    return color;
  }
}
