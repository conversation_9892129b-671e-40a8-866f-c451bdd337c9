package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.RichCarouselWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.RichCarouselWidgetBuilderMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.RICH_CAROUSEL)
public class RichCarousalWidgetAdapter extends WidgetAdapter<RichCarouselWidgetData> {

  public RichCarousalWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, RichCarouselWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
  }
}
