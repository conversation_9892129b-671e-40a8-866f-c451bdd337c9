package com.flipkart.sm.pages.utils;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.aapi.multiwidget.models.response.AggregatedDataResponse;
import com.flipkart.cse.pages.model.dataproviders.DataProviderWidgetConfiguration;
import com.flipkart.cse.pages.model.page.layout.PageLayoutComponentConfig;
import com.flipkart.dataprovider.models.controller.request.PageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.response.ControllerViewResponse;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.request.page.v4.PageRequest;
import com.flipkart.rome.datatypes.response.fintech.supermoney.nudge.*;
import com.flipkart.rome.datatypes.response.page.v4.Widget;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageServiceDataAPIType;
import com.flipkart.sm.pages.dp.constants.PageType;
import lombok.CustomLog;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4.NAVIGATION_BAR_WIDGET;
import static com.flipkart.sm.pages.dp.constants.Constants.PARTNER_CONSENT_BOTTTOMSHEET_NUDGE;
import static com.flipkart.sm.pages.dp.constants.Constants.PARTNER_REPORT_CONSENT_URL;

@CustomLog
public class ControllerViewResponseUtils {
    public static void postBuildProcess(PageFetchControllerRequest request,
                                        AggregatedDataResponse response,
                                        ControllerViewResponse controllerViewResponse) {
        removeAndAddNavigationBar(request, controllerViewResponse);
        removeAndAddFooter(controllerViewResponse);
        controllerViewResponse.getWidgetMap().values().removeIf(Objects::isNull);

        Map<String, SMNudgeData> pageNudges = new HashMap<>();
        //TODO: CREATE SOME SORT OF TOAST ABSTRACTION
        if (response.containsKey(PageServiceDataAPIType.GET_TOAST_MESSAGE) && response.get(PageServiceDataAPIType.GET_TOAST_MESSAGE) != null) {
            pageNudges.put("TOAST", (ToastNudgeData) response.get(PageServiceDataAPIType.GET_TOAST_MESSAGE));
        }
        if (response.containsKey(PageServiceDataAPIType.GET_OVERLAY_NUDGE) && response.get(PageServiceDataAPIType.GET_OVERLAY_NUDGE) != null) {
            pageNudges.put("OVERLAY", (OverlayNudgeData) response.get(PageServiceDataAPIType.GET_OVERLAY_NUDGE));
        }

        if (PARTNER_CONSENT_BOTTTOMSHEET_NUDGE.equals(request.getPageParams().get(PageServiceDataAPIType.GET_BOTTOMSHEET_NUDGE)) && !hasPartnerReportConsent(response)) {
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageUri(PARTNER_REPORT_CONSENT_URL);

            BottomSheetNudgeData bottomSheetNudgeData = new BottomSheetNudgeData();
            bottomSheetNudgeData.setPageRequest(pageRequest);
            bottomSheetNudgeData.setCanDismissBottomSheet(Boolean.FALSE);
            bottomSheetNudgeData.setNudgeType(NudgeType.BOTTOM_SHEET_NUDGE);
            pageNudges.put("BOTTOMSHEET", bottomSheetNudgeData);
        }

        if (!pageNudges.isEmpty()) {
            controllerViewResponse.setPageNudges(pageNudges);
        }

    }

    private static boolean hasPartnerReportConsent(AggregatedDataResponse response) {
        return Objects.nonNull(response.get(PageServiceDataAPIType.BUREAU_DATA)) &&
                ("SUPERMONEY".equals(((BureauDataResponse) response.get(PageServiceDataAPIType.BUREAU_DATA)).getMerchant()) ||
                        ((BureauDataResponse) response.get(PageServiceDataAPIType.BUREAU_DATA)).isCrossMerchantConsentExists());
    }

    private static void removeAndAddFooter(ControllerViewResponse controllerViewResponse) {
        List<Map.Entry<Integer, Widget>> list = controllerViewResponse.getWidgetMap().entrySet().stream()
                .filter(kv -> kv.getValue() != null && kv.getValue().getType().equals(WidgetTypeV4.SM_FOOTER_WIDGET))
                .collect(Collectors.toList());
        if (list.isEmpty()) return;
        Map.Entry<Integer, Widget> kv = list.get(0);
        controllerViewResponse.setFooterWidget(kv.getValue());
        controllerViewResponse.getWidgetMap().remove(kv.getKey());
    }

    private static void removeAndAddNavigationBar(PageFetchControllerRequest request, ControllerViewResponse controllerViewResponse) {
        Optional<Map.Entry<Integer, Widget>> navBarOpt = controllerViewResponse.getWidgetMap().entrySet().stream()
                .filter(kv -> kv != null && kv.getValue() != null && kv.getValue().getType().equals(NAVIGATION_BAR_WIDGET))
                .findFirst();
        // todo : update aapi page configs with boolean flag for default nav bar in page metadata
        if (navBarOpt.isPresent()) {
            controllerViewResponse.setNavigationBarWidget(navBarOpt.get().getValue());
            controllerViewResponse.getWidgetMap().remove(navBarOpt.get().getKey());
        }
    }

    private static WidgetInfo getNavWidgetInfo(PageFetchControllerRequest request) {
        PageLayoutComponentConfig slot = new PageLayoutComponentConfig();
        slot.setWidgetTypes(Collections.singletonList(new DataProviderWidgetConfiguration(NAVIGATION_BAR_WIDGET.name(), NAVIGATION_BAR_WIDGET.name())));
        slot.setSlotParams(new HashMap<String, Object>() {{
            put(Constants.TYPE, PageType.DEFAULT_NAV_BAR.toString());
        }});

        return WidgetInfo.builder()
                .queryParams(request.getQueryParamMap())
                .pageParams(request.getPageParams())
                .pageRequestContext(request.getPageRequestContext())
                .context(request.getRequestContext())
                .headers(request.getHeaders())
                .refetched(request.isRefetched())
                .slot(slot)
                .build();
    }
}
