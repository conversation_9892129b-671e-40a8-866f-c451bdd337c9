package com.flipkart.sm.pages.dp.constants;


public class Constants {

    // Application data constants

    // USED TO FETCH INFORMATION FROM slot params
    public static final String TYPE = "TYPE";
    public static final String PRODUCT_TYPE = "productType";
    public static final String DELIMITER_DOUBLE_UNDERSCORE = "__";
    public static final String ACCOUNT_STATUS = "ACCOUNT_STATUS";
    public static final String CREDIT_METRIC = "CREDIT_METRIC";
    public static final String CREDIT_SCORE_CATEGORY = "CREDIT_SCORE_CATEGORY";

    // Page config tags
    public static final String ORG_KEY = "org";
    public static final String SUPERMONEY = "supermoney";
    public static final String USER_CREDIT_REPORT_STATUS = "user_credit_report_state";
    public static final String EXISTING_CREDIT_REPORT = "EXISTING_REPORT";
    public static final String NO_EXISTING_CREDIT_REPORT = "NO_EXISTING_REPORT";
    public static final String COMING_SOON = "COMING_SOON";

    public static final String PARTNER_REPORT_CONSENT_URL = "/creditscore/checkscore/partnerReportConsent";
    public static final String PARTNER_CONSENT_BOTTTOMSHEET_NUDGE = "PARTNER_CONSENT_BOTTTOMSHEET_NUDGE";
    public static final String BANK_IMAGE = "https://static-assets-web.flixcart.com/sm-payments/images/upi_accounts/base_bank.png";
    public static final String CREDIT_CARD_IMAGE = "https://static-assets-web.flixcart.com/sm-payments/images/card_settings/upi_settings_card_dark.png";
    public static final String BANK_IMAGES_PATH = "template/creditscore/bankImages.json";

    public static final String CREDIT_CARD_ACCOUNT_TYPE = "10";
    public static final String UNDER_THOUSAND_DECIMAL_PATTERN = "##0.00";
    public static final String UNDER_THOUSAND_INTEGER_PATTERN = "##0";
    public static final String OVER_THOUSAND_INTEGER_FLOW = "000";
    public static final String OVER_THOUSAND_DECIMAL_FLOW = "000.00";
}
