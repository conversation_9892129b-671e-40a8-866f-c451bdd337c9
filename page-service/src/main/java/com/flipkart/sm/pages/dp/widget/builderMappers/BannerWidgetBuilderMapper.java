package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.BannerWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CheckCreditScoreBannerWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CheckCreditScorePLBannerWidgetBuilder;

public class BannerWidgetBuilderMapper {

    public static WidgetBuilder<BannerWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case CHECK_CREDIT_SCORE:
                return new CheckCreditScoreBannerWidgetBuilder(widgetInfo);
            case CHECK_CREDIT_SCORE_PL :
                return new CheckCreditScorePLBannerWidgetBuilder(widgetInfo);

            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
