package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.ButtonListWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.NoCsButtonWidgetBuilder;

public class ButtonListWidgetBuilderMapper {

    public static WidgetBuilder<ButtonListWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case NO_CS_AVAILABLE:
                return new NoCsButtonWidgetBuilder(widgetInfo);
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
