package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.NavigationBarWidgetData;
import com.flipkart.sm.pages.config.StaticResourcesConfigUtils;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;


public class CashComingSoonNavBarWidgetBuilder extends WidgetBuilder<NavigationBarWidgetData> {
    private static final String WIDGET_IDENTIFIER = "NAV_COMING_SOON_CASH";
    private final WidgetTemplateProvider widgetTemplateProvider;

    public CashComingSoonNavBarWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public NavigationBarWidgetData getWidgetData() {
        return widgetTemplateProvider.getWidgetTemplate( WIDGET_IDENTIFIER,
                new TypeReference<NavigationBarWidgetData>() {}
        );
    }
}
