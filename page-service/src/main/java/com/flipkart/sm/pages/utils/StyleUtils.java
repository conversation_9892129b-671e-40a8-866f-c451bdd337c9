package com.flipkart.sm.pages.utils;

import com.flipkart.rome.datatypes.response.common.Style;
import com.flipkart.rome.datatypes.response.common.enums.ButtonViewType;
import com.flipkart.rome.datatypes.response.common.enums.FontWeight;
import com.flipkart.rome.datatypes.response.common.enums.TextAlign;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValueStyle;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichButtonValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.rome.datatypes.response.fintech.supermoney.CreditScoreMetadata;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.CreditScoreCategory;
import com.flipkart.sm.pages.dp.constants.style.Colors;
import com.flipkart.sm.pages.dp.constants.style.FontFamily;

import java.util.HashMap;
import java.util.Map;

public class StyleUtils {

    public static TextStyle buildTextStyle(Colors color, FontFamily fontFamily, int fontSize, FontWeight fontWeight) {
        TextStyle textStyle = new TextStyle();
        textStyle.setColor(color.name());
        textStyle.setFontFamily(fontFamily.name());
        textStyle.setFontSize(fontSize);
        textStyle.setFontWeight(fontWeight);
        return textStyle;
    }

    public static TextStyle buildTextStyle(Colors color, FontFamily fontFamily, int fontSize, FontWeight fontWeight, int lineHeight) {
        TextStyle textStyle = buildTextStyle(color, fontFamily, fontSize, fontWeight);
        textStyle.setLineHeight(lineHeight);
        return textStyle;
    }

    public static TextStyle buildTextStyle(Colors color, int fontSize, FontWeight fontWeight, TextAlign textAlign, int lineHeight) {
        TextStyle textStyle = new TextStyle();
        textStyle.setColor(color.name());
        textStyle.setFontSize(fontSize);
        textStyle.setFontWeight(fontWeight);
        textStyle.setTextAlign(textAlign);
        textStyle.setLineHeight(lineHeight);
        return textStyle;
    }

    public static Style buildButtonListWidgetStyle(String backgroundColor, int flex, String flexDirection) {
        Style style = new Style();
        style.setBackgroundColor(backgroundColor);
        style.setFlex(flex);
        style.setFlexDirection(flexDirection);
        return style;
    }

    public static Style buildCardWidgetStyle() {
        Style style = new Style();
        style.setJustifyContent("space-between");
        style.setFlex(1);
        style.setAlignItems("center");
        return style;
    }

    public static Style buildReviewCardSummaryListHeaderStyle() {
        Style style = new Style();
        style.setMarginBottom(16);
        return style;
    }

    public static TextStyle buildDescriptionTextStyleCardWidget() {
        TextStyle textStyle = new TextStyle();
        textStyle.setFontSize(16);
        textStyle.setFontWeight(FontWeight.semibold);
        textStyle.setColor("#475467");
        textStyle.setTextAlign(TextAlign.left);
        textStyle.setLineHeight(20);
        return textStyle;
    }

    public static void buildRichButtonValueStyle(RichButtonValue richButtonValue) {
        richButtonValue.setButtonColor("#B5EF85");
        richButtonValue.setButtonTextColor("#1D2939");
        richButtonValue.setButtonTextSize(18);
        richButtonValue.setButtonTextWeight("semibold");
        richButtonValue.setButtonViewType(ButtonViewType.SOLID);
    }

    public final static Map<String, String> bankToImageUrlConfigKeyMapping = createBankToImageUrlConfigKeyMapping();

    private static Map<String, String> createBankToImageUrlConfigKeyMapping () {
        Map<String, String> map = new HashMap<>();
        map.put("", "");
        return map;
    }
    public static void  setColorForCreditScore(CreditScoreMetadata creditScoreMetadata, CreditScoreCategory category){
        switch (category){
            case EXCELLENT:creditScoreMetadata.setSecondaryColor(Colors.Sucess500.toString()); break;
            case GOOD:creditScoreMetadata.setSecondaryColor(Colors.Warn600.toString()); break;
            default :creditScoreMetadata.setSecondaryColor(Colors.Red500.toString()); break;
        }
    }

    public static ImageValue buildImageValue(String url, int height, int width ) {
        ImageValue imageValue = new ImageValue();
        ImageValueStyle style = new ImageValueStyle();
        imageValue.setType("ImageValue");
        imageValue.setSource(url);
        imageValue.setDynamicImageUrl(url);
        imageValue.setHeight(height);
        imageValue.setWidth(width);
        style.setBorderColor(Colors.SuperGray200.toString());
        imageValue.setStyle(style);
        return imageValue;
    }
}
