package com.flipkart.sm.pages.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.mobile.parser.vo.Device;

import java.io.IOException;

public class DeviceDeserializer extends JsonDeserializer<Device> {

  @Override
  public Device deserialize(JsonParser jp, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jp.getCodec();
    JsonNode node = oc.readTree(jp);

    final String family = node.get("family").asText();
    final String type = node.get("type").asText();

    return new Device(family, type);
  }
}