package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SmFooterWidgetData;
import com.flipkart.sm.pages.config.StaticResourcesConfigUtils;
import com.flipkart.sm.pages.dp.constants.ImageUrlConfigKeys;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.utils.WidgetUtils;

public class PoweredByExperianFooterWidgetBuilder extends WidgetBuilder<SmFooterWidgetData> {

    private final StaticResourcesConfigUtils staticResourcesConfigUtils;

    public PoweredByExperianFooterWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        staticResourcesConfigUtils = GuiceInjector.getInjector().getInstance(StaticResourcesConfigUtils.class);;
    }

    @Override
    public SmFooterWidgetData getWidgetData() {

        ImageValue imageValue = WidgetUtils.buildImageValue(
                "Powered by Experain", 40, 312,
                staticResourcesConfigUtils.getString(ImageUrlConfigKeys.POWER_BY_EXPERIAN)
        );

        SmFooterWidgetData smFooterWidgetData = new SmFooterWidgetData();
        smFooterWidgetData.setImageValue(imageValue);
        return smFooterWidgetData;
    }

}
