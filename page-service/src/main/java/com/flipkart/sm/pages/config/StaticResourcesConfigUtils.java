package com.flipkart.sm.pages.config;

import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public class StaticResourcesConfigUtils {

    DynamicBucket dynamicBucket;

    @Inject
    StaticResourcesConfigUtils(@Named("pageServiceStaticResources") DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
    }

    public String getString(String key) {
        return getString(dynamicBucket, key);
    }

    public String getString(String key, String defaultValue) {
        return getString(dynamicBucket, key, defaultValue);
    }

    public String getString(DynamicBucket dynamicBucket, String key) {
        return dynamicBucket.getString(key);
    }

    public String getString(DynamicBucket dynamicBucket, String key, String defaultValue) {
        String value = dynamicBucket.getString(key);
        if (value == null) {
            value = defaultValue;
        }

        return value;
    }
    public Integer getInt(String key) {
        return getInt(dynamicBucket, key);
    }

    public Integer getInt(String key, Integer defaultValue) {
        return getInt(dynamicBucket, key, defaultValue);
    }

    public Integer getInt(DynamicBucket dynamicBucket, String key) {
        return dynamicBucket.getInt(key);
    }

    public Integer getInt(DynamicBucket dynamicBucket, String key, Integer defaultValue) {
        Integer value = dynamicBucket.getInt(key);
        if (value == null) {
            value = defaultValue;
        }

        return value;
    }

    public String getBankImageForCS(String bankName){
        Map<String, Object> mapOfImages = getObjectMap("bankNameToImageMap");
        return String.valueOf(mapOfImages.getOrDefault(bankName,""));
    }

    private Map<String, Object> getObjectMap(String mapNameKey) {
        Object object = this.dynamicBucket.getBucket().getKeys().get(mapNameKey);
        if(Objects.nonNull(object)){
            return  (Map<String, Object>) object;
        }
        return new HashMap<>();
    }
}
