package com.flipkart.sm.pages.dp.widget.provider.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.sm.pages.config.PageServiceConfig;
import com.flipkart.sm.pages.utils.FileUtils;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@CustomLog
@Singleton
public class WidgetConfigProviderImpl implements WidgetConfigProvider {

    private final Map<String, JsonNode> widgetConfigMap;

    @Inject
    public WidgetConfigProviderImpl(PageServiceConfig pageServiceConfig) {
        this.widgetConfigMap = new HashMap<>();
        pageServiceConfig.getWidgetConfigMap().forEach((key, path) -> {
            try {
                this.widgetConfigMap.put(key, ObjectMapperUtil.get().readTree(FileUtils.readFileasString(path)));
            } catch (IOException e) {
                log.error("[WidgetConfigProviderImpl] Error occurred while parsing template {}, error {}", key, e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public <WidgetConfig> WidgetConfig getWidgetConfig(String templateKey, TypeReference<WidgetConfig> typeReference) {
        try {
            return ObjectMapperUtil.get().convertValue(widgetConfigMap.get(templateKey), typeReference);
        } catch (Exception e) {
            log.error("Not able to map the parse the template ",e);
            throw new RuntimeException(e);
        }
    }
}
