package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;
import com.flipkart.sm.pages.config.StaticResourcesConfigUtils;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;


public class CSfromPartnerCardWidgetBuilder extends WidgetBuilder<CardWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CS_FROM_PARTNER_CARD";
    private final WidgetTemplateProvider widgetTemplateProvider;

    public CSfromPartnerCardWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public CardWidgetData getWidgetData() {
        CardWidgetData cardWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<CardWidgetData>() {});
        return cardWidgetData;
    }
}