package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SmFooterWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;

public class PoweredByUPIFooterWidgetBuilder extends WidgetBuilder<SmFooterWidgetData> {

    private static final String WIDGET_IDENTIFIER = "POWERED_BY_UPI_FOOTER";
    private final WidgetTemplateProvider widgetTemplateProvider;

    public PoweredByUPIFooterWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public SmFooterWidgetData getWidgetData() {
        SmFooterWidgetData smFooterWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<SmFooterWidgetData>() {
        });
        return smFooterWidgetData;
    }

}
