package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.library.Feature;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import org.junit.Test;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.InputStream;

import static org.junit.Assert.*;

public class LeadDetailsTest {

    @Test
    public void create() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("LeadDetails.json");
        LeadDetails leadDetails = objectMapper.readValue(resourceAsStream, LeadDetails.class);
        assertNotNull(leadDetails);
        assertEquals(78, leadDetails.getUserProfile().getProfileId());
        assertEquals(560103, leadDetails.getUserProfile().getUserEnteredPincode());
    }

    @Test
    public void testLeadDetailsBuilder() {
        // Given
        String leadId = "test-lead-123";
        LeadDetails.LeadState state = LeadDetails.LeadState.NAME_PAGE;
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("key1", "value1");
        applicationData.put("key2", 123);
        
        ProfileDetailedResponse userProfile = new ProfileDetailedResponse();
        Map<Feature, String> experianData = new HashMap<>();

        String experianReport = "test-experian-report";
        UserScores userScores = new UserScores();
        Integer monthlyIncome = 50000;
        Integer bonusIncome = 5000;
        List<Lender> lendersToFilterOut = Arrays.asList(Lender.AXIS, Lender.IDFC);

        // When
        LeadDetails leadDetails = LeadDetails.builder()
                .leadId(leadId)
                .stateOfLead(state)
                .applicationData(applicationData)
                .userProfile(userProfile)
                .experianData(experianData)
                .experianReport(experianReport)
                .userScores(userScores)
                .monthlyIncome(monthlyIncome)
                .bonusIncome(bonusIncome)
                .lendersToFilterOut(lendersToFilterOut)
                .build();

        // Then
        assertEquals(leadId, leadDetails.getLeadId());
        assertEquals(state, leadDetails.getStateOfLead());
        assertEquals(applicationData, leadDetails.getApplicationData());
        assertEquals(userProfile, leadDetails.getUserProfile());
        assertEquals(experianData, leadDetails.getExperianData());
        assertEquals(experianReport, leadDetails.getExperianReport());
        assertEquals(userScores, leadDetails.getUserScores());
        assertEquals(monthlyIncome, leadDetails.getMonthlyIncome());
        assertEquals(bonusIncome, leadDetails.getBonusIncome());
        assertEquals(lendersToFilterOut, leadDetails.getLendersToFilterOut());
    }

    @Test
    public void testLeadDetailsNoArgsConstructor() {
        // When
        LeadDetails leadDetails = new LeadDetails();

        // Then
        assertNull(leadDetails.getLeadId());
        assertNull(leadDetails.getStateOfLead());
        assertNull(leadDetails.getApplicationData());
        assertNull(leadDetails.getUserProfile());
        assertNull(leadDetails.getExperianData());
        assertNull(leadDetails.getExperianReport());
        assertNull(leadDetails.getUserScores());
        assertNull(leadDetails.getMonthlyIncome());
        assertEquals(Integer.valueOf(0), leadDetails.getBonusIncome()); // Default value
        assertNull(leadDetails.getLendersToFilterOut());
    }

    @Test
    public void testLeadDetailsAllArgsConstructor() {
        // Given
        String leadId = "test-lead-456";
        LeadDetails.LeadState state = LeadDetails.LeadState.REVIEW_PAGE_1;
        ApplicationUserData applicationUserData = new ApplicationUserData();
        Map<String, Object> applicationData = new HashMap<>();
        ProfileDetailedResponse userProfile = new ProfileDetailedResponse();
        Map<Feature, String> experianData = new HashMap<>();
        String experianReport = "report";
        UserScores userScores = new UserScores();
        Integer monthlyIncome = 60000;
        Integer bonusIncome = 6000;
        List<Lender> lendersToFilterOut = Arrays.asList(Lender.MONEYVIEW);

        // When
        LeadDetails leadDetails = new LeadDetails(
                state, applicationData, applicationUserData, leadId, userProfile, experianData,
                experianReport, userScores, monthlyIncome, bonusIncome, lendersToFilterOut
        );

        // Then
        assertEquals(leadId, leadDetails.getLeadId());
        assertEquals(state, leadDetails.getStateOfLead());
        assertEquals(applicationData, leadDetails.getApplicationData());
        assertEquals(userProfile, leadDetails.getUserProfile());
        assertEquals(experianData, leadDetails.getExperianData());
        assertEquals(experianReport, leadDetails.getExperianReport());
        assertEquals(userScores, leadDetails.getUserScores());
        assertEquals(monthlyIncome, leadDetails.getMonthlyIncome());
        assertEquals(bonusIncome, leadDetails.getBonusIncome());
        assertEquals(lendersToFilterOut, leadDetails.getLendersToFilterOut());
    }

    @Test
    public void testLeadDetailsSetters() {
        // Given
        LeadDetails leadDetails = new LeadDetails();
        String leadId = "test-lead-789";
        LeadDetails.LeadState state = LeadDetails.LeadState.BASIC_DETAILS;
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("test", "data");

        // When
        leadDetails.setLeadId(leadId);
        leadDetails.setStateOfLead(state);
        leadDetails.setApplicationData(applicationData);
        leadDetails.setMonthlyIncome(70000);
        leadDetails.setBonusIncome(7000);

        // Then
        assertEquals(leadId, leadDetails.getLeadId());
        assertEquals(state, leadDetails.getStateOfLead());
        assertEquals(applicationData, leadDetails.getApplicationData());
        assertEquals(Integer.valueOf(70000), leadDetails.getMonthlyIncome());
        assertEquals(Integer.valueOf(7000), leadDetails.getBonusIncome());
    }

    @Test
    public void testLeadDetailsToString() {
        // Given
        LeadDetails leadDetails = LeadDetails.builder()
                .leadId("test-lead")
                .stateOfLead(LeadDetails.LeadState.NAME_PAGE)
                .monthlyIncome(50000)
                .bonusIncome(5000)
                .build();

        // When
        String toString = leadDetails.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("LeadDetails"));
        assertTrue(toString.contains("test-lead"));
        assertTrue(toString.contains("NAME_PAGE"));
        assertTrue(toString.contains("50000"));
        assertTrue(toString.contains("5000"));
    }

    @Test
    public void testLeadStateEnum() {
        // Test all enum values exist and can be accessed
        assertEquals("NAME_PAGE", LeadDetails.LeadState.NAME_PAGE.name());
        assertEquals("REVIEW_PAGE_1", LeadDetails.LeadState.REVIEW_PAGE_1.name());
        assertEquals("REVIEW_PAGE_2", LeadDetails.LeadState.REVIEW_PAGE_2.name());
        assertEquals("BASIC_DETAILS", LeadDetails.LeadState.BASIC_DETAILS.name());
        assertEquals("VERIFY_PINCODE_START", LeadDetails.LeadState.VERIFY_PINCODE_START.name());
        assertEquals("VERIFY_PINCODE_END", LeadDetails.LeadState.VERIFY_PINCODE_END.name());
        assertEquals("VERIFY_PAN_START", LeadDetails.LeadState.VERIFY_PAN_START.name());
        assertEquals("VERIFY_PAN_END", LeadDetails.LeadState.VERIFY_PAN_END.name());
        assertEquals("PULL_EXPERIAN_START", LeadDetails.LeadState.PULL_EXPERIAN_START.name());
        assertEquals("PULL_EXPERIAN_END", LeadDetails.LeadState.PULL_EXPERIAN_END.name());
        assertEquals("CREATE_PROFILE_START", LeadDetails.LeadState.CREATE_PROFILE_START.name());
        assertEquals("CREATE_PROFILE_END", LeadDetails.LeadState.CREATE_PROFILE_END.name());
        assertEquals("LEAD_V3_NAME_PAGE", LeadDetails.LeadState.LEAD_V3_NAME_PAGE.name());
        assertEquals("LEAD_V3_PAGE_1", LeadDetails.LeadState.LEAD_V3_PAGE_1.name());
        assertEquals("LEAD_V3_PAGE_2", LeadDetails.LeadState.LEAD_V3_PAGE_2.name());
        assertEquals("LEAD_V4_LANDING_PAGE", LeadDetails.LeadState.LEAD_V4_LANDING_PAGE.name());
    }

    @Test
    public void testLeadStateEnumValues() {
        // Test that all expected enum values are present
        LeadDetails.LeadState[] values = LeadDetails.LeadState.values();
        assertEquals(18, values.length);
        
        // Test valueOf
        assertEquals(LeadDetails.LeadState.NAME_PAGE, LeadDetails.LeadState.valueOf("NAME_PAGE"));
        assertEquals(LeadDetails.LeadState.LEAD_V4_LANDING_PAGE, LeadDetails.LeadState.valueOf("LEAD_V4_LANDING_PAGE"));
    }

    @Test
    public void testLeadDetailsWithNullValues() {
        // Given
        LeadDetails leadDetails = LeadDetails.builder()
                .leadId(null)
                .stateOfLead(null)
                .applicationData(null)
                .userProfile(null)
                .experianData(null)
                .experianReport(null)
                .userScores(null)
                .monthlyIncome(null)
                .bonusIncome(null)
                .lendersToFilterOut(null)
                .build();

        // Then
        assertNull(leadDetails.getLeadId());
        assertNull(leadDetails.getStateOfLead());
        assertNull(leadDetails.getApplicationData());
        assertNull(leadDetails.getUserProfile());
        assertNull(leadDetails.getExperianData());
        assertNull(leadDetails.getExperianReport());
        assertNull(leadDetails.getUserScores());
        assertNull(leadDetails.getMonthlyIncome());
        assertNull(leadDetails.getBonusIncome());
        assertNull(leadDetails.getLendersToFilterOut());
    }

    @Test
    public void testLeadDetailsWithEmptyCollections() {
        // Given
        Map<String, Object> emptyApplicationData = new HashMap<>();
        Map<Feature, String> emptyExperianData = new HashMap<>();
        List<Lender> emptyLendersList = new ArrayList<>();

        // When
        LeadDetails leadDetails = LeadDetails.builder()
                .applicationData(emptyApplicationData)
                .experianData(emptyExperianData)
                .lendersToFilterOut(emptyLendersList)
                .build();

        // Then
        assertEquals(emptyApplicationData, leadDetails.getApplicationData());
        assertEquals(emptyExperianData, leadDetails.getExperianData());
        assertEquals(emptyLendersList, leadDetails.getLendersToFilterOut());
        assertTrue(leadDetails.getApplicationData().isEmpty());
        assertTrue(leadDetails.getExperianData().isEmpty());
        assertTrue(leadDetails.getLendersToFilterOut().isEmpty());
    }
}
