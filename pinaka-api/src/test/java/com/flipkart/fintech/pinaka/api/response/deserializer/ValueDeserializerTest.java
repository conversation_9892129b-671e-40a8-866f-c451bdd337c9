package com.flipkart.fintech.pinaka.api.response.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.ButtonValue;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test class for ValueDeserializer
 * Tests deserialization of various Value types and error handling scenarios
 */
@RunWith(MockitoJUnitRunner.class)
public class ValueDeserializerTest {

    private ValueDeserializer valueDeserializer;
    private ObjectMapper objectMapper;

    @Mock
    private JsonParser jsonParser;

    @Mock
    private DeserializationContext context;

    @Before
    public void setUp() {
        valueDeserializer = new ValueDeserializer();
        objectMapper = new ObjectMapper();
        
        // Register the ValueDeserializer with ObjectMapper
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Value.class, valueDeserializer);
        objectMapper.registerModule(module);
    }

    @Test
    public void testConstructorWithNullParameter() {
        ValueDeserializer deserializer = new ValueDeserializer();
        assertNotNull(deserializer);
    }

    @Test
    public void testConstructorWithClassParameter() {
        ValueDeserializer deserializer = new ValueDeserializer(Value.class);
        assertNotNull(deserializer);
    }

    @Test
    public void testDeserializerIsActuallyCalled() throws IOException {
        // This test ensures the deserializer is actually being invoked
        String json = "{"
                + "\"type\": \"RichTextValue\","
                + "\"text\": \"Test to ensure deserializer is called\""
                + "}";

        // Use the deserializer directly to ensure it's being tested
        JsonParser parser = objectMapper.getFactory().createParser(json);
        parser.nextToken(); // Move to START_OBJECT

        Value result = valueDeserializer.deserialize(parser, objectMapper.getDeserializationContext());

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        assertEquals("RichTextValue", result.getType());
    }

    @Test
    public void testDeserializeRichTextValue() throws IOException {
        String json = "{"
                + "\"type\": \"RichTextValue\","
                + "\"text\": \"Sample text\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        RichTextValue richTextValue = (RichTextValue) result;
        assertEquals("RichTextValue", richTextValue.getType());
    }

    @Test
    public void testDeserializeImageValue() throws IOException {
        String json = "{"
                + "\"type\": \"ImageValue\","
                + "\"source\": \"https://example.com/image.jpg\","
                + "\"height\": 100,"
                + "\"width\": 200"
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof ImageValue);
        ImageValue imageValue = (ImageValue) result;
        assertEquals("ImageValue", imageValue.getType());
    }

    @Test
    public void testDeserializeButtonValue() throws IOException {
        String json = "{"
                + "\"type\": \"ButtonValue\","
                + "\"title\": \"Click me\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof ButtonValue);
        ButtonValue buttonValue = (ButtonValue) result;
        assertEquals("ButtonValue", buttonValue.getType());
    }

    @Test(expected = IOException.class)
    public void testDeserializeMissingTypeField() throws IOException {
        String json = "{"
                + "\"text\": \"Sample text\","
                + "\"color\": \"#000000\""
                + "}";

        objectMapper.readValue(json, Value.class);
    }

    @Test(expected = IOException.class)
    public void testDeserializeNullTypeField() throws IOException {
        String json = "{"
                + "\"type\": null,"
                + "\"text\": \"Sample text\""
                + "}";

        objectMapper.readValue(json, Value.class);
    }

    @Test
    public void testDeserializeUnknownType() throws IOException {
        String json = "{"
                + "\"type\": \"UnknownValueType\","
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for unknown type");
        } catch (IOException e) {
            assertTrue(e.getMessage().contains("Unknown Value type: UnknownValueType"));
        }
    }

    @Test
    public void testDeserializeEmptyTypeField() throws IOException {
        String json = "{"
                + "\"type\": \"\","
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for empty type");
        } catch (IOException e) {
            assertTrue(e.getMessage().contains("Unknown Value type:"));
        }
    }

    @Test
    public void testDeserializeCreativeCardValue() throws IOException {
        String json = "{"
                + "\"type\": \"CreativeCardValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("CreativeCardValue", result.getType());
    }

    @Test
    public void testDeserializeAnnouncementValue() throws IOException {
        String json = "{"
                + "\"type\": \"AnnouncementValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("AnnouncementValue", result.getType());
    }

    @Test
    public void testDeserializeHeaderValue() throws IOException {
        String json = "{"
                + "\"type\": \"HeaderValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("HeaderValue", result.getType());
    }

    @Test
    public void testDeserializeFooterValue() throws IOException {
        String json = "{"
                + "\"type\": \"FooterValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("FooterValue", result.getType());
    }

    // Note: WidgetSharedData test removed because it doesn't have a 'type' field
    // and may not properly extend Value class

    @Test
    public void testDeserializeStatefulRichButtonValue() throws IOException {
        String json = "{"
                + "\"type\": \"StatefulRichButtonValue\","
                + "\"title\": \"Submit\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("StatefulRichButtonValue", result.getType());
    }

    @Test
    public void testDeserializeFormattedMessageValue() throws IOException {
        String json = "{"
                + "\"type\": \"FormattedMessageValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("FormattedMessageValue", result.getType());
    }

    @Test
    public void testDeserializeRichAnnouncementValue() throws IOException {
        String json = "{"
                + "\"type\": \"RichAnnouncementValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("RichAnnouncementValue", result.getType());
    }

    @Test
    public void testDeserializeComplexJsonStructure() throws IOException {
        String json = "{"
                + "\"type\": \"RichTextValue\","
                + "\"text\": \"Complex text\","
                + "\"style\": {"
                + "  \"color\": \"#FF0000\","
                + "  \"fontSize\": 16,"
                + "  \"fontWeight\": \"bold\""
                + "}"
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        assertEquals("RichTextValue", result.getType());
    }

    @Test
    public void testDeserializeWithSpecialCharacters() throws IOException {
        String json = "{"
                + "\"type\": \"RichTextValue\","
                + "\"text\": \"Text with special chars: \\\"quotes\\\", \\n newlines, \\t tabs\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        assertEquals("RichTextValue", result.getType());
    }

    @Test
    public void testErrorMessageForMissingType() throws IOException {
        String json = "{"
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for missing type field");
        } catch (IOException e) {
            assertTrue("Error message should contain the expected text",
                e.getMessage().contains("ValueDeserializer - 'type' field is missing in Value object"));
        }
    }

    @Test
    public void testErrorMessageForUnknownType() throws IOException {
        String json = "{"
                + "\"type\": \"NonExistentValueType\","
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for unknown type");
        } catch (IOException e) {
            assertTrue("Error message should contain the expected text",
                e.getMessage().contains("ValueDeserializer - Unknown Value type: NonExistentValueType. Please add mapping for this type."));
        }
    }

    @Test
    public void testDeserializePrimitiveCard() throws IOException {
        String json = "{"
                + "\"type\": \"PrimitiveCard\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertEquals("PrimitiveCard", result.getType());
    }

    @Test
    public void testDeserializeMinimalValidJson() throws IOException {
        String json = "{"
                + "\"type\": \"RichTextValue\""
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        assertEquals("RichTextValue", result.getType());
    }

    @Test
    public void testDeserializeWithValidImageFields() throws IOException {
        String json = "{"
                + "\"type\": \"ImageValue\","
                + "\"source\": \"https://example.com/image.jpg\","
                + "\"height\": 100,"
                + "\"width\": 200"
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof ImageValue);
        assertEquals("ImageValue", result.getType());
    }

    @Test
    public void testDeserializeWithNumericType() throws IOException {
        // Test case where type field is numeric (should be converted to string)
        String json = "{"
                + "\"type\": 123,"
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for numeric type");
        } catch (IOException e) {
            assertTrue(e.getMessage().contains("Unknown Value type: 123"));
        }
    }

    @Test
    public void testDeserializeWithBooleanType() throws IOException {
        // Test case where type field is boolean (should be converted to string)
        String json = "{"
                + "\"type\": true,"
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for boolean type");
        } catch (IOException e) {
            assertTrue(e.getMessage().contains("Unknown Value type: true"));
        }
    }

    @Test
    public void testDeserializeWithArrayType() throws IOException {
        // Test case where type field is an array (should cause error)
        String json = "{"
                + "\"type\": [\"RichTextValue\"],"
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected exception for array type");
        } catch (Exception e) {
            // Should fail during JSON parsing or type extraction
            assertNotNull(e);
        }
    }

    @Test
    public void testDeserializeWithObjectType() throws IOException {
        // Test case where type field is an object (should cause error)
        String json = "{"
                + "\"type\": {\"value\": \"RichTextValue\"},"
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected exception for object type");
        } catch (Exception e) {
            // Should fail during JSON parsing or type extraction
            assertNotNull(e);
        }
    }

    @Test
    public void testDeserializeCaseSensitiveType() throws IOException {
        // Test case sensitivity of type field
        String json = "{"
                + "\"type\": \"richtextvalue\","
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for case-sensitive type");
        } catch (IOException e) {
            assertTrue(e.getMessage().contains("Unknown Value type: richtextvalue"));
        }
    }

    @Test
    public void testDeserializeWithWhitespaceInType() throws IOException {
        // Test type field with whitespace
        String json = "{"
                + "\"type\": \" RichTextValue \","
                + "\"text\": \"Sample text\""
                + "}";

        try {
            objectMapper.readValue(json, Value.class);
            fail("Expected IOException for type with whitespace");
        } catch (IOException e) {
            assertTrue(e.getMessage().contains("Unknown Value type:  RichTextValue "));
        }
    }

    // Tests using JSON resource files
    @Test
    public void testDeserializeRichTextValueFromFile() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("value-deserializer/rich-text-value.json")) {
            assertNotNull("Test resource file not found", is);

            Value result = objectMapper.readValue(is, Value.class);

            assertNotNull(result);
            assertTrue(result instanceof RichTextValue);
            assertEquals("RichTextValue", result.getType());
        }
    }

    @Test
    public void testDeserializeImageValueFromFile() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("value-deserializer/image-value.json")) {
            assertNotNull("Test resource file not found", is);

            Value result = objectMapper.readValue(is, Value.class);

            assertNotNull(result);
            assertTrue(result instanceof ImageValue);
            assertEquals("ImageValue", result.getType());
        }
    }

    @Test
    public void testDeserializeButtonValueFromFile() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("value-deserializer/button-value.json")) {
            assertNotNull("Test resource file not found", is);

            Value result = objectMapper.readValue(is, Value.class);

            assertNotNull(result);
            assertTrue(result instanceof ButtonValue);
            assertEquals("ButtonValue", result.getType());
        }
    }

    @Test
    public void testDeserializeMissingTypeFromFile() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("value-deserializer/missing-type.json")) {
            assertNotNull("Test resource file not found", is);

            try {
                objectMapper.readValue(is, Value.class);
                fail("Expected IOException for missing type field");
            } catch (IOException e) {
                assertEquals("ValueDeserializer - 'type' field is missing in Value object", e.getMessage());
            }
        }
    }

    @Test
    public void testDeserializeUnknownTypeFromFile() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("value-deserializer/unknown-type.json")) {
            assertNotNull("Test resource file not found", is);

            try {
                objectMapper.readValue(is, Value.class);
                fail("Expected IOException for unknown type");
            } catch (IOException e) {
                assertEquals("ValueDeserializer - Unknown Value type: UnknownValueType. Please add mapping for this type.", e.getMessage());
            }
        }
    }

    @Test
    public void testDeserializeMultipleValuesFromArray() throws IOException {
        String jsonArray = "["
                + "{"
                + "  \"type\": \"RichTextValue\","
                + "  \"text\": \"First text\""
                + "},"
                + "{"
                + "  \"type\": \"ImageValue\","
                + "  \"source\": \"https://example.com/image.jpg\","
                + "  \"height\": 100,"
                + "  \"width\": 200"
                + "},"
                + "{"
                + "  \"type\": \"ButtonValue\","
                + "  \"title\": \"Click me\""
                + "}"
                + "]";

        Value[] results = objectMapper.readValue(jsonArray, Value[].class);

        assertNotNull(results);
        assertEquals(3, results.length);

        assertTrue(results[0] instanceof RichTextValue);
        assertEquals("RichTextValue", results[0].getType());

        assertTrue(results[1] instanceof ImageValue);
        assertEquals("ImageValue", results[1].getType());

        assertTrue(results[2] instanceof ButtonValue);
        assertEquals("ButtonValue", results[2].getType());
    }

    @Test
    public void testDeserializeNestedValueStructure() throws IOException {
        String json = "{"
                + "\"wrapper\": {"
                + "  \"value\": {"
                + "    \"type\": \"RichTextValue\","
                + "    \"text\": \"Nested text\""
                + "  }"
                + "}"
                + "}";

        // Parse the outer structure first
        JsonNode rootNode = objectMapper.readTree(json);
        JsonNode valueNode = rootNode.get("wrapper").get("value");

        Value result = objectMapper.treeToValue(valueNode, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        assertEquals("RichTextValue", result.getType());
    }

    @Test
    public void testDeserializeWithNullValues() throws IOException {
        String json = "{"
                + "\"type\": \"RichTextValue\","
                + "\"text\": null"
                + "}";

        Value result = objectMapper.readValue(json, Value.class);

        assertNotNull(result);
        assertTrue(result instanceof RichTextValue);
        assertEquals("RichTextValue", result.getType());
    }
}
