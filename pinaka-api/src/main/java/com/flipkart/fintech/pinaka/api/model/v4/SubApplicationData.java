package com.flipkart.fintech.pinaka.api.model.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.SubApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.SubApplicationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * suapplication data model for pinaka tenant in AMS
 */
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class SubApplicationData {
    private Map<String, Object>  data;
    private String               externalRefId;
    private SubApplicationStatus status;
    private SubApplicationType   type;
}
