package com.flipkart.fintech.pinaka.api.request.v4.kyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateAadhaarOtpRequest extends KycUpdateRequest {

    @NonNull
    private String encryptedAadhaarNumber;
    private EncryptionKeyData encryptionKeyData;

}
