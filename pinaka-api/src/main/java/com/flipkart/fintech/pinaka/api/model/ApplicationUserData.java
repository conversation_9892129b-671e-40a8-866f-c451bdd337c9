package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.profile.model.EmploymentType;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplicationUserData {
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String dob;
    private String city;
    private String state;
    private Integer pincode;
    private String gender;
    private String area;
    private String email;
    private String pan;
    private String loanPurpose;
    private String houseNumber;
    private EmploymentType employmentType;
    private String income;
    private String bonusIncome;
    private Object organization;
    private String incomeSource;
    private Object industryName;
    private String annualTurnOver;
    private Object consentListData;
    private Object consentData;
    private PaOffer paOffer;
}
