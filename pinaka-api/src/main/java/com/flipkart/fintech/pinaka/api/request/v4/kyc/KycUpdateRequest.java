package com.flipkart.fintech.pinaka.api.request.v4.kyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import com.flipkart.fintech.pinaka.api.enums.KycUpdateContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(
        property = "context",
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        use = JsonTypeInfo.Id.NAME,
        visible = true
) @JsonSubTypes({@JsonSubTypes.Type(
        name = "AADHAAR_XML_PAN_SUBMIT",
        value = PanSubmitRequest.class
), @JsonSubTypes.Type(
        name = "INITIATE_AADHAAR_VERIFICATION",
        value = KycUpdateRequest.class
), @JsonSubTypes.Type(
        name = "KYC_REVIEW_AND_SUBMIT",
        value = KycUpdateRequest.class
), @JsonSubTypes.Type(
        name = "KYC_DISCARD",
        value = KycUpdateRequest.class
),@JsonSubTypes.Type(
        name = "KYC_RETRY",
        value = KycUpdateRequest.class
),@JsonSubTypes.Type(
        name = "KYC_DOCUMENT_UPLOAD",
        value = UploadDocumentRequest.class
),@JsonSubTypes.Type(
        name = "CKYC_PAN_SUBMIT",
        value = PanSubmitRequest.class
),@JsonSubTypes.Type(
        name = "CKYC_GENERATE_OTP",
        value = GenerateAadhaarOtpRequest.class
),@JsonSubTypes.Type(
        name = "CKYC_VERIFY_OTP",
        value = VerifyAadhaarOtpRequest.class
),@JsonSubTypes.Type(
        name = "EKYC_PAN_SUBMIT",
        value = PanSubmitRequest.class
),@JsonSubTypes.Type(
        name = "EKYC_GENERATE_OTP",
        value = GenerateAadhaarOtpRequest.class
),@JsonSubTypes.Type(
        name = "EKYC_VERIFY_OTP",
        value = VerifyAadhaarOtpRequest.class
)
})
public class KycUpdateRequest {
    @NonNull
    private KycUpdateContext context;
}
