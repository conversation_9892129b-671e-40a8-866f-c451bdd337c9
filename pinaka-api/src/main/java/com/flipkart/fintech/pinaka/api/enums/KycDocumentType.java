package com.flipkart.fintech.pinaka.api.enums;

/**
 * <AUTHOR>
 * @since 28/09/20.
 */
public enum KycDocumentType {

    OTHER(com.flipkart.fintech.stratum.api.enums.kyc.KycDocumentType.OTHER),
    AADHAAR_XML(com.flipkart.fintech.stratum.api.enums.kyc.KycDocumentType.AADHAAR_XML);

  private final com.flipkart.fintech.stratum.api.enums.kyc.KycDocumentType documentType;

  KycDocumentType(com.flipkart.fintech.stratum.api.enums.kyc.KycDocumentType documentType) {
    this.documentType = documentType;
  }

  public com.flipkart.fintech.stratum.api.enums.kyc.KycDocumentType getStratumDocumentType() {
    return documentType;
  }
}
