package com.flipkart.fintech.pinaka.api.enums;

/**
 * Created by kunal.keshwani on 06/05/19.
 */
public enum CardUsageType {
    INTERNATIONAL("international"),
    DOMESTIC("domestic");

    private String value;

    CardUsageType(String value) {
        this.value = value;
    }

    public static CardUsageType fromString(String value) {
        for(CardUsageType cardUsageType : CardUsageType.values()) {
            if(cardUsageType.value.equals(value)) {
                return cardUsageType;
            }
        }
        return null;
    }
}
