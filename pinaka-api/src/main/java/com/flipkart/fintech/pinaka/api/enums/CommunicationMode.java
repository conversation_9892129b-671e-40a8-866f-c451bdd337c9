package com.flipkart.fintech.pinaka.api.enums;

public enum CommunicationMode {

    //Note : Adding all states as that of com.flipkart.affordability.collections.model.enums.CommunicationMode for backward
    // compatibility bcoz removing dependency of affordability CommunicationMode Enum wherever it is being used as it
    // doesn't provide flexibility to create our specific communicationMode and also we already had our CommunicationMode enum
    SMS,
    EMAIL,
    PN,
    IN_APP,

    IVR,
    CALL,
    IGS_CALL,
    UBONA_IVR;
}
