package com.flipkart.fintech.pinaka.api.response.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.flipkart.rome.datatypes.response.common.leaf.value.Value;
import lombok.CustomLog;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@CustomLog
public class ValueDeserializer extends StdDeserializer<Value> {

    private static final Map<String, Class<? extends Value>> TYPE_MAPPING = new HashMap<>();

    static {
        // Add mappings for all known Value subclasses based on the JSON response
        addTypeMapping("PrimitiveCard", "com.flipkart.rome.datatypes.response.fintech.supermoney.cards.PrimitiveCard");
        addTypeMapping("RichCard", "com.flipkart.rome.datatypes.response.fintech.supermoney.cards.RichCard");
        addTypeMapping("RichTextValue", "com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue");
        addTypeMapping("ImageValue", "com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue");
        addTypeMapping("ButtonValue", "com.flipkart.rome.datatypes.response.common.leaf.value.ButtonValue");
        addTypeMapping("CreativeCardValue", "com.flipkart.rome.datatypes.response.common.leaf.value.CreativeCardValue");
        addTypeMapping("RichAnnouncementValue", "com.flipkart.rome.datatypes.response.common.leaf.value.RichAnnouncementValue");
        addTypeMapping("AnnouncementValue", "com.flipkart.rome.datatypes.response.common.leaf.value.AnnouncementValue");
        addTypeMapping("HeaderValue", "com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue");
        addTypeMapping("FooterValue", "com.flipkart.rome.datatypes.response.common.leaf.value.FooterValue");
        addTypeMapping("WidgetSharedData", "com.flipkart.rome.datatypes.response.common.leaf.value.WidgetSharedData");
        addTypeMapping("StatefulRichButtonValue", "com.flipkart.rome.datatypes.response.common.leaf.value.StatefulRichButtonValue");
        addTypeMapping("FormattedMessageValue", "com.flipkart.rome.datatypes.response.common.leaf.value.product.FormattedMessageValue");
        
        // Add more mappings as needed based on the types found in your JSON responses
    }
    
    private static void addTypeMapping(String typeName, String className) {
        try {
            TYPE_MAPPING.put(typeName, (Class<? extends Value>) Class.forName(className));
        } catch (ClassNotFoundException e) {
            // Log the error but continue - some classes might not be available in all environments
            log.warn("Could not load Value subclass " + typeName + " (" + className + "): " + e.getMessage());
        }
    }

    public ValueDeserializer() {
        this(null);
    }

    public ValueDeserializer(Class<?> vc) {
        super(vc);
    }

    @Override
    public Value deserialize(JsonParser jsonParser, DeserializationContext context) throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) jsonParser.getCodec();
        JsonNode node = mapper.readTree(jsonParser);
        
        JsonNode typeNode = node.get("type");
        if (typeNode == null) {
            throw new IOException("ValueDeserializer - 'type' field is missing in Value object");
        }
        
        String typeName = typeNode.asText();
        Class<? extends Value> targetClass = TYPE_MAPPING.get(typeName);
        
        if (targetClass == null) {
            throw new IOException("ValueDeserializer - Unknown Value type: " + typeName + ". Please add mapping for this type.");
        }
        
        return (Value) mapper.treeToValue(node, targetClass);
    }
}
