package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.SessionType;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 20/08/18.
 */
public class Session {

    private SessionType type;

    private String referenceId;

    private String sessionKey;

    private Timestamp expiresAt;

    public SessionType getType() {
        return type;
    }

    public void setType(SessionType type) {
        this.type = type;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public Timestamp getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(Timestamp expiresAt) {
        this.expiresAt = expiresAt;
    }
}
