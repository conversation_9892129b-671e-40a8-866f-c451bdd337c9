package com.flipkart.fintech.pinaka.api.model.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConsentReferenceData {
    private String id; //will get this from consent service
    private boolean consentReceived;
    private String timestamp;
}
