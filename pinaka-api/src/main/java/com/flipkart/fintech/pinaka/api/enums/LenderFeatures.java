package com.flipkart.fintech.pinaka.api.enums;

public enum LenderFeatures {

    ORDER_FIRST_TS("order_first_ts",FeatureSource.CREMO),
    ACCOUNT_VINTAGE("account_vintage",FeatureSource.CREMO),
    ORDER_LAST_TS("order_last_ts",FeatureSource.CREMO),
    DAYS_SINCE_LAST_ORDER("days_since_last_order",FeatureSource.CREMO),
    ORDER_LEVEL_WINDOW_LT_ORDER_COUNT("order_level_window_lt_order_count",FeatureSource.CREMO),
    ORDER_WINDOW_LT_ORDER_VALUE("order_window_lt__order_value",FeatureSource.CREMO),
    GMV_TILL_DATE("gmv_till_date",FeatureSource.CREMO),
    ORDER_WINDOW_LT_CANCELLED_COUNT("order_window_lt_cancelled_count",FeatureSource.CREMO),
    ORDER_WINDOW_LT_RTO_COUNT("order_window_lt_rto_count",FeatureSource.CREMO),
    ORDER_WINDOW_LT_RVP_COUNT("order_window_lt_rvp_count",FeatureSource.CREMO),
    ORDER_WINDOW_LT_ORDER_COUNT("order_window_lt_order_count",FeatureSource.CREMO),
    PAYMENT_MODERS_CREDIT_CARD_EMI("payment_modes_credit_card_emi",FeatureSource.CREMO),
    PAYMENT_MODES_NETBANKING("payment_modes_netbanking",FeatureSource.CREMO),
    PAYMENT_MODES_DEBIT_CARD("payment_modes_debit_card",FeatureSource.CREMO),
    PAYMENT_MODES_CREDIT_CARD("payment_modes_credit_card",FeatureSource.CREMO),
    ORDER_LEVEL_WINDOW_LT_ORDER_VALUE_PER_MONTH_MAX1("order_level_window_lt_order_value_per_month_max1",FeatureSource.CREMO),
    HIGHTEST_GMV_IN_A_MONTH("highest_gmv_in_a_month",FeatureSource.CREMO),
    ORDER_LEVEL_WINDOW_LT_ORDER_VALUE_PER_MONTH_MAX2("order_level_window_lt_order_value_per_month_max2",FeatureSource.CREMO),
    SECOND_HIGHEST_GMV_IN_A_MONTH("second_highest_gmv_in_a_month",FeatureSource.CREMO),
    ORDER_LEVEL_WINDOW_12M_MAX_USED_ADDRESS_ORDER_COUNT("order_level_window_12m_max_used_address_order_count",FeatureSource.CREMO);

    private final String featureName;
    private final FeatureSource featureSource;

    LenderFeatures(String featureName, FeatureSource featureSource) {
        this.featureName = featureName;
        this.featureSource = featureSource;
    }

    public String getFeatureName() {
        return featureName;
    }

    public FeatureSource getFeatureSource() {
        return featureSource;
    }
}
