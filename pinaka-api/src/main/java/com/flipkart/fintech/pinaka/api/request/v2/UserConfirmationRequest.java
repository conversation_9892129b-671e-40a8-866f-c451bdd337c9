package com.flipkart.fintech.pinaka.api.request.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.UserAccount;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UserConfirmationRequest extends UserAccount {

    @NotNull
    @JsonProperty("accepted")
    boolean userAccepted;

    @NotNull
    @JsonProperty("product_type")
    ProductType productType;


    public boolean isUserAccepted() {
        return userAccepted;
    }

    public void setUserAccepted(boolean userAccepted) {
        this.userAccepted = userAccepted;
    }

    public ProductType getProductType() {
        return productType;
    }

    public void setProductType(ProductType productType) {
        this.productType = productType;
    }
}
