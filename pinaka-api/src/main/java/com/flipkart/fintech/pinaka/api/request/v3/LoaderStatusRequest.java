package com.flipkart.fintech.pinaka.api.request.v3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.LoaderContext;
import com.flipkart.fintech.pinaka.api.enums.ProductSubType;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.LowerCaseWithUnderscoresStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LoaderStatusRequest {
    private ProductType productType;
    private Channel channel;
    private String appVersion;
    private String merchantUserId;
    private String applicationId;
    private LoaderContext context;
    private ProductSubType productSubType;
    private String utmSource;
}
