package com.flipkart.fintech.pinaka.api.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 09/04/18.
 */
public enum ErrorCode {

    CIBIL_FAILED,
    ERROR_RESPONSE_FROM_CIBIL,
    PROCESSING_ERROR,
    CUSTOMER_RECORD_NOT_FOUND,
    UNKNOWN,
    PHONE_NUMBER_NOT_VERIFIED,
    NOT_ELIGIBLE_FOR_THE_PRODUCT,
    NOT_ELIGIBLE_FOR_REVALIDATION_JOURNEY;

    private static Map<String, ErrorCode> map = new HashMap<>();

    static {
        map.put("CIBIL_REQUEST_FAILED", C<PERSON>IL_FAILED);
        map.put("CIBIL_RESPONSE_FAILED", ERROR_RESPONSE_FROM_CIBIL);
        map.put("PROCESSING_ERROR", PROCESSING_ERROR);
        map.put("CUSTOMER_RECORD_NOT_FOUND", CUSTOMER_RECORD_NOT_FOUND);
    }

    public static ErrorCode getFromString(String code){
        if(null == code) return null;
        ErrorCode errorCode = map.get(code.toUpperCase());
        if(null == errorCode){
            errorCode = UNKNOWN;
        }
        return errorCode;
    }
}
