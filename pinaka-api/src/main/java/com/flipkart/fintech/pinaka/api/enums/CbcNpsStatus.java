package com.flipkart.fintech.pinaka.api.enums;

public enum CbcNpsStatus {

    //Triggered after 25 days of user drop off
    APPLICATION_IN_PROGRESS,
    IPA_REJECTION,
    FINAL_REJECTION,
    //Triggered After 10 days of APPROVED application
    APPROVED,
    //Triggered After 3 months of APPROVED application or 80 days after APPROVED NPS is triggered
    FIRST_POST_APPROVAL,
    //Triggered After 11 months of APPROVED application or 8 months after FIRST_POST_APPROVAL NPS is triggered
    SECOND_POST_APPROVAL,
    //Triggered After every 6 months SECOND_POST_APPROVAL onwards
    SUBSEQUENT_POST_APPROVAL
}
