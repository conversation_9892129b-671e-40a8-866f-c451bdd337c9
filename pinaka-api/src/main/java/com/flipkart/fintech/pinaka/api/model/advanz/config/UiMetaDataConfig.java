package com.flipkart.fintech.pinaka.api.model.advanz.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UiMetaDataConfig {

    private String title;
    private String nonUpgradedTitle;
    private String subtitle;
    private String nonUpgradedSubtitle;
    private String payLaterSubtitle;
    private String emiSubtitle;
    private String imageUrl;
    private String nonUpgradedImageUrl;
    private String logoUrl;
    private String knowMoreText;
    private String knowMoreLandingUrl;
    private String payLaterSubmitButtonText;
    private String payLaterSubmitButtonUrl;
    private String fsupSubmitButtonText;
    private String creditInfoTitle;
    private String creditInfoLateFeeText;
    private HowToUseInfoConfig howToUseInfo;
    private String taglinePayLater;
    private String taglinePartialKyc;
}
