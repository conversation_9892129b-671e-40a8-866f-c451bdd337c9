package com.flipkart.fintech.pinaka.api.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 02/11/17.
 */
public enum CommunicationType {
    PN("push_notification"),
    SMS("sms"),
    EMAIL("email"),
    IN_APP("in_app");

    private String value;

    private CommunicationType(String value){ this.value = value;}

    @JsonCreator
    public CommunicationType fromString(String value){
        return CommunicationType.valueOf(value);
    }
}
