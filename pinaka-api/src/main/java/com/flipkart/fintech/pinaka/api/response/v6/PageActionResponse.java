package com.flipkart.fintech.pinaka.api.response.v6;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.rome.datatypes.response.page.action.v1.calm.FormAutoSubmitResponseContext;
import flipkart.lego.api.entities.DataType;
import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ToString
public class PageActionResponse implements DataType {
    private Action action;
    private Boolean actionSuccess;
    private ErrorOperation error;
    private Params params;
    private FormAutoSubmitResponse formAutoSubmitResponse;

    public PageActionResponse(Action action, Boolean actionSuccess, ErrorOperation error,
        Params params) {
        this.action = action;
        this.actionSuccess = actionSuccess;
        this.error = error;
        this.params = params;
    }

}
