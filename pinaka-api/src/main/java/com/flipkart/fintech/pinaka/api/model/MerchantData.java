package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 18/08/17.
 */
public class MerchantData {

    @JsonProperty("txn_count")
    private int txnCount;

    @JsonProperty("highest_txn_value")
    private Float highestTxnValue;

    @JsonProperty("last_txn_date")
    private Date lastTxnDate;

    public int getTxnCount() {
        return txnCount;
    }

    public void setTxnCount(int txnCount) {
        this.txnCount = txnCount;
    }

    public Float getHighestTxnValue() {
        return highestTxnValue;
    }

    public void setHighestTxnValue(Float highestTxnValue) {
        this.highestTxnValue = highestTxnValue;
    }
}
