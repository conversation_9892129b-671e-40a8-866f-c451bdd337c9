package com.flipkart.fintech.pinaka.api.enums;

public enum AddressProof {
    AADHAAR_LETTER("Aad<PERSON>ar Letter"),
    PASSPORT("Passport"),
    DRIVING_LICENCE("Driving Licence"),
    VOTER_ID("Voter ID"),
    ELECTRICITY_BILL("Electricity Bill"),
    PHONE_BILL("Phone Bill - Landline or Mobile"),
    BANK_STATEMENT("Bank Statement"),
    RENT_AGREEMENT("Rent Agreement"),
    SALE_DEED("Sale Deed or Agreement");

    private final String addressProof;

    AddressProof(String addressProof) {
        this.addressProof = addressProof;
    }

    public String getAddressProof() {
        return addressProof;
    }

}
