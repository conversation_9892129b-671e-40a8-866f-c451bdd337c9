package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.CbcNpsStatus;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class JourneyData {
    private Lender lender;
    private String accountId;
    private ProductType productType;
    private ApplicationStatus applicationStatus;
    private String applicationReferenceId;
    private String cohortType;
    private String scheduledDate;

    // Note : Created separate status field as we can't determine all journeyStates for NPS with applicationStatuses
    // available as we need to differentiate REJECTION status before and after IPA approval which can't be determined by
    // applicationStatus and similarly need to differentiate APPROVED status at various stages
    private CbcNpsStatus cbcNpsStatus;
}
