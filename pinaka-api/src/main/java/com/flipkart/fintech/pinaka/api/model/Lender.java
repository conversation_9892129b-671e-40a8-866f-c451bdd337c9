package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.LenderState;
import com.flipkart.fintech.pinaka.api.enums.LenderType;

import java.sql.Timestamp;

/**
 * Represents the Lender Entity
 *
 * <AUTHOR>
 * @version 1.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Lender {
    @JsonProperty("lender_id")
    private String lenderId;
    
    @JsonProperty("lender_name")
    private String lenderName;
    
    @JsonProperty("lender_type")
    private LenderType lenderType;
    
    @JsonProperty("lender_state")
    private LenderState lenderState;

    @JsonProperty("stamp_created")
    private Timestamp stampCreated;

    @JsonProperty("stamp_modified")
    private Timestamp stampLastModified;
    
    public String getLenderId() {
        return lenderId;
    }
    
    public void setLenderId(String lenderId) {
        this.lenderId = lenderId;
    }
    
    public String getLenderName() {
        return lenderName;
    }
    
    public LenderType getLenderType() {
        return lenderType;
    }
    
    public void setLenderType(LenderType lenderType) {
        this.lenderType = lenderType;
    }
    
    public void setLenderName(String lenderName) {
        this.lenderName = lenderName;
    }
    
    public LenderState getLenderState() {
        return lenderState;
    }
    
    public void setLenderState(LenderState lenderState) {
        this.lenderState = lenderState;
    }

    public Timestamp getStampCreated() {
        return stampCreated;
    }

    public void setStampCreated(Timestamp stampCreated) {
        this.stampCreated = stampCreated;
    }

    public Timestamp getStampLastModified() {
        return stampLastModified;
    }

    public void setStampLastModified(Timestamp stampLastModified) {
        this.stampLastModified = stampLastModified;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Lender lender = (Lender) o;
        
        if (!getLenderId().equals(lender.getLenderId())) return false;
        if (!getLenderName().equals(lender.getLenderName())) return false;
        if (getLenderType() != lender.getLenderType()) return false;
        return getLenderState() == lender.getLenderState();
    }
    
    @Override
    public int hashCode() {
        int result = getLenderId().hashCode();
        result = 31 * result + getLenderName().hashCode();
        result = 31 * result + getLenderType().hashCode();
        result = 31 * result + getLenderState().hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "Lender{" +
                "lenderId='" + lenderId + '\'' +
                ", lenderName='" + lenderName + '\'' +
                ", lenderType=" + lenderType +
                ", lenderState=" + lenderState +
                ", stampCreated=" + stampCreated +
                ", stampLastModified=" + stampLastModified +
                '}';
    }
}
