package com.flipkart.fintech.pinaka.api.model.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.pinaka.api.enums.ProductSubType;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.enums.SubApplicationType;
import com.flipkart.fintech.pinaka.api.model.ProductContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * application data model for pinaka tenant in AMS
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ApplicationData {
    private ProductType                                 productType;
    private ProductContext                              productContext;
    private ProductSubType                              productSubType;
    private String                                      financialProvider;
    private String                                      externalUserId;
    private Tenant                                      tenant;
    private String                                      rejectReason;
    private String                                      externalReferenceId;
    private String                                      journeyContext;
    private String                                      kycMethod;
    private Map<SubApplicationType, SubApplicationData> subApplicationDataMap;
    private String upgradeLimitConsentId;
}
