package com.flipkart.ams;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.winterfell.client.WinterfellClientV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class WinterfellUtilsV2Test {

    @Mock
    private WinterfellClientV2 winterfellClientV2;

    @Mock
    private CreateApplicationRequest createApplicationRequest;

    @Mock
    private ResumeApplicationRequest resumeApplicationRequest;

    @Mock
    private ApplicationDataResponse applicationDataResponse;

    @Mock
    private MerchantUser merchantUser;

    private WinterfellUtilsV2 winterfellUtilsV2;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        winterfellUtilsV2 = new WinterfellUtilsV2(winterfellClientV2);
    }

    @Test
    void testConstructor() {
        // Verify that the constructor properly initializes the dependencies
        assertNotNull(winterfellUtilsV2);
        // We can't directly access private fields, but we can verify behavior through method calls
    }

    @Test
    void testCreateApplication_Success() throws Exception {
        // Arrange
        String externalUserId = "user123";
        String applicationType = "PERSONAL_LOAN";

        when(createApplicationRequest.getExternalUserId()).thenReturn(externalUserId);
        when(createApplicationRequest.getApplicationType()).thenReturn(applicationType);
        when(winterfellClientV2.createApplicationV2(eq(Tenant.CALM.name()), eq(createApplicationRequest)))
                .thenReturn(applicationDataResponse);

        // Act
        ApplicationDataResponse result = winterfellUtilsV2.createApplication(createApplicationRequest);

        // Assert
        assertNotNull(result);
        assertEquals(applicationDataResponse, result);
        verify(winterfellClientV2).createApplicationV2(Tenant.CALM.name(), createApplicationRequest);
    }

    @Test
    void testCreateApplication_ThrowsWinterfellServiceException() throws Exception {
        // Arrange
        String externalUserId = "user123";
        String applicationType = "PERSONAL_LOAN";
        String errorMessage = "Winterfell service error";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(createApplicationRequest.getExternalUserId()).thenReturn(externalUserId);
        when(createApplicationRequest.getApplicationType()).thenReturn(applicationType);
        when(winterfellClientV2.createApplicationV2(eq(Tenant.CALM.name()), eq(createApplicationRequest)))
                .thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.createApplication(createApplicationRequest)
        );

        assertEquals(errorMessage, exception.getMessage());
        verify(winterfellClientV2).createApplicationV2(Tenant.CALM.name(), createApplicationRequest);
    }

    @Test
    void testCreateApplication_WithNullExternalUserId() throws Exception {
        // Arrange
        String applicationType = "PERSONAL_LOAN";
        String errorMessage = "Null pointer exception";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(createApplicationRequest.getExternalUserId()).thenReturn(null);
        when(createApplicationRequest.getApplicationType()).thenReturn(applicationType);
        when(winterfellClientV2.createApplicationV2(eq(Tenant.CALM.name()), eq(createApplicationRequest)))
                .thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.createApplication(createApplicationRequest)
        );

        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void testCreateApplication_WithDifferentExceptionTypes() throws Exception {
        // Arrange
        String externalUserId = "user123";
        String applicationType = "PERSONAL_LOAN";
        IllegalArgumentException originalException = new IllegalArgumentException("Invalid argument");

        when(createApplicationRequest.getExternalUserId()).thenReturn(externalUserId);
        when(createApplicationRequest.getApplicationType()).thenReturn(applicationType);
        when(winterfellClientV2.createApplicationV2(eq(Tenant.CALM.name()), eq(createApplicationRequest)))
                .thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.createApplication(createApplicationRequest)
        );

        assertEquals("Invalid argument", exception.getMessage());
    }

    @Test
    void testResumeApplication_Success() throws Exception {
        // Arrange
        String applicationId = "app123";
        String merchantUserId = "merchant456";
        String smUserId = "sm789";

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        when(winterfellClientV2.resumeApplicationV2(
                eq(Tenant.CALM.name()),
                eq(applicationId),
                eq(merchantUserId),
                eq(smUserId),
                eq(resumeApplicationRequest)
        )).thenReturn(applicationDataResponse);

        // Act
        ApplicationDataResponse result = winterfellUtilsV2.resumeApplication(
                applicationId, merchantUser, resumeApplicationRequest);

        // Assert
        assertNotNull(result);
        assertEquals(applicationDataResponse, result);
        verify(winterfellClientV2).resumeApplicationV2(
                Tenant.CALM.name(),
                applicationId,
                merchantUserId,
                smUserId,
                resumeApplicationRequest
        );
    }

    @Test
    void testResumeApplication_ThrowsWinterfellServiceException() throws Exception {
        // Arrange
        String applicationId = "app123";
        String merchantUserId = "merchant456";
        String smUserId = "sm789";
        String errorMessage = "Resume application failed";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        when(winterfellClientV2.resumeApplicationV2(
                eq(Tenant.CALM.name()),
                eq(applicationId),
                eq(merchantUserId),
                eq(smUserId),
                eq(resumeApplicationRequest)
        )).thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.resumeApplication(applicationId, merchantUser, resumeApplicationRequest)
        );

        assertEquals(errorMessage, exception.getMessage());
        verify(winterfellClientV2).resumeApplicationV2(
                Tenant.CALM.name(),
                applicationId,
                merchantUserId,
                smUserId,
                resumeApplicationRequest
        );
    }

    @Test
    void testResumeApplication_WithNullApplicationId() throws Exception {
        // Arrange
        String merchantUserId = "merchant456";
        String smUserId = "sm789";
        String errorMessage = "Application ID cannot be null";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        when(winterfellClientV2.resumeApplicationV2(
                eq(Tenant.CALM.name()),
                isNull(),
                eq(merchantUserId),
                eq(smUserId),
                eq(resumeApplicationRequest)
        )).thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.resumeApplication(null, merchantUser, resumeApplicationRequest)
        );

        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void testResumeApplication_WithNullMerchantUser() throws Exception {
        // Arrange
        String applicationId = "app123";

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.resumeApplication(applicationId, null, resumeApplicationRequest)
        );

        // The actual code will throw NullPointerException which gets wrapped in WinterfellServiceException
        // The message will be null since NPE doesn't have a message
        assertNull(exception.getMessage());
    }

    @Test
    void testCreateApplication_WithNullRequest() throws Exception {
        // Arrange
        // Mock the winterfellClientV2 to throw an exception, which will trigger the logging code
        // that tries to access createApplicationRequest.getExternalUserId() and getApplicationType()
        RuntimeException originalException = new RuntimeException("Some error");
        when(winterfellClientV2.createApplicationV2(eq(Tenant.CALM.name()), isNull()))
                .thenThrow(originalException);

        // Act & Assert
        // The actual code will throw NullPointerException when trying to log the error
        // because it tries to access createApplicationRequest.getExternalUserId() and getApplicationType()
        assertThrows(
                NullPointerException.class,
                () -> winterfellUtilsV2.createApplication(null)
        );
    }

    @Test
    void testResumeApplication_WithNullRequest() throws Exception {
        // Arrange
        String applicationId = "app123";
        String merchantUserId = "merchant456";
        String smUserId = "sm789";
        String errorMessage = "Resume request cannot be null";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        when(winterfellClientV2.resumeApplicationV2(
                eq(Tenant.CALM.name()),
                eq(applicationId),
                eq(merchantUserId),
                eq(smUserId),
                isNull()
        )).thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.resumeApplication(applicationId, merchantUser, null)
        );

        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void testResumeApplication_WithEmptyApplicationId() throws Exception {
        // Arrange
        String applicationId = "";
        String merchantUserId = "merchant456";
        String smUserId = "sm789";
        String errorMessage = "Application ID cannot be empty";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(merchantUser.getMerchantUserId()).thenReturn(merchantUserId);
        when(merchantUser.getSmUserId()).thenReturn(smUserId);
        when(winterfellClientV2.resumeApplicationV2(
                eq(Tenant.CALM.name()),
                eq(applicationId),
                eq(merchantUserId),
                eq(smUserId),
                eq(resumeApplicationRequest)
        )).thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.resumeApplication(applicationId, merchantUser, resumeApplicationRequest)
        );

        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void testResumeApplication_WithNullMerchantUserIds() throws Exception {
        // Arrange
        String applicationId = "app123";
        String errorMessage = "Merchant user ID cannot be null";
        RuntimeException originalException = new RuntimeException(errorMessage);

        when(merchantUser.getMerchantUserId()).thenReturn(null);
        when(merchantUser.getSmUserId()).thenReturn(null);
        when(winterfellClientV2.resumeApplicationV2(
                eq(Tenant.CALM.name()),
                eq(applicationId),
                isNull(),
                isNull(),
                eq(resumeApplicationRequest)
        )).thenThrow(originalException);

        // Act & Assert
        WinterfellServiceException exception = assertThrows(
                WinterfellServiceException.class,
                () -> winterfellUtilsV2.resumeApplication(applicationId, merchantUser, resumeApplicationRequest)
        );

        assertEquals(errorMessage, exception.getMessage());
    }
}
