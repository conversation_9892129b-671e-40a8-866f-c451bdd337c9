package com.flipkart.ams;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.flipkart.fintech.citadel.api.models.ActiveApplicationResponse;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.AmsBridge;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ApplicationServiceImplTest {

    @Mock private WinterfellUtils winterfellUtils;
    @Mock private AmsBridge amsBridge;
    private ApplicationServiceImpl applicationService;

    private MerchantUser merchantUser;
    private ProductType productType;
    private ApplicationDataResponse applicationDataResponse;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        applicationService = new ApplicationServiceImpl(winterfellUtils, amsBridge);

        merchantUser = MerchantUser.getDefaultMerchantUserGivenMerchantAccountId("ACC1234");
        productType = ProductType.PERSONAL_LOAN;
    }

    @Test
    void testFindActiveApplicationWhenApplicationExistsAndNotExpired() throws PinakaException {
        applicationDataResponse = new ApplicationDataResponse();
        applicationDataResponse.setApplicationId("app123");
        when(winterfellUtils.findActiveApplicationId(merchantUser, productType)).thenReturn("app123");
        when(winterfellUtils.fetchApplicationData("app123", merchantUser)).thenReturn(applicationDataResponse);
        when(amsBridge.hasExpired(applicationDataResponse)).thenReturn(false);

        Optional<ApplicationDataResponse> result = applicationService.findActiveApplication(merchantUser, productType);

        assertTrue(result.isPresent());
        assertEquals("app123", result.get().getApplicationId());
        verify(winterfellUtils, times(0)).discardApplication(any(), anyString());
    }

    @Test
    void testFindActiveApplicationWhenApplicationDoesNotExist() throws PinakaException {
        when(winterfellUtils.findActiveApplicationId(merchantUser, productType)).thenReturn(null);

        Optional<ApplicationDataResponse> result = applicationService.findActiveApplication(merchantUser, productType);

        assertFalse(result.isPresent());
        verify(winterfellUtils, times(0)).discardApplication(any(), anyString());
    }

    @Test
    void testFindActiveApplicationWithLeadDiscard() throws PinakaException {
        applicationDataResponse = new ApplicationDataResponse();
        applicationDataResponse.setApplicationId("app123");
        when(winterfellUtils.findActiveApplicationId(merchantUser, productType)).thenReturn("app123");
        when(winterfellUtils.fetchApplicationData("app123", merchantUser)).thenReturn(applicationDataResponse);
        when(amsBridge.hasExpired(applicationDataResponse)).thenReturn(true);

        HashMap<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadId", "lead123");
        applicationDataResponse.setApplicationData(applicationData);

        Optional<ApplicationDataResponse> result = applicationService.findActiveApplication(merchantUser, productType);

        assertFalse(result.isPresent());
        verify(winterfellUtils, times(1)).discardApplication(merchantUser, "lead123");
        verify(winterfellUtils, times(1)).discardApplication(merchantUser, "app123");
    }


    @Test
    void shouldDeleteApplicationAndAllLeads() throws PinakaException {
        String applicationId = "APP345";
        MerchantUser user = MerchantUser.getMerchantUser("FLIPKART", "Acc133", "sm133");
        ActiveApplicationResponse activeApplicationResponse = new ActiveApplicationResponse();
        Map<String, List<String>> applicationList = new HashMap<>();
        applicationList.put("PERSONAL_LOAN", Arrays.asList("APP123", "APP345"));
        applicationList.put("LEAD", Arrays.asList("APP456", "APP567"));
        activeApplicationResponse.setApplicationList(applicationList);
        when(winterfellUtils.getActiveApplications(user, null)).thenReturn(activeApplicationResponse);
        applicationService.discardApplicationAndLeads(user, applicationId);
        verify(winterfellUtils, times(1)).getActiveApplications(user, null);
        verify(winterfellUtils, times(1)).discardApplication(user, applicationId);
        verify(winterfellUtils, times(1)).discardApplication(user, "APP456");
        verify(winterfellUtils, times(1)).discardApplication(user, "APP567");
        verify(winterfellUtils, times(0)).discardApplication(user, "APP123");
    }
}
