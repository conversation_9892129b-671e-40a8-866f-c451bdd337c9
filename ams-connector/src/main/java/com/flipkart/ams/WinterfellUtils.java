package com.flipkart.ams;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.citadel.api.models.ActiveApplicationResponse;
import com.flipkart.fintech.citadel.api.models.ActiveApplicationsResponse;
import com.flipkart.fintech.citadel.api.models.ApplicationStateResponse;
import com.flipkart.fintech.citadel.api.models.WorkflowStatesRequest;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.request.ApplicationResponse;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.MigrateApplicationInstancesRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.UpdatePartnerStateRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.winterfell.client.WintefellClientException;
import com.flipkart.fintech.winterfell.client.WinterfellClient;
import com.google.inject.Inject;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import lombok.CustomLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.flipkart.ams.WinterfellUtils.HystrixConfig.GROUP_KEY;
import static com.flipkart.ams.WinterfellUtils.HystrixConfig.THREAD_POOL_KEY;
import static com.netflix.hystrix.contrib.javanica.conf.HystrixPropertiesManager.*;

@CustomLog
public class WinterfellUtils {

    private final WinterfellClient winterfellClient;
    private final Tenant tenant;
    @Inject
    public WinterfellUtils(WinterfellClient winterfellClient) {
        this.winterfellClient = winterfellClient;
      this.tenant = Tenant.CALM;
    }

    @HystrixCommand(
            groupKey = GROUP_KEY,
            commandKey = "PL_CREATE_APPLICATION",
            threadPoolKey = THREAD_POOL_KEY,
            commandProperties = {
                    @HystrixProperty(name = EXECUTION_ISOLATION_STRATEGY, value = "THREAD"),
                    @HystrixProperty(name = CIRCUIT_BREAKER_ENABLED, value = "true"),
                    @HystrixProperty(name = EXECUTION_TIMEOUT_ENABLED, value = "true"),
                    @HystrixProperty(
                            name = EXECUTION_ISOLATION_THREAD_TIMEOUT_IN_MILLISECONDS,
                            value = "5000"),
                    @HystrixProperty(name = REQUEST_CACHE_ENABLED, value = "false")
            },
            threadPoolProperties = {
                    @HystrixProperty(name = CORE_SIZE, value = "100"),
                    @HystrixProperty(name = MAX_QUEUE_SIZE, value = "25"),
                    @HystrixProperty(name = QUEUE_SIZE_REJECTION_THRESHOLD, value = "20"),
                    @HystrixProperty(name = KEEP_ALIVE_TIME_MINUTES, value = "32")
            })
    public ApplicationResponse createApplication(CreateApplicationRequest createApplicationRequest) throws WinterfellServiceException {//TODO used
        try {
            return winterfellClient.createApplication(tenant.name(), createApplicationRequest);
        } catch (Exception e) {
            log.error("Error while creating application at Winterfell. userId: {}, applicationType: {}. error: {}",
                    createApplicationRequest.getExternalUserId(), createApplicationRequest.getApplicationType(),
                    e.getMessage(), e);
            throw new WinterfellServiceException(e.getMessage());
        }
    }

    @HystrixCommand(
            threadPoolKey = THREAD_POOL_KEY,
            groupKey = GROUP_KEY,
            commandKey = "PL_FETCH_APPLICATION_DATA"
    )
    public ApplicationDataResponse fetchApplicationData(String applicationId, MerchantUser merchantUser) throws WinterfellServiceException {
        try {
            return winterfellClient.getApplication(tenant.name(), applicationId, merchantUser.getMerchantUserId(), merchantUser.getSmUserId());
        } catch (Exception e) {
            log.error("Error while fetching application data at Winterfell. application: {}. account: {}. {}",
                    applicationId, merchantUser.getMerchantUserId(), e.getMessage(), e);
            throw new WinterfellServiceException(e.getMessage());
        }
    }

    @HystrixCommand(
            threadPoolKey = THREAD_POOL_KEY,
            groupKey = GROUP_KEY,
            commandKey = "PL_RESUME_APPLICATION"
    )
    public ApplicationResponse resumeApplication(String applicationId, MerchantUser merchantUser,
                                                 ResumeApplicationRequest resumeApplicationRequest) throws WinterfellServiceException {
        try {
            return winterfellClient.resumeApplication(tenant.name(), applicationId, merchantUser.getMerchantUserId(),
                    merchantUser.getSmUserId(), resumeApplicationRequest);
        } catch (Exception e) {
            log.error("Error while resuming application at Winterfell. {}", e.getMessage(), e);
            throw new WinterfellServiceException(e.getMessage());
        }
    }

    @HystrixCommand(
            threadPoolKey = THREAD_POOL_KEY,
            groupKey = GROUP_KEY,
            commandKey = "PL_FETCH_ACTIVE_APPLICATION"
    )
    public ActiveApplicationResponse getActiveApplications(MerchantUser merchantUser, String applicationType) throws PinakaException {
        try {
            return winterfellClient.getActiveApplications(tenant.name(), merchantUser.getMerchantUserId(),
                    merchantUser.getSmUserId(), applicationType);
        } catch (WintefellClientException e) {
            throw new PinakaException(e.getMessage());
        }
    }

    public ActiveApplicationsResponse getActiveApplicationsResponseV2(String smUserId, String productType) throws PinakaException {
        try {
            return winterfellClient.getActiveApplicationsResponseV2(tenant.name(), smUserId, productType);
        } catch (WintefellClientException e) {
            throw new PinakaException(e.getMessage());
        }
    }

    @Deprecated
    public String findActiveApplicationId(MerchantUser merchantUser, ProductType productType)
            throws PinakaException {
        ActiveApplicationResponse activeApplicationResponse = getActiveApplications(
                merchantUser, null);
        List<String> applicationTypes = ApplicationTypeUtils.getApplicationTypes(productType);
        for (String applicationType : applicationTypes) {
            List<String> applicationIds = activeApplicationResponse.getApplicationList().get(
                    applicationType);
            if (CollectionUtils.isNotEmpty(applicationIds)) {
                return applicationIds.get(0);
            }
        }
        return null;
    }


    @HystrixCommand(
            threadPoolKey = THREAD_POOL_KEY,
            groupKey = GROUP_KEY,
            commandKey = "PL_DISCARD_APPLICATION"
    )
    public void discardApplication(MerchantUser merchantUser, String applicationId) throws PinakaException {
        try {
            winterfellClient.discardApplication(tenant.name(), merchantUser.getMerchantUserId(),
                    merchantUser.getSmUserId(), applicationId);
            log.info("Discarded application {}", applicationId);
        } catch (WintefellClientException e) {
            throw new PinakaException(e.getMessage());
        }
    }


    public ApplicationStateResponse getApplications(WorkflowStatesRequest request) throws PinakaException {
        try {
            return winterfellClient.getApplications(tenant.name(), request);
        } catch (WintefellClientException e) {
            throw new PinakaException(e.getMessage());
        }
    }

    static class HystrixConfig {
        final static String GROUP_KEY = "WinterfellClient";
        final static String THREAD_POOL_KEY = "PL_WINTERFELL_API_POOL";
    }

    @HystrixCommand(
        groupKey = "WINTERFELL",
        commandKey = "FETCH_APPLICATION_DATA"
    )
    @ExceptionMetered
    @Timed
    public ApplicationDataResponse fetchApplicationData(String applicationReferenceId, String tenantId) throws PinakaException {
        ApplicationDataResponse applicationDataResponse;
        try {
            applicationDataResponse = winterfellClient.getApplication(tenantId, applicationReferenceId, getRequestContextThreadLocalUserId(tenantId), null);
        } catch (WintefellClientException e) {
            throw new PinakaException(e.getMessage());
        }
        if (applicationDataResponse == null || applicationDataResponse.getApplicationData() == null) {
            throw new PinakaException("No application data found for application");
        }
        return applicationDataResponse;
    }


    @HystrixCommand(
        groupKey = "WINTERFELL",
        commandKey = "UPDATE_PARTNER_STATE"
    )
    @ExceptionMetered
    @Timed
    public void updatePartnerState(
        UpdatePartnerStateRequest updatePartnerStateRequest) throws PinakaException {
        ApplicationResponse applicationDataResponse;
        try {
            applicationDataResponse = winterfellClient.updatePartnerState(
								String.valueOf(Tenant.CALM),updatePartnerStateRequest);
        } catch (Exception e) {
            throw new PinakaException(e.getMessage());
        }
    }

    public String getRequestContextThreadLocalUserId(String tenant) {
        if (Objects.nonNull(RequestContextThreadLocal.REQUEST_CONTEXT.get())) {
            String userId = RequestContextThreadLocal.REQUEST_CONTEXT.get().getHeader(Constants.X_EXTERNAL_USER_ID);
            if (StringUtils.isNotBlank(userId)) {
                return userId;
            }
            log.info("AccountId is null for tenant: {} and requestContext: {}", tenant,
                RequestContextThreadLocal.REQUEST_CONTEXT.get());
            return "";
        }
        log.info("RequestContext is null");
        return "";
    }

}
