package com.flipkart.ams;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.ImplementedBy;

/**
 * Enhanced version of ApplicationService with consistent response types across create, read, and
 * update operations.
 */
@ImplementedBy(ApplicationServiceV2Impl.class)
public interface ApplicationServiceV2 {

  /**
   * Enhanced version of resumeApplication that returns complete ApplicationDataResponse This
   * eliminates the need for a separate fetch after resuming an application
   *
   * @param merchantUser The merchant user
   * @param applicationId The application ID
   * @param resumeApplicationRequest The resume application request
   * @return The complete application data response
   * @throws PinakaException if there's an error
   */
  ApplicationDataResponse resumeApplication(
      MerchantUser merchantUser,
      String applicationId,
      ResumeApplicationRequest resumeApplicationRequest)
      throws PinakaException;

  /**
   * Enhanced version of createApplication that returns complete ApplicationDataResponse This
   * eliminates the need for a separate fetch after creating an application
   *
   * @param createApplicationRequest The create application request
   * @return The complete application data response
   * @throws PinakaException if there's an error
   */
  ApplicationDataResponse createApplication(CreateApplicationRequest createApplicationRequest)
      throws PinakaException;
}
