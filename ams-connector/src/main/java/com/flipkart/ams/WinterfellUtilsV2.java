package com.flipkart.ams;

import static com.flipkart.ams.WinterfellUtils.HystrixConfig.GROUP_KEY;
import static com.flipkart.ams.WinterfellUtils.HystrixConfig.THREAD_POOL_KEY;
import static com.netflix.hystrix.contrib.javanica.conf.HystrixPropertiesManager.*;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.winterfell.client.WinterfellClientV2;
import com.google.inject.Inject;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import lombok.CustomLog;

/**
 * Enhanced version of WinterfellUtils that provides consistent response types across create, read,
 * and update operations.
 */
@CustomLog
public class WinterfellUtilsV2 {

  private final WinterfellClientV2 winterfellClientV2;
  private final Tenant tenant;

  @Inject
  public WinterfellUtilsV2(WinterfellClientV2 winterfellClientV2) {
    this.winterfellClientV2 = winterfellClientV2;
    this.tenant = Tenant.CALM;
  }

  /**
   * Enhanced version of createApplication that returns complete ApplicationDataResponse This
   * eliminates the need for a separate fetch after creating an application
   */
  @HystrixCommand(
      groupKey = GROUP_KEY,
      commandKey = "PL_CREATE_APPLICATION_V2",
      threadPoolKey = THREAD_POOL_KEY,
      commandProperties = {
        @HystrixProperty(name = EXECUTION_ISOLATION_STRATEGY, value = "THREAD"),
        @HystrixProperty(name = CIRCUIT_BREAKER_ENABLED, value = "true"),
        @HystrixProperty(name = EXECUTION_TIMEOUT_ENABLED, value = "true"),
        @HystrixProperty(name = EXECUTION_ISOLATION_THREAD_TIMEOUT_IN_MILLISECONDS, value = "5000"),
        @HystrixProperty(name = REQUEST_CACHE_ENABLED, value = "false")
      },
      threadPoolProperties = {
        @HystrixProperty(name = CORE_SIZE, value = "100"),
        @HystrixProperty(name = MAX_QUEUE_SIZE, value = "25"),
        @HystrixProperty(name = QUEUE_SIZE_REJECTION_THRESHOLD, value = "20"),
        @HystrixProperty(name = KEEP_ALIVE_TIME_MINUTES, value = "32")
      })
  public ApplicationDataResponse createApplication(
      CreateApplicationRequest createApplicationRequest) throws WinterfellServiceException {
    try {
      return winterfellClientV2.createApplicationV2(tenant.name(), createApplicationRequest);
    } catch (Exception e) {
      log.error(
          "Error while creating application at Winterfell. userId: {}, applicationType: {}. error: {}",
          createApplicationRequest.getExternalUserId(),
          createApplicationRequest.getApplicationType(),
          e.getMessage(),
          e);
      throw new WinterfellServiceException(e.getMessage());
    }
  }

  /**
   * Enhanced version of resumeApplication that returns complete ApplicationDataResponse This
   * eliminates the need for a separate fetch after resuming an application
   */
  @HystrixCommand(
      threadPoolKey = THREAD_POOL_KEY,
      groupKey = GROUP_KEY,
      commandKey = "PL_RESUME_APPLICATION_V2")
  public ApplicationDataResponse resumeApplication(
      String applicationId,
      MerchantUser merchantUser,
      ResumeApplicationRequest resumeApplicationRequest)
      throws WinterfellServiceException {
    try {
      return winterfellClientV2.resumeApplicationV2(
          tenant.name(),
          applicationId,
          merchantUser.getMerchantUserId(),
          merchantUser.getSmUserId(),
          resumeApplicationRequest);
    } catch (Exception e) {
      log.error("Error while resuming application at Winterfell. {}", e.getMessage(), e);
      throw new WinterfellServiceException(e.getMessage());
    }
  }
}
