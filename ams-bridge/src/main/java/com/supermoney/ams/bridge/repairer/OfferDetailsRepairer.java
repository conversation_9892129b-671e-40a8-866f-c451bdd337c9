package com.supermoney.ams.bridge.repairer;

import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.common.collect.ImmutableMap;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.time.LocalDate;

import java.time.Instant;
import java.time.Month;
import java.time.ZoneId;

import static com.supermoney.ams.bridge.repairer.FormRepairerUtil.getFormSubmitRequest;
import static com.supermoney.ams.bridge.utils.PinakaConstants.PLConstants.FINANCIAL_PROVIDER;
import static com.supermoney.ams.bridge.utils.PinakaConstants.PLWorkflowVariable.GET_OFFER;
import static com.supermoney.ams.bridge.utils.PinakaConstants.SANDBOX_LENDERS_SET;

/**
 * <AUTHOR>
 * @date 20/06/24
 */
@CustomLog
public class OfferDetailsRepairer implements Repairer {
    @Override
    public boolean isRepairNeeded(ApplicationDataResponse response, RepairableFormInteractionConfig config) {
        if(config.isReadRepair()){
            try{
                LocalDate expiryDate = getExpiryTime(response);
                if(LocalDate.now().isAfter(expiryDate)){
                    return true;
                }
            } catch (Exception e){
                log.error(e.getMessage());
            }
        }
        return false;
    }

    private LocalDate getExpiryTime(ApplicationDataResponse response) {
        if(response.getApplicationData().containsKey(FINANCIAL_PROVIDER) && SANDBOX_LENDERS_SET.contains((
                String)response.getApplicationData().get(FINANCIAL_PROVIDER)) && response.getApplicationData().containsKey(GET_OFFER)){
            GetOfferResponse offerResponse = ObjectMapperUtil.get().convertValue(response.getApplicationData().get(GET_OFFER), GetOfferResponse.class);
            return Instant.ofEpochMilli(offerResponse.getGeneratedOffer().getOffer().getValidTill()).atZone(ZoneId.systemDefault()).toLocalDate();
        }
        return LocalDate.of(2100, Month.JANUARY, 1);
    }

    @Override
    public UserActionRequest getDummyUserActionRequest(ApplicationDataResponse response, RepairableFormInteractionConfig config) {
        FormSubmitRequest dummyFormSubmitRequest = getFormSubmitRequest(response);
        dummyFormSubmitRequest.setFormData(ImmutableMap.of("repair", true));
        return dummyFormSubmitRequest;
    }
}
