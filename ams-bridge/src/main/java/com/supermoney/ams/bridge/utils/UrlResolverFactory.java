package com.supermoney.ams.bridge.utils;

import com.flipkart.fintech.pandora.api.model.pl.sandbox.response.AccountAggregatorResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.SubmitOfferResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;

import com.supermoney.ams.bridge.models.interaction.LenderInteractionConfig;
import java.util.List;

/**
 * <AUTHOR>
 * @date 04/04/24
 */
public class UrlResolverFactory {
    public static String getUrlFromApplicationDataResponseAndTaskKeys(ApplicationDataResponse applicationDataResponse, List<String> keys,
        LenderInteractionConfig lenderInteractionConfig){

        if(applicationDataResponse.getApplicationData().containsKey(lenderInteractionConfig.getDataKey())){
            return (String) applicationDataResponse.getApplicationData().get(lenderInteractionConfig.getDataKey());
        }

        if("initAa".equals(lenderInteractionConfig.getKeys().get(0))) {
            AccountAggregatorResponse response = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData()
                    .get(lenderInteractionConfig.getKeys().get(0)), AccountAggregatorResponse.class);
            return response.getAaRedirectionUrl();
        }
        String redirectionUrl = "";
        //todo
        for(String taskKey: keys){
            if(taskKey.equals("lenderJourney") && applicationDataResponse.getApplicationData().containsKey("getLenderStatus")){
                GetApplicationStatusResponse res = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("getLenderStatus"),GetApplicationStatusResponse.class);
                return res.getJourneyState().getRedirectionUrl();
            } else if(taskKey.equals("createApplicationLender") && applicationDataResponse.getApplicationData().containsKey("createApplicationLender")){
                CreateApplicationResponse res = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("createApplicationLender"), CreateApplicationResponse.class);
                redirectionUrl =res.getJourneyState().getRedirectionUrl();
            } else if(taskKey.equals("submitOffer") && applicationDataResponse.getApplicationData().containsKey("submitOffer")){
                SubmitOfferResponse res = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("submitOffer"), SubmitOfferResponse.class);
                redirectionUrl =res.getJourneyState().getRedirectionUrl();
            } else if(taskKey.equals("initAa") && applicationDataResponse.getApplicationData().containsKey("initAa")){
                AccountAggregatorResponse res = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("initAa"), AccountAggregatorResponse.class);
                redirectionUrl = res.getAaRedirectionUrl();
            }
        }

        return redirectionUrl;
    }
}
