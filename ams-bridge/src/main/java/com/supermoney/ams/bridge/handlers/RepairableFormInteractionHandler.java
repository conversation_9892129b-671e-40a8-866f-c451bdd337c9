package com.supermoney.ams.bridge.handlers;


import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.repairer.FormRepairerUtil;

public class RepairableFormInteractionHandler extends FormInteractionHandler {

    public RepairableFormInteractionHandler(RepairableFormInteractionConfig interactionConfig) {
        super(interactionConfig);
    }

    @Override
    public boolean checkReadRepair(ApplicationDataResponse applicationDataResponse) {
        return FormRepairerUtil.isRepairNeeded(applicationDataResponse, (RepairableFormInteractionConfig) interactionConfig);
    }

    @Override
    public boolean checkUserDiscardAllowed(ApplicationDataResponse applicationDataResponse) {
        return interactionConfig.isUserDiscardAllowed();
    }

    @Override
    public boolean isTerminalState(ApplicationDataResponse applicationDataResponse) {
        return interactionConfig.isTerminalState();
    }

    @Override
    public UserActionRequest getDummyUserActionRepairRequest(ApplicationDataResponse applicationDataResponse) {
        return FormRepairerUtil.getDummyUserActionRequest(applicationDataResponse, (RepairableFormInteractionConfig) interactionConfig);
    }
}
