package com.supermoney.ams.bridge;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.ImplementedBy;
import com.supermoney.ams.bridge.models.ApplicationState;

import java.util.Map;

@ImplementedBy(AmsBridgeImpl.class)
public interface AmsBridge {
  ResumeApplicationRequest getResumeRequest(UserActionRequest userActionRequest, ApplicationDataResponse applicationDataResponse);
  PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser);
  boolean isSameInteraction(ApplicationState state, ApplicationState applicationState);
  String getInteractionKey(ApplicationState state);
  boolean isResumable(UserActionRequest userActionRequest, ApplicationDataResponse applicationDataResponse);
  boolean isUserDiscardAllowed(ApplicationDataResponse applicationDataResponse);

  boolean isTerminalState(ApplicationDataResponse applicationDataResponse);

  boolean checkReadRepair(ApplicationDataResponse applicationDataResponse);
  ResumeApplicationRequest getRepairRequest(ApplicationDataResponse applicationDataResponse);
  default ResumeApplicationRequest getRepairRequest(ApplicationDataResponse applicationDataResponse, Map<String,String> additionalParams){
    return getRepairRequest(applicationDataResponse);
  };
  boolean hasExpired(ApplicationDataResponse applicationDataResponse);
}
