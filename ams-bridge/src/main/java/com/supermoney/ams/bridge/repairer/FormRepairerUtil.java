package com.supermoney.ams.bridge.repairer;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;

public class FormRepairerUtil {

    public static UserActionRequest getDummyUserActionRequest(ApplicationDataResponse applicationDataResponse,
                                                              RepairableFormInteractionConfig interactionConfig) {
        return RepairerFactory.getRepairer(ApplicationState.create(applicationDataResponse))
                .getDummyUserActionRequest(applicationDataResponse, interactionConfig);
    }

    public static boolean isRepairNeeded(ApplicationDataResponse applicationDataResponse,
                                         RepairableFormInteractionConfig interactionConfig) {
        return RepairerFactory.getRepairer(ApplicationState.create(applicationDataResponse))
                .isRepairNeeded(applicationDataResponse, interactionConfig);
    }

    public static FormSubmitRequest getFormSubmitRequest(ApplicationDataResponse response) {
        FormSubmitRequest dummyFormSubmitRequest = new FormSubmitRequest();
        dummyFormSubmitRequest.setType(UserRequestActionType.FORM);
        dummyFormSubmitRequest.setAccountId(response.getExternalUserId());
        dummyFormSubmitRequest.setSmUserId(response.getSmUserId());
        dummyFormSubmitRequest.setApplicationId(response.getApplicationId());
        dummyFormSubmitRequest.setProcessInstance(response.getPendingTask().get(0).getProcessInstanceId());
        dummyFormSubmitRequest.setTaskKey(response.getPendingTask().get(0).getTaskKey());
        dummyFormSubmitRequest.setTaskId(response.getPendingTask().get(0).getTaskId());
        return dummyFormSubmitRequest;
    }
}
