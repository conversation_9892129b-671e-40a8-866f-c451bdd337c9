package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.DocumentSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.VkycSubmitRequest;
import com.google.inject.Inject;
import org.apache.commons.lang.NotImplementedException;

public class DataBuilderFactory {

  private final PlClient plClient;

  @Inject
  public DataBuilderFactory(PlClient plClient) {
    this.plClient = plClient;
  }

  public DataBuilder get(UserActionRequest userActionRequest) {
    switch (userActionRequest.getType()) {
      case FORM:
        return new FormDataBuilder((FormSubmitRequest) userActionRequest);
      case MANDATE:
        return new MandateDataBuilder((MandateSubmitRequest) userActionRequest, plClient);
      case DOCUMENT:
        return new DocumentDataBuilder((DocumentSubmitRequest) userActionRequest);
      case VKYC:
        return new VkycDataBuilder((VkycSubmitRequest) userActionRequest);
    }
    throw new NotImplementedException();
  }
}
