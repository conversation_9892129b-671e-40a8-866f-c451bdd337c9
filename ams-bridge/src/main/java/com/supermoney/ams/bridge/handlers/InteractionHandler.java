package com.supermoney.ams.bridge.handlers;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;

import java.util.Map;

public interface InteractionHandler {
  PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser);
  default boolean checkReadRepair(ApplicationDataResponse applicationDataResponse){
    return false;
  }
  default UserActionRequest getDummyUserActionRepairRequest(ApplicationDataResponse applicationDataResponse) {
    return null;
  }
  default UserActionRequest getDummyUserActionRepairRequest(ApplicationDataResponse applicationDataResponse, Map<String,String> additionalParams) {
    return getDummyUserActionRepairRequest(applicationDataResponse);
  }

  default boolean checkUserDiscardAllowed(ApplicationDataResponse applicationDataResponse){
    return false;
  }

  default boolean isTerminalState(ApplicationDataResponse applicationDataResponse){
    return false;
  }
}
