package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.DocumentSubmitRequest;
import java.util.HashMap;
import java.util.Map;

public class DocumentDataBuilder implements DataBuilder {

  private final DocumentSubmitRequest documentSubmitRequest;

  public DocumentDataBuilder(DocumentSubmitRequest documentSubmitRequest) {
    this.documentSubmitRequest = documentSubmitRequest;
  }

  @Override
  public Map<String, Object> build() {
    HashMap<String, Object> data = new HashMap<>();
    data.put("documentId", documentSubmitRequest.getDocumentId());
    data.put("documentMeta", documentSubmitRequest.getDocumentMeta());
    return data;
  }
}
