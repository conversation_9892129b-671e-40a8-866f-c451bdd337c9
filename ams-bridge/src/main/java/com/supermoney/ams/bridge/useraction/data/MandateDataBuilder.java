package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pandora.api.model.idfc.EmandateLenderRequest;
import com.flipkart.fintech.pandora.api.model.idfc.EmandateLenderResponse;
import com.flipkart.fintech.pandora.api.model.idfc.EmandatePayload;
import com.flipkart.fintech.pandora.api.model.idfc.EmandateResponse;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;

public class MandateDataBuilder implements DataBuilder {
    private final MandateSubmitRequest mandateSubmitRequest;
    private final PlClient plClient;


    public MandateDataBuilder(MandateSubmitRequest mandateSubmitRequest, PlClient plClient) {
        this.mandateSubmitRequest = mandateSubmitRequest;
        this.plClient = plClient;
    }

    @Override
    public Map<String, Object> build() {
        Map<String, Object> data = new HashMap<>();
        data.put("mandateResponseString", mandateSubmitRequest.getMandateResponseString());
        return data;
    }

    @Override
    public Map<String, Object> readRepairData(ApplicationDataResponse applicationDataResponse){
        String txnId = getTransactionId(applicationDataResponse);
        Map<String, Object> data = new HashMap<>();
        data.put("mandateId", txnId);
        return data;
    }

    private String getTransactionId(ApplicationDataResponse applicationDataResponse) {
        if(applicationDataResponse.getApplicationData().containsKey("generateEmandateUrl")) {
            EmandateResponse emandateResponse = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("generateEmandateUrl"), EmandateResponse.class);
            EmandatePayload emandatePayload = emandateResponse.getPayload();
            if(emandatePayload.getProperties().containsKey("DATA")) {
                String emandateData = emandatePayload.getProperties().get("DATA");
                EmandateLenderRequest emandateLenderRequest = new EmandateLenderRequest();
                emandateLenderRequest.setResponse(emandateData);
                EmandateLenderResponse emandateLenderResponse = plClient.decryptEmanadateUrl("idfc", emandateLenderRequest);
                if(emandateLenderResponse.getProperties().containsKey("MERCHENTTXNID")) {
                    return emandateLenderResponse.getProperties().get("MERCHENTTXNID");
                }
            }
        }
        return null;
    }

}
