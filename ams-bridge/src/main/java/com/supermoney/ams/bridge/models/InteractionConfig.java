package com.supermoney.ams.bridge.models;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.supermoney.ams.bridge.models.interaction.DocumentInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.FormInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.LenderInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.MandateInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.VkycInteractionConfig;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(name = "FORM_INTERACTION", value = FormInteractionConfig.class),
    @JsonSubTypes.Type(name = "REPAIRABLE_FORM_INTERACTION", value = RepairableFormInteractionConfig.class),
    @JsonSubTypes.Type(name = "MANDATE_INTERACTION", value = MandateInteractionConfig.class),
    @JsonSubTypes.Type(name = "DOCUMENT_INTERACTION", value = DocumentInteractionConfig.class),
    @JsonSubTypes.Type(name = "VKYC_INTERACTION", value = VkycInteractionConfig.class),
    @JsonSubTypes.Type(name = "LENDER_INTERACTION", value = LenderInteractionConfig.class),
})
public abstract class InteractionConfig {
  private JsonNode schema;

  private JsonNode sample;
}
