package com.supermoney.ams.bridge.utils;

import com.supermoney.ams.bridge.models.LifecycleConfig;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang.time.DateUtils;

public class TimeUtil {

  public static Date addTime(Date date, LifecycleConfig lifecycle) {
    TimeUnit timeUnit = lifecycle.getTimeUnit();
    int timeValue = lifecycle.getTimeValue();
    switch (timeUnit) {
      case DAYS:
        return DateUtils.addDays(date, timeValue);
      case HOURS:
        return DateUtils.addHours(date, timeValue);
      case SECONDS:
        return DateUtils.addSeconds(date, timeValue);
      case MINUTES:
        return DateUtils.addMinutes(date, timeValue);
      default:
        throw new IllegalArgumentException(String.format("Invalid timeUnit: %s", timeUnit));
    }
  }
}
