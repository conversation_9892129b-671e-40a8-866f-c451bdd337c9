package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static com.supermoney.ams.bridge.utils.FileUtils.readType;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class MandateDataBuilderTest {
    @Mock
    PlClient plClient;

    @Before
    public void setUp() throws Exception{
    }
    @Test
    public void successResponse() {
        MandateSubmitRequest mandateSubmitRequest = readMandateSubmitRequest("ams/mandate_response_success.json");
        Assert.assertTrue(mandateSubmitRequest.getMandateResponseString().contains("|"));
        Assert.assertTrue(mandateSubmitRequest.getMandateResponseString().contains("="));
        Map<String, Object> data = new MandateDataBuilder(mandateSubmitRequest, plClient).build();
        assertEquals(1, data.size());
    }

    @Test
    public void failureResponse() {
        MandateSubmitRequest mandateSubmitRequest = readMandateSubmitRequest("ams/mandate_response_failure.json");
        Assert.assertTrue(mandateSubmitRequest.getMandateResponseString().contains("|"));
        Assert.assertTrue(mandateSubmitRequest.getMandateResponseString().contains("="));
        Map<String, Object> data = new MandateDataBuilder(mandateSubmitRequest, plClient).build();
        assertEquals(1, data.size());
    }

    private MandateSubmitRequest readMandateSubmitRequest(String fileName) {
        return readType(fileName, MandateSubmitRequest.class);
    }

}
