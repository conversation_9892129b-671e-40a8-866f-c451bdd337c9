package com.supermoney.ams.bridge.repairer;

import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class OfferDetailsRepairerTest {

    private ApplicationDataResponse applicationDataResponse;
    private OfferDetailsRepairer offerDetailsRepairer;
    private RepairableFormInteractionConfig repairableFormInteractionConfig;

    @BeforeEach
    void setUp() {
        InputStream applicationData = OfferDetailsRepairerTest.class.getClassLoader().
                getResourceAsStream("ams/repairableOfferSandboxApplicationDataResponse.json");
        assert applicationData != null;
        try {
            applicationDataResponse = ObjectMapperUtil.get().readValue(applicationData,ApplicationDataResponse.class);

        } catch (IOException e) {
            e.printStackTrace();
        }
        offerDetailsRepairer = new OfferDetailsRepairer();
    }

    @Test
    void isRepairNeededPositiveTest() {
        repairableFormInteractionConfig = Mockito.mock(RepairableFormInteractionConfig.class);
        when(repairableFormInteractionConfig.isReadRepair()).thenReturn(false);
        assertFalse(offerDetailsRepairer.isRepairNeeded(applicationDataResponse, repairableFormInteractionConfig));
    }


}