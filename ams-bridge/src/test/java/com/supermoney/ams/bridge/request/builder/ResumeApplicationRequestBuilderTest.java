package com.supermoney.ams.bridge.request.builder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.fintech.pandora.client.PandoraClientConfiguration;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import com.supermoney.ams.bridge.helpers.URLHelper;
import com.supermoney.ams.bridge.models.InteractionConfig;
import com.supermoney.ams.bridge.models.MerchantJourneyConfig;
import com.supermoney.ams.bridge.useraction.data.DataBuilderFactory;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.validator.DataValidator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static com.supermoney.ams.bridge.utils.FileUtils.readType;
import static com.supermoney.ams.bridge.utils.FileUtils.readTypeFromTypeReference;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class ResumeApplicationRequestBuilderTest {

    private ResumeApplicationRequestBuilder resumeApplicationRequestBuilder;
    @Mock
    PlClient plClient;


    @Before
    public void setUp() throws Exception {
        Map<String, MerchantJourneyConfig> merchantJourneyUrlConfigMap = readMerchantJourneyUrlConfig(Files.MERCHANT_JOURNEY_URL_CONFIG);
        resumeApplicationRequestBuilder = new ResumeApplicationRequestBuilder(new DataBuilderFactory(plClient), new DataValidator(),
                new URLHelper(merchantJourneyUrlConfigMap));
    }

    @Test
    public void correctFormSubmitRequest() throws JsonProcessingException {
        FormSubmitRequest submitRequest = readFormSubmitRequest("ams/form1.json");
        ApplicationDataResponse applicationDataResponse = readApplicationDataResponse(Files.AMS_APPLICATION_1_JSON);
        InteractionConfig interactionConfig = readInteractionConfig(Files.FORM_INTERACTION_CONFIG_1);
        ResumeApplicationRequest resumeApplicationRequest = resumeApplicationRequestBuilder.build(submitRequest, applicationDataResponse, interactionConfig);
        isCorrect(resumeApplicationRequest, "ams/resume_application_request1.json");
    }

    @Test(expected = InvalidPayloadException.class)
    public void incorrectFormSubmitRequest() {
        FormSubmitRequest formSubmitRequest = readFormSubmitRequest("ams/form1_invalid.json");
        ApplicationDataResponse applicationDataResponse = readApplicationDataResponse(Files.AMS_APPLICATION_1_JSON);
        InteractionConfig interactionConfig = readInteractionConfig(Files.FORM_INTERACTION_CONFIG_1);
        resumeApplicationRequestBuilder.build(formSubmitRequest, applicationDataResponse, interactionConfig);
    }

    private void isCorrect(ResumeApplicationRequest resumeApplicationRequest, String fileName)
            throws JsonProcessingException {
        String actual = ObjectMapperUtil.get().writeValueAsString(resumeApplicationRequest);
        String expected = readResumeRequest(fileName);
        assertEquals(expected, actual);
    }

    private String readResumeRequest(String fileName) {
        Class<JsonNode> type = JsonNode.class;
        JsonNode jsonNode = readType(fileName, type);
        try {
            return ObjectMapperUtil.get().writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private FormSubmitRequest readFormSubmitRequest(String fileName) {
        Class<FormSubmitRequest> type = FormSubmitRequest.class;
        return readType(fileName, type);
    }

    private ApplicationDataResponse readApplicationDataResponse(String fileName) {
        Class<ApplicationDataResponse> type = ApplicationDataResponse.class;
        return readType(fileName, type);
    }

    private InteractionConfig readInteractionConfig(String fileName) {
        Class<InteractionConfig> type = InteractionConfig.class;
        return readType(fileName, type);
    }

    private Map<String, MerchantJourneyConfig> readMerchantJourneyUrlConfig(String fileName) {
        TypeReference<Map<String, MerchantJourneyConfig>> type = new TypeReference<Map<String, MerchantJourneyConfig>>() {
        };
        return readTypeFromTypeReference(fileName, type);
    }

    static class Files {
        public static final String FORM_INTERACTION_CONFIG_1 = "ams/form_interaction_config1.json";
        public static final String AMS_APPLICATION_1_JSON = "ams/application1.json";
        public static final String MERCHANT_JOURNEY_URL_CONFIG = "ams/merchant_journey_url_config.json";
    }

}